import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from '../services/user.service';
import { ConfigService } from '@nestjs/config';
import { Mapper } from '@automapper/core';
import axios from 'axios';
import { UserResponseFromApiDto } from '../dto/user-response-from-api.dto';
import { UserSuccessResponseDto } from '../dto/user-success-response.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { ProjectService } from '../../studio/project/services/project.service';

jest.mock('axios');
const mockAxios = axios as jest.Mocked<typeof axios>;

describe('UserService', () => {
  let service: UserService;
  let mockOrganizationService: jest.Mocked<OrganizationService>;
  let mockProjectService: jest.Mocked<ProjectService>;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockMapper: Mapper;

  beforeEach(async () => {
    mockConfigService = {
      get: jest.fn().mockReturnValue('http://example.com'),
    } as unknown as any;

    mockMapper = {
      map: jest.fn().mockImplementation((source) => ({ ...source })),
    } as unknown as Mapper;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: 'automapper:nestjs:default', useValue: mockMapper },
        {
          provide: OrganizationService,
          useValue: {
            findAllByUserId: jest
              .fn()
              .mockResolvedValue({ items: [], total: 0 }),
          },
        },
        {
          provide: ProjectService,
          useValue: {
            getEditorProjects: jest
              .fn()
              .mockResolvedValue({ items: [{}], total: 1 }),
          },
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    mockOrganizationService = module.get(OrganizationService);
    mockProjectService = module.get(ProjectService);
  });

  it('should return user data with scoring, studio access, and authorities', async () => {
    const authorization = 'Bearer token';

    const mockApiResponse = {
      data: {
        result: {
          id: '1',
          displayName: 'Test User',
          photo: 'http://example.com/photo.jpg',
          email: '<EMAIL>',
          scoringEnabled: false,
          studioEnabled: true,
        },
      },
      status: 200,
    };

    const mockPermissionsResponse = {
      data: {
        result: {
          authorities: [
            { id: 1, authority: 'ROLE_USER' },
            { id: 2, authority: 'ROLE_ADMIN' },
            { id: 3, authority: 'ROLE_EDITOR' },
          ],
        },
      },
    };

    mockAxios.get
      .mockResolvedValueOnce(mockApiResponse)
      .mockResolvedValueOnce(mockPermissionsResponse);

    const result = await service.getUser(authorization);

    expect(mockAxios.get).toHaveBeenCalledWith(
      `${mockConfigService.get('baseVidMobApiUrl')}/api/v1/user`,
      {
        headers: { Authorization: authorization },
      },
    );
    expect(mockAxios.get).toHaveBeenCalledWith(
      `${mockConfigService.get('baseVidMobApiUrl')}/api/v2/user/1/permission`,
      {
        headers: { Authorization: authorization },
      },
    );
    expect(mockMapper.map).toHaveBeenCalledWith(
      expect.objectContaining({
        id: '1',
        displayName: 'Test User',
        photo: 'http://example.com/photo.jpg',
        email: '<EMAIL>',
        scoringEnabled: false,
        studioEnabled: true,
        authorities: ['ROLE_USER', 'ROLE_ADMIN', 'ROLE_EDITOR'],
      }),
      UserResponseFromApiDto,
      UserSuccessResponseDto,
    );
    expect(result).toEqual(
      expect.objectContaining({
        responseData: expect.objectContaining({
          id: '1',
          displayName: 'Test User',
          photo: 'http://example.com/photo.jpg',
          email: '<EMAIL>',
          scoringEnabled: false,
          studioEnabled: true,
          authorities: ['ROLE_USER', 'ROLE_ADMIN', 'ROLE_EDITOR'],
        }),
        statusCode: 200,
      }),
    );
  });

  it('should handle axios error correctly', async () => {
    const authorization = 'Bearer token';
    const mockError = {
      response: {
        data: { error: 'Error occurred' },
        status: 403,
      },
    };
    mockAxios.get.mockRejectedValue(mockError);

    const result = await service.getUser(authorization);

    expect(result.responseData).toEqual(mockError.response.data);
    expect(result.statusCode).toEqual(403);
  });
});
