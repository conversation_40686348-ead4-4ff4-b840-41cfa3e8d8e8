import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { UserResponseFromApiDto } from '../dto/user-response-from-api.dto';
import { UserSuccessResponseDto } from '../dto/user-success-response.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { ProjectService } from '../../studio/project/services/project.service';

@Injectable()
export class UserService {
  baseApiUser: string;

  constructor(
    private configService: ConfigService,
    private readonly organizationService: OrganizationService,
    private readonly projectService: ProjectService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.baseApiUser = this.configService.get<string>('baseVidMobApiUrl', '');
  }

  private async getProjects(authorization: string) {
    try {
      const projects = await this.projectService.getEditorProjects(
        authorization,
        { perPage: 1 },
      );
      return projects['items'];
    } catch (error) {
      console.log('Error getting projects', error.response.data);
      return [];
    }
  }

  async getUser(authorization: string) {
    const getUserEndpoint = `${this.baseApiUser}/api/v1/user`;

    try {
      const userResponse = await axios.get(getUserEndpoint, {
        headers: {
          Authorization: authorization,
        },
      });

      const userId = userResponse.data.result.id;

      const [projects, organizations, permissionsResponse] = await Promise.all([
        this.getProjects(authorization),
        this.organizationService.findAllByUserId(userId, '', { perPage: 1 }),
        axios.get(`${this.baseApiUser}/api/v2/user/${userId}/permission`, {
          headers: {
            Authorization: authorization,
          },
        }),
      ]);

      const scoringEnabled = organizations.items.length > 0;
      const studioEnabled = projects.length > 0;
      const authorities = permissionsResponse.data.result.authorities.map(
        (auth: any) => auth.authority,
      );

      const userResponseData = {
        ...userResponse.data.result,
        scoringEnabled,
        studioEnabled,
        authorities,
      };

      const user = this.classMapper.map(
        userResponseData,
        UserResponseFromApiDto,
        UserSuccessResponseDto,
      );

      return {
        responseData: user,
        statusCode: userResponse.status,
      };
    } catch (error) {
      const status =
        error.response && error.response.status ? error.response.status : 500;
      const message =
        error.response && error.response.data
          ? error.response.data
          : { message: 'Internal server error' };
      return {
        responseData: message,
        statusCode: status,
      };
    }
  }
}
