import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { Injectable } from '@nestjs/common';
import { UserResponseFromApiDto } from '../dto/user-response-from-api.dto'; // Import your source DTO
import { UserSuccessResponseDto } from '../dto/user-success-response.dto';

@Injectable()
export class UserProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        UserResponseFromApiDto, // Source DTO
        UserSuccessResponseDto, // Destination DTO
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.id.toString()),
        ),
        forMember(
          (dest) => dest.displayName,
          mapFrom((src) => src.displayName),
        ),
        forMember(
          (dest) => dest.photo,
          mapFrom((src) => src.photo),
        ),

        forMember(
          (dest) => dest.email,
          mapFrom((src) => src.email),
        ),
        forMember(
          (dest) => dest.scoringEnabled,
          mapFrom((src) => src.scoringEnabled),
        ),
        forMember(
          (dest) => dest.studioEnabled,
          mapFrom((src) => src.studioEnabled),
        ),
        forMember(
          (dest) => dest.authorities,
          mapFrom((src) => src.authorities),
        ),
      );
    };
  }
}
