import { Controller, Get, Request, Res } from '@nestjs/common';
import {
  ApiOperation,
  ApiTags,
  ApiResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { UserService } from '../services/user.service';
import { Response } from 'express';
import { UserSuccessResponseDto } from '../dto/user-success-response.dto';

@ApiTags('Plugin User')
@ApiSecurity('Bearer Token')
@Controller('plugin/user')
export class UserController {
  constructor(private userService: UserService) {}

  @Get()
  @ApiOperation({ summary: 'Get user details' })
  @ApiResponse({
    status: 200,
    description: 'The user details have been successfully retrieved.',
    type: UserSuccessResponseDto,
  })
  async getUser(@Request() req: any, @Res() res: Response) {
    const authorization = req.headers.authorization;

    const response = await this.userService.getUser(authorization);

    return res.status(response.statusCode).json(response.responseData);
  }
}
