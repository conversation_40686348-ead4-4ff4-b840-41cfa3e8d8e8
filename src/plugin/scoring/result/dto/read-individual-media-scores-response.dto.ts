interface ParametersDto {
  [key: string]: any;
}

interface CustomValueDto {
  id: string;
  value: string;
  criteriaId: number;
  dateCreated: string;
  lastUpdated: string;
}

export interface ScoreDto {
  id: string;
  identifier: string;
  parameters: ParametersDto;
  rule: string;
  description: string | null;
  name: string | null;
  platformIdentifier: string;
  result: string;
  isOptional: number;
  isCustom: number;
  customValues: CustomValueDto[];
  isBestPractice: boolean;
  category: string;
  mediaTypes: string[];
  isGlobal: boolean;
  customIconUrl: string | null;
}
