export enum MediaProcessingState {
  PENDING = 'PENDING', // file upload (from app) or download (from cloud repository) has not begun
  ACCUMULATING = 'ACCUMULATING', // file data is being actively accumulated from source
  ARCHIVING = 'ARCHIVING', // file is being transferred from VidMob to S3
  COMPLETE = 'COMPLETE', // file is in S3 and ready to view
  FAILED = 'FAILED', // file data gathering failed and will not be retried (for example, remote authentication failed)
}
