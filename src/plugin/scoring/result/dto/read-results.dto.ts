import { AutoMap } from '@automapper/classes';
import { MediaProcessingState } from './media-processing-state.dto';

export class ReadResultsDto {
  /** Unique identifier for the scorecard.
   * @example '12345'
   */
  @AutoMap()
  scorecardId: string;

  @AutoMap()
  mediaId: number;

  /** deviceIdentifier of the media.
   * @example 'Adobe After Effects'
   */
  @AutoMap()
  deviceIdentifier: string;

  /** Name of the media.
   * @example 'Sample Video'
   */
  @AutoMap()
  name: string;

  /** Percentage score of the media.
   * @example 85
   */
  @AutoMap()
  scorePercentage: number; // Renamed 'Score%' to 'scorePercentage' for better code readability.

  /** Platform where the media is located or used.
   * @example 'YouTube'
   */
  @AutoMap()
  channel: string;

  /** Date when the media was created or uploaded.
   * @example '2023-01-15T08:00:00Z'
   */
  @AutoMap()
  dateCreated: string;

  /** MIME type of the media.
   * @example 'video/mp4'
   */
  @AutoMap()
  mimeType: string;

  /** Version of the media.
   * @example '1.0'
   */
  @AutoMap()
  version: string;

  @AutoMap()
  scorecardStatus: string;

  @AutoMap()
  mediaRecognitionStatus: string;

  @AutoMap()
  mediaUrl?: string;

  @AutoMap()
  mediaProcessingState: MediaProcessingState;

  @AutoMap()
  isOutdated: boolean;
}
