import { AutoMap } from '@automapper/classes';
import { IsUUID } from 'class-validator';
import { MediaProcessingState } from './media-processing-state.dto';

export interface MediaLifecycleCount {
  [currentMediaId: string]: number;
}

export type batchTypes = 'IN_FLIGHT' | 'PRE_FLIGHT' | 'PLUGIN_MEDIA';

export type batchStatus =
  | 'SETUP'
  | 'PROCESSING'
  | 'COMPLETE'
  | 'DELETE'
  | 'ERROR'
  | 'VERSIONED';

export enum Platform {
  ALL_PLATFORMS = 'ALL_PLATFORMS',
  TWITTER = 'TWITTER',
  FACEBOOK = 'FACEBOOK',
  TIKTOK = 'TIKTOK',
  PINTEREST = 'PINTEREST',
  LINKEDIN = 'LINKEDIN',
  DV360 = 'DV360',
  ADWORDS = 'ADWORDS',
  VERIZONNATIVE = 'VERIZONNATIVE',
  SNAPCHAT = 'SNAPCHAT',
  AMAZON = 'AMAZON',
  REDDIT = 'REDDIT',
}

export enum BatchType {
  IN_FLIGHT = 'IN_FLIGHT',
  PRE_FLIGHT = 'PRE_FLIGHT',
  PLUGIN_MEDIA = 'PLUGIN_MEDIA',
}

export class LocationDTO {
  /** ISO code for the country of the location.
   * @example 'usa'
   */
  @AutoMap()
  isoCode: string;

  /** Name of the country of the location.
   * @example 'United States' */
  @AutoMap()
  name: string;
}

export class BrandDTO {
  /** UUID of the brand.
   * @example 'd7392a8d-3f73-44b8-aad6-10ca5c3f4aa8'
   */
  @AutoMap()
  id: string;

  /** Name of the brand.
   * @example 'My Brand' */
  @AutoMap()
  name: string;
}

// Platform Ad Account Data Transfer Object
export class PlatformAdAccountDTO {
  /** System assigned Id of the platform ad account.
   * @example 12
   * */
  @AutoMap()
  id: number;
  /** Platform of the ad account.
   * @example 'FACEBOOK'
   * */
  @AutoMap()
  platform: Platform;
  /** Account ID of the platform ad account.
   * @example '*********'
   * */
  @AutoMap()
  platformAccountId: string;
  /** Name of the platform ad account.
   * @example 'Ad Account 1'
   * */
  @AutoMap()
  platformAccountName: string;
}

// Person Data Transfer Object
export class PersonDTO {
  /** Unique identifier for the person.
   * @example 21541
   * */
  @AutoMap()
  id: number;

  /** First name of the person.
   * @example 'John'
   * */
  @AutoMap()
  firstName: string;

  /** Last name of the person.
   * @example 'Doe'
   * */
  @AutoMap()
  lastName: string;

  /** Email address of the person.
   * @example '<EMAIL>'
   * */
  @AutoMap()
  email: string;
}

export class GetScorecardsResponseDto {
  /** System assigned identifier for the scorecard.
   * @example '1251'
   */
  @AutoMap()
  id: string;

  /**
   * Short description of the scorecard.
   * TODO: The datamodel changes for compliance_batch to have this entity field.
   */
  description: string;

  /**
   * Proposed UUID for the scorecard.Replaces the db generated `id` field.
   * TODO: The datamodel changes for compliance_batch to have this entity field.
   */
  @IsUUID()
  scorecardId: string;

  /** Name of the scorecard.
   * @example 'Scorecard 1'
   * */
  @AutoMap()
  name: string;

  /** Type of the scorecard.
   * @example 'IN_FLIGHT'
   */
  @AutoMap()
  batchType: batchTypes;

  /** Status of the scorecard.
   *  @example 'PROCESSING'
   */
  @AutoMap()
  status: batchStatus;

  /** Unique identifier for the criteria set.
   * @example '789'
   * */
  @AutoMap()
  criteriaSetId: number;

  /**
   * Workspace id of the scorecard.
   */
  @AutoMap()
  partnerId: number;

  /** Platforms associated with the scorecard.
   * @example ['FACEBOOK','TWITTER']
   * */
  @AutoMap()
  platforms: Platform[];
  /**
   * Folder id of the scorecard.
   * @example '123'
   */
  @AutoMap()
  partnerAssetFolderId: number;
  /** Reason for the scorecard being outdated.
   * @example 'Criteria Change'
   * */
  @AutoMap()
  reasonOutdated: string;

  /** Date when the scorecard was created.
   * @example '2023-10-05T08:00:00Z'
   * */
  @AutoMap()
  dateCreated: Date;

  /** Date when the scorecard was last updated.
   * @example '2023-10-05T08:30:00Z'
   * */
  @AutoMap()
  lastUpdated: Date;

  /** Start date of the scorecard.
   * @example '2023-09-01T00:00:00Z'
   * */
  @AutoMap()
  startDate?: Date;

  /** End date of the scorecard.
   * @example '2023-09-30T23:59:59Z'
   * */
  @AutoMap()
  endDate?: Date;
  /** Score associated with the scorecard.
   * @example 85
   * */
  @AutoMap()
  score: number;

  /** Markets where scorecards is applicable.
   * */
  @AutoMap(() => LocationDTO)
  markets: LocationDTO[];

  /**
   * Platform ad account associated with the scorecard.
   * */
  @AutoMap(() => PlatformAdAccountDTO)
  platformAdAccount?: PlatformAdAccountDTO;

  /** Person associated with the scorecard, as a PersonDTO object. */
  @AutoMap()
  person: PersonDTO;

  /** Indicates whether the scorecard is outdated.
   * @example 1
   * */
  @AutoMap()
  isOutdated: boolean;

  /**
   * Indicates whether the scorecard is internal.
   * @example 0
   * */
  @AutoMap()
  isInternal: boolean;

  @AutoMap()
  requestOrigin?: string;

  baseReportId?: number;

  countMediaWithLifecycle?: number;

  mediaToLifecycleCount?: MediaLifecycleCount;

  /** Brands tied to scorecard.
   * @example ["d739278d-3673-44a8-aa26-10cadc3f4aa8", "0bddbd01-792a-462f-b5d8-5502ce220abc"]
   * */
  @AutoMap(() => BrandDTO)
  brands: BrandDTO[];

  @AutoMap()
  mediaId?: number;

  @AutoMap()
  deviceIdentifier?: string;

  @AutoMap()
  mediaRecognitionStatus: string;

  @AutoMap()
  mediaUrl?: string;

  @AutoMap()
  processingState: MediaProcessingState;
}
