import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class CreateResultDto {
  @ApiProperty({
    description: 'Identifier of the platform',
    example: 'FACEBOOK',
  })
  @IsString()
  @IsNotEmpty()
  platformIdentifier: string;

  @ApiProperty({
    description: 'ID of the workspace',
    example: 123,
  })
  @IsNumber()
  @IsNotEmpty()
  workspaceId: number;

  @ApiProperty({
    description: 'Name of the file to be processed',
    example: 'vidMobTemp.mov',
  })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({
    description: 'Scorecard name',
    example: 'VidMob Scorecard',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  scorecardName?: string;

  @ApiProperty({
    description: 'Device identifier',
    example: 'Adobe After Effects',
  })
  @IsString()
  @MaxLength(190)
  @IsOptional()
  deviceIdentifier?: string;
}
