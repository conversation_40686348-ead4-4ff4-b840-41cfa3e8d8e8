import { Test, TestingModule } from '@nestjs/testing';
import { ResultController } from './result.controller';
import { ResultService } from '../services/result.service';
import { CreateResultDto } from '../dto/create-result.dto';
import { Response } from 'express';
import { ForbiddenException } from '@nestjs/common';

describe('ResultController', () => {
  let controller: ResultController;
  let service: jest.Mocked<ResultService>;

  beforeEach(async () => {
    const mockResultService = {
      getScoringResultsByMedia: jest.fn(),
      createResult: jest.fn(),
      submitResult: jest.fn(),
      getMediaScoreDetails: jest.fn(),
    };
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ResultController],
      providers: [
        {
          provide: ResultService,
          useValue: mockResultService,
        },
      ],
    }).compile();

    controller = module.get<ResultController>(ResultController);
    service = module.get<ResultService>(
      ResultService,
    ) as jest.Mocked<ResultService>;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createResult', () => {
    it('should call ResultService with the correct parameters', async () => {
      const mockCreateResultDto: CreateResultDto = {
        platformIdentifier: 'FACEBOOK',
        workspaceId: 123,
        fileName: 'vidMobTemp.mov',
        scorecardName: 'vidMobTemp',
        deviceIdentifier: 'Adobe After Effects',
      };
      const mockAuthorization = 'Bearer token';
      const mockUserId = 1;

      const mockReq = {
        userId: mockUserId,
        headers: { authorization: mockAuthorization },
      };

      service.createResult.mockResolvedValue({
        mediaId: 456,
        scorecardId: 789,
      });

      await controller.createResult(mockCreateResultDto, mockReq as any);

      expect(service.createResult).toHaveBeenCalledWith(
        mockUserId,
        mockCreateResultDto,
        mockAuthorization,
      );
    });
  });

  describe('ResultController - Get Scorecards Summary', () => {
    let controller: ResultController;
    let service: jest.Mocked<ResultService>;

    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [ResultController],
        providers: [
          {
            provide: ResultService,
            useValue: {
              getScorecards: jest.fn(),
            },
          },
        ],
      }).compile();

      controller = module.get<ResultController>(ResultController);
      service = module.get<ResultService>(
        ResultService,
      ) as jest.Mocked<ResultService>;
    });

    it('should return a summary of scorecards successfully', async () => {
      const mockWorkspaceId = 123;
      const mockAuthorization = 'Bearer validToken';
      const mockUserId = 1; // Assuming this is set somehow in the request object
      const mockPaginationOptions = { offset: 0, perPage: 10 }; // Assuming default pagination options
      const mockGetScorecardsQueryParamDto = {}; // Assuming no query params for simplicity

      const mockScorecardsSummary = {
        status: 'OK',
        result: [
          {
            id: 1,
            status: 'SETUP',
            mediaId: 123,
            workspaceId: 123,
            scorecardId: 123,
          },
        ],
        pagination: {
          offset: 0,
          perPage: 50,
          nextOffset: 0,
          totalSize: 45,
        },
      };

      service.getScorecards.mockResolvedValue(mockScorecardsSummary as any);

      const result = await controller.getScorecards(
        mockPaginationOptions,
        mockWorkspaceId,
        mockGetScorecardsQueryParamDto as any,
        {
          userId: mockUserId,
          headers: { authorization: mockAuthorization },
        } as any,
      );

      expect(result).toEqual(mockScorecardsSummary);
      expect(service.getScorecards).toHaveBeenCalledWith(
        mockUserId,
        mockAuthorization,
        mockPaginationOptions,
        expect.objectContaining({ workspaceId: mockWorkspaceId }),
      );
    });

    it('should throw a ForbiddenException when the user does not have access to the workspace', async () => {
      service.getScorecards.mockRejectedValue(new ForbiddenException());

      await expect(
        controller.getScorecards(
          { offset: 0, perPage: 10 },
          123,
          { workspaceId: 123 },
          {
            userId: 1,
            headers: { authorization: 'Bearer invalidToken' },
          } as any,
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('getScoringResultsByMedia', () => {
    it('should call ResultService.getScoringResultsByMedia with the correct parameters and return the results', async () => {
      const mockMediaId = 456;
      const mockScorecardId = 789;
      const mockAuthorization = 'Bearer token';
      const mockResponseData = 'some scoring results';

      service.getScoringResultsByMedia.mockResolvedValue({
        statusCode: 200,
        data: { result: mockResponseData },
      });

      const mockRes: Partial<Response> = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const mockReq = { headers: { authorization: mockAuthorization } };

      await controller.getScoringResultsByMedia(
        mockMediaId,
        mockRes as Response,
        mockReq as any,
        mockScorecardId,
      );

      expect(service.getScoringResultsByMedia).toHaveBeenCalledWith(
        mockMediaId,
        mockScorecardId,
        mockAuthorization,
      );

      expect(mockRes.status).toHaveBeenCalledWith(200);

      const jsonMock = mockRes.json as jest.Mock;
      expect(jsonMock).toHaveBeenCalled();

      expect(jsonMock.mock.calls[0][0]).toEqual({
        result: 'some scoring results',
      });
    });
  });

  describe('submitResult', () => {
    const mockScorecardId = 123;
    const mockAuthorization = 'Bearer token';

    const mockRes: Partial<Response> = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    const mockReq = { headers: { authorization: mockAuthorization } };

    it('should successfully submit a scorecard', async () => {
      service.submitResult.mockResolvedValue({
        statusCode: 200,
        data: {
          status: 'OK',
          result: { id: mockScorecardId, status: 'SUBMITTED' },
        },
      });

      await controller.submitResult(
        { scorecardId: mockScorecardId },
        mockRes as Response,
        mockReq as any,
      );

      expect(service.submitResult).toHaveBeenCalledWith(
        mockScorecardId,
        mockAuthorization,
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'OK',
        result: { id: mockScorecardId, status: 'SUBMITTED' },
      });
    });

    it('should return an error if the scorecard is not in a valid state for submission', async () => {
      service.submitResult.mockResolvedValue({
        statusCode: 400,
        data: {
          status: 'Error',
          message:
            'Only batches in a SETUP, COMPLETE, or ERROR state may be submitted/resubmitted for processing',
        },
      });

      await controller.submitResult(
        { scorecardId: mockScorecardId },
        mockRes as Response,
        mockReq as any,
      );

      expect(service.submitResult).toHaveBeenCalledWith(
        mockScorecardId,
        mockAuthorization,
      );
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'Error',
        message:
          'Only batches in a SETUP, COMPLETE, or ERROR state may be submitted/resubmitted for processing',
      });
    });

    it('should handle unexpected errors gracefully', async () => {
      service.submitResult.mockResolvedValue({
        statusCode: 500,
        data: { message: 'An unexpected error occurred' },
      });

      await controller.submitResult(
        { scorecardId: mockScorecardId },
        mockRes as Response,
        mockReq as any,
      );

      expect(service.submitResult).toHaveBeenCalledWith(
        mockScorecardId,
        mockAuthorization,
      );
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'An unexpected error occurred',
      });
    });
  });
});
