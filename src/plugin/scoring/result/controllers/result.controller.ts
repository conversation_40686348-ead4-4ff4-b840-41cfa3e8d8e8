import {
  Controller,
  Get,
  Param,
  <PERSON>s,
  Post,
  Body,
  Request,
  ParseIntPipe,
  Query,
  Logger,
  Version,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiTags,
  ApiOkResponse,
  ApiParam,
  ApiBody,
  ApiCreatedResponse,
  ApiQuery,
  ApiSecurity,
} from '@nestjs/swagger';
import { ResultService } from '../services/result.service';
import { Response } from 'express';
import { CreateResultDto } from '../dto/create-result.dto';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import { readWorkspaceScoring } from '../../scoring.permissions';
import { GetScorecardsQueryparamsDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { SubmitScorecardMediaDto } from '../dto/submit-scorecard-media.dto';
import { CreateResultResponseDto } from '../dto/create-result-response.dto';

@ApiTags('Plugin Scoring')
@ApiSecurity('Bearer Token')
@Controller('plugin/scoring')
export class ResultController {
  constructor(private resultService: ResultService) {}
  private readonly logger = new Logger(ResultController.name);

  /**
   * This endpoint gets a list of scorecards for a given workspace. It supports a series of filters through query params.
   */
  @Permissions(readWorkspaceScoring)
  @Get('workspace/:workspaceId/summary')
  @ApiOperation({ summary: 'Get scorecards summary' })
  async getScorecards(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query() getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    this.logger.debug('Input - getScorecards: ', {
      GetScorecardsQueryparamsDto,
      PaginationOptions,
    });
    const userId: number = req['userId'] as number;

    getScorecardsQueryParamDto.workspaceId = workspaceId;

    return await this.resultService.getScorecards(
      userId,
      authorization,
      paginationOptions,
      getScorecardsQueryParamDto,
    );
  }

  /**
   * This endpoint gets a list of scorecards for a given workspace with project and iterationMedia details
   */
  @Permissions(readWorkspaceScoring)
  @Get('workspace/:workspaceId/summaryFromProjects')
  @ApiOperation({ summary: 'Get scorecards summary for iteration media' })
  async getScorecardDataForIterationMediaMock(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query() getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    this.logger.debug('Input - getScorecardDataForIterationMedia: ', {
      GetScorecardsQueryparamsDto,
      PaginationOptions,
    });
    const userId: number = req['userId'] as number;

    getScorecardsQueryParamDto.workspaceId = workspaceId;

    return await this.resultService.getScorecardDataForIterationMedia(
      userId,
      authorization,
      paginationOptions,
      getScorecardsQueryParamDto,
    );
  }

  /**
   * This endpoint gets the details of a specific scorecard by workspaceId, mediaId and scorecardId.
   */
  @Permissions(readWorkspaceScoring)
  @Get('workspace/:workspaceId/media/:mediaId/scoreResult')
  @ApiOperation({ summary: 'Get scoring results for a specific media' })
  @ApiParam({
    name: 'workspaceId',
    type: Number,
    required: true,
    description:
      'The ID of the workspace for which to retrieve scoring results',
  })
  @ApiParam({
    name: 'mediaId',
    type: String,
    required: true,
    description: 'The ID of the media for which to retrieve scoring results',
  })
  @ApiQuery({
    name: 'scorecardId',
    type: Number,
    required: false,
    description:
      'The ID of the scorecard for which to retrieve scoring results',
  })
  @ApiOkResponse({
    description: 'Scoring results returned successfully',
  })
  async getScoringResultsByMedia(
    @Param('mediaId') mediaId: number,
    @Res() res: Response,
    @Request() req: any,
    @Query('scorecardId') scorecardId?: number,
  ) {
    const authorization = req.headers.authorization;
    const response = await this.resultService.getScoringResultsByMedia(
      mediaId,
      scorecardId,
      authorization,
    );

    return res.status(response.statusCode).json(response.data);
  }

  /**
   * This endpoint gets the details of a specific scorecard by workspaceId, mediaId and scorecardId.
   */
  @Permissions(readWorkspaceScoring)
  @Get('workspace/:workspaceId/media/:mediaId/scoreResult')
  @Version('2')
  @ApiOperation({ summary: 'Get scoring results for a specific media' })
  @ApiParam({
    name: 'workspaceId',
    type: Number,
    required: true,
    description:
      'The ID of the workspace for which to retrieve scoring results',
  })
  @ApiParam({
    name: 'mediaId',
    type: String,
    required: true,
    description: 'The ID of the media for which to retrieve scoring results',
  })
  @ApiQuery({
    name: 'scorecardId',
    type: Number,
    required: false,
    description:
      'The ID of the scorecard for which to retrieve scoring results',
  })
  @ApiQuery({
    name: 'platforms',
    type: String,
    required: false,
    description:
      'The platform of the scorecard for which to retrieve scoring results',
  })
  @ApiOkResponse({
    description: 'Scoring results returned successfully',
  })
  async getScoringResultsByMediaV2(
    @Param('mediaId') mediaId: string,
    @Param('workspaceId') workspaceId: number,
    @Res() res: Response,
    @Request() req: any,
    @Query('scorecardId') scorecardId?: number,
  ) {
    const authorization = req.headers.authorization;
    const response = await this.resultService.getScoringResultsByMediaV2(
      mediaId,
      scorecardId,
      workspaceId,
      authorization,
    );

    return res.status(response.statusCode).json(response.data);
  }

  /**
   * This endpoint starts the scoring process for a media.
   */
  @Post('media')
  @ApiOperation({ summary: 'Submit scoring result' })
  @ApiBody({ type: CreateResultDto })
  @ApiCreatedResponse({
    description: 'Scoring result submitted successfully',
    type: CreateResultResponseDto,
  })
  async createResult(
    @Body() createResultDto: CreateResultDto,
    @Request() req: any,
  ): Promise<CreateResultResponseDto> {
    const authorization = req.headers.authorization;
    const userId: number = req['userId'] as number;
    return await this.resultService.createResult(
      userId,
      createResultDto,
      authorization,
    );
  }

  /**
   * This endpoint submits a scorecard with a media to be scored.
   */
  @Post('submit')
  @ApiOperation({ summary: 'Submit a scorecard with a media to be scored' })
  @ApiBody({ type: SubmitScorecardMediaDto })
  async submitResult(
    @Body() submitScorecardMediaDto: SubmitScorecardMediaDto,
    @Res() res: Response,
    @Request() req: any,
  ): Promise<any> {
    const authorization = req.headers.authorization;
    const response = await this.resultService.submitResult(
      submitScorecardMediaDto.scorecardId,
      authorization,
    );

    return res.status(response.statusCode).json(response.data);
  }
}
