import { Test, TestingModule } from '@nestjs/testing';
import { ResultService } from './result.service';
import { ConfigService } from '@nestjs/config';
import { CriteriaService } from '../../criteria/services/criteria.service';
import {
  CriteriaSetService,
  GetScorecardsResponseDto,
  ScoreService,
  ScorecardService,
  MediaService,
  ScoringCriteriaService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';
import { ProjectService } from '@vidmob/vidmob-studio-service-sdk';
import { ProjectService as LocalprojectService } from '../../../studio/project/services/project.service';
import axios from 'axios';
import { UserService } from '../../../user/services/user.service';
import {
  HttpStatus,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ResultService', () => {
  let service: ResultService;
  let configService: ConfigService;
  let criteriaService: jest.Mocked<CriteriaService>;
  let scoringCriteriaSetsService: jest.Mocked<CriteriaSetService>;
  let scorecardService: jest.Mocked<ScorecardService>;
  let scoreService: jest.Mocked<ScoreService>;
  let userService: jest.Mocked<UserService>;
  let workspaceService: jest.Mocked<WorkspaceService>;
  let mediaService: jest.Mocked<MediaService>;
  let projectService: jest.Mocked<ProjectService>;
  let localProjectService: jest.Mocked<LocalprojectService>;
  let scoringCriteriaService: jest.Mocked<ScoringCriteriaService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ResultService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('http://example.com'),
          },
        },
        {
          provide: CriteriaService,
          useValue: {
            isUserAllowedToReadCreate: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: CriteriaSetService,
          useValue: {
            getCriteriaSetAsPromise: jest.fn(),
          },
        },
        {
          provide: ScorecardService,
          useValue: {
            createScorecardAsPromise: jest.fn(),
            getScorecardsAsPromise: jest.fn(),
            updateScorecardAsPromise: jest.fn(),
          },
        },
        {
          provide: ScoreService,
          useValue: {
            getScoreAsPromise: jest.fn(),
          },
        },
        {
          provide: UserService,
          useValue: {
            getUser: jest.fn(),
          },
        },
        {
          provide: WorkspaceService,
          useValue: {
            getWorkspace: jest.fn(),
            checkBrandGovernanceAsPromise: jest.fn(),
          },
        },
        {
          provide: MediaService,
          useValue: {
            createMediaAsPromise: jest.fn(),
          },
        },
        {
          provide: ProjectService,
          useValue: {
            getProject: jest.fn(),
          },
        },
        {
          provide: ScoringCriteriaService,
          useValue: {
            getScoringCriteriaAsPromise: jest.fn(),
          },
        },
        {
          provide: LocalprojectService,
          useValue: {
            getEditorProjects: jest.fn(),
          },
        },
        {
          provide: 'automapper:nestjs:default',
          useValue: {
            map: jest.fn(),
            mapArray: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ResultService>(ResultService);
    configService = module.get<ConfigService>(ConfigService);
    criteriaService = module.get(CriteriaService);
    scorecardService = module.get(ScorecardService);
    scoreService = module.get(ScoreService);
    userService = module.get(UserService);
    workspaceService = module.get(WorkspaceService);

    const mockGetScorecardsResponseDto: GetScorecardsResponseDto = {
      id: '1',
      description: 'Mock Description',
      scorecardId: '2',
      name: 'Mock Scorecard',
      batchType: GetScorecardsResponseDto.BatchTypeEnum.InFlight,
      status: GetScorecardsResponseDto.StatusEnum.Complete,
      criteriaSetId: 101,
      partnerId: 201,
      platforms: [
        GetScorecardsResponseDto.PlatformsEnum.Facebook,
        GetScorecardsResponseDto.PlatformsEnum.Twitter,
      ],
      partnerAssetFolderId: 301,
      reasonOutdated: 'Mock Reason',
      dateCreated: '2023-01-01T00:00:00Z',
      lastUpdated: '2023-01-02T00:00:00Z',
      startDate: '2023-01-03T00:00:00Z',
      endDate: '2023-01-04T00:00:00Z',
      score: 85.5,
      totalMediaCount: 1,
      markets: [],
      platformAdAccount: undefined,
      person: undefined,
      isOutdated: false,
      isInternal: true,
      requestOrigin: 'Mock Origin',
      baseReportId: 123,
      countMediaWithLifecycle: 5,
      mediaToLifecycleCount: {},
      brands: [],
    };

    scorecardService.updateScorecardAsPromise.mockResolvedValue({
      status: 'OK',
      result: mockGetScorecardsResponseDto,
    });

    scorecardService.getScorecardsAsPromise.mockResolvedValue({
      status: 'OK',
      result: [mockGetScorecardsResponseDto],
    });

    mockedAxios.post.mockResolvedValue({
      data: {
        result: [{ media: { id: 'newMediaId' } }],
      },
    });

    jest.spyOn(service as any, 'resolveBatchDetails').mockResolvedValue({
      result: {
        id: 1,
        partnerAssetFolderId: 1,
        partnerId: 'mockPartnerId',
        person: {
          id: 1,
        },
      },
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createResult', () => {
    it('should create a new batch', async () => {
      const userId = 1;
      const createResultDto = {
        workspaceId: 123,
        platformIdentifier: 'FACEBOOK',
        fileName: 'vidMobTemp.mov',
        scorecardName: 'vidMobTemp',
        deviceIdentifier: 'Adobe After Effects',
      };
      const authorization = 'Bearer token';

      const result = await service.createResult(
        userId,
        createResultDto,
        authorization,
      );

      expect(result).toEqual({
        mediaId: 'newMediaId',
        scorecardId: 1,
      });
    });

    it('should create a new batch with no scorecardName on payload', async () => {
      const userId = 1;
      const createResultDto = {
        workspaceId: 123,
        platformIdentifier: 'FACEBOOK',
        fileName: 'vidMobTemp.mov',
        deviceIdentifier: 'Adobe After Effects',
      };
      const authorization = 'Bearer token';

      const result = await service.createResult(
        userId,
        createResultDto,
        authorization,
      );

      expect(result).toEqual({
        mediaId: 'newMediaId',
        scorecardId: 1,
      });
    });
  });

  describe('createScorecardWithIterationMedia', () => {
    it('should create a new scorecard with iteration media', async () => {
      const outputVideoId = 123;
      const dto = {
        workspaceId: 456,
        platformIdentifier: 'FACEBOOK',
        fileName: 'vidMobTemp.mov',
        scorecardName: 'vidMobTemp',
        totalHours: 1,
        deviceIdentifier: 'Adobe After Effects',
      };
      const authorization = 'Bearer token';
      const userId = 1;

      jest.spyOn(service as any, 'createIterationMedia').mockResolvedValueOnce({
        media: {
          id: 'iterationMediaId',
        },
      });

      jest
        .spyOn(service as any, 'createScorecardForIterationMedia')
        .mockImplementationOnce(async () => {
          return {
            result: { id: 123 },
          };
        });

      jest
        .spyOn(scorecardService as any, 'updateScorecardAsPromise')
        .mockImplementationOnce(async () => {
          return {
            status: 'OK',
          };
        });

      jest
        .spyOn(workspaceService as any, 'checkBrandGovernanceAsPromise')
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            brandGovernance: true,
          },
        });

      const result = await service.createScorecardWithIterationMedia(
        outputVideoId,
        dto,
        authorization,
        userId,
      );

      expect(result).toEqual({
        mediaId: 'iterationMediaId',
        scorecardId: 123,
      });
    });

    it('should create only the iteration media', async () => {
      const outputVideoId = 123;
      const dto = {
        workspaceId: 456,
        platformIdentifier: 'FACEBOOK',
        fileName: 'vidMobTemp.mov',
        scorecardName: 'vidMobTemp',
        totalHours: 1,
        deviceIdentifier: 'Adobe After Effects',
      };
      const authorization = 'Bearer token';
      const userId = 1;

      jest.spyOn(service as any, 'createIterationMedia').mockResolvedValueOnce({
        media: { id: 'iterationMediaId' },
      });

      jest
        .spyOn(service as any, 'createScorecardForIterationMedia')
        .mockImplementationOnce(async () => {
          return {
            result: { id: 123 },
          };
        });

      jest
        .spyOn(scorecardService as any, 'updateScorecardAsPromise')
        .mockImplementationOnce(async () => {
          return {
            status: 'OK',
          };
        });

      jest
        .spyOn(workspaceService as any, 'checkBrandGovernanceAsPromise')
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            brandGovernance: false,
          },
        });

      const result = await service.createScorecardWithIterationMedia(
        outputVideoId,
        dto,
        authorization,
        userId,
      );

      expect(result).toEqual({
        mediaId: 'iterationMediaId',
        scorecardId: null,
      });
    });

    it('should throw NotFoundException if iteration media creation fails', async () => {
      const outputVideoId = 123;
      const dto = {
        workspaceId: 456,
        platformIdentifier: 'FACEBOOK',
        fileName: 'vidMobTemp.mov',
        scorecardName: 'vidMobTemp',
        totalHours: 1,
        deviceIdentifier: 'Adobe After Effects',
      };
      const authorization = 'Bearer token';
      const userId = 1;

      jest
        .spyOn(service as any, 'createIterationMedia')
        .mockResolvedValueOnce(null);

      await expect(
        service.createScorecardWithIterationMedia(
          outputVideoId,
          dto,
          authorization,
          userId,
        ),
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe('ResultService.getScoringResultsByMediaV2', () => {
    let service: ResultService;
    let mockScorecardSDKService: jest.Mocked<ScorecardService>;
    const workspaceId = 23142;
    const mediaIdStr = '205308';
    const mediaIdNum = Number(mediaIdStr);

    const mediaDetailsResponse = {
      result: [
        {
          mediaDetailsWithScores: [
            {
              mediaObject: {
                id: mediaIdNum,
                displayName: 'video.mp4',
                fileType: 'VIDEO',
                mimeType: 'video/mp4',
                thumbnails: [],
                applicabilityType: 'VIDEO',
              },
              scores: [
                {
                  criteriaId: 10216,
                  status: 'NO_DATA',
                  detailedStatus: 'ERROR',
                },
                {
                  criteriaId: 10459,
                  status: 'NO_DATA',
                  detailedStatus: 'NEW_CRITERIA',
                },
              ],
            },
          ],
        },
      ],
    };

    const workspaceCriteriaResponse = {
      result: [
        {
          id: 10216,
          identifier: 'BRAND_NAME_IN_AUDIO_ANYTIME',
          parameters: {},
          name: 'aaaaaaaaaaaaaaa',
          platform: 'FACEBOOK',
          isOptional: 0,
          isCustom: 0,
          customValues: null,
          rule: 'Brand name mentioned',
          description:
            'Confirms if, based on this account’s brand settings, the brand name was mentioned in the video.',
          isBestPractice: false,
          category: 'Brand Visibility',
          applicability: 'VIDEO',
          criteriaSet: { isGlobal: 0 },
          customIconUrl: undefined,
        },
        {
          id: 10459,
          identifier: 'CUSTOM_COLOR',
          parameters: {},
          name: 'AmEx Blue',
          platform: 'FACEBOOK',
          isOptional: 0,
          isCustom: 1,
          customValues: ['[0,111,207]'],
          rule: "Includes: '#006fcf' included in the creative",
          description:
            'Indicates if specified colors are present in the creative.',
          isBestPractice: false,
          category: 'Other',
          applicability: 'VIDEO',
          criteriaSet: { isGlobal: 0 },
          customIconUrl: undefined,
        },
      ],
    };

    const builtScores = [
      {
        id: 10216,
        identifier: 'BRAND_NAME_IN_AUDIO_ANYTIME',
        parameters: {},
        rule: 'Brand name mentioned',
        description:
          'Confirms if, based on this account’s brand settings, the brand name was mentioned in the video.',
        name: 'aaaaaaaaaaaaaaa',
        platformIdentifier: 'FACEBOOK',
        result: 'ERROR',
        isOptional: 0,
        isCustom: 0,
        customValues: null,
        isBestPractice: false,
        category: 'Brand Visibility',
        mediaTypes: ['VIDEO'],
        isGlobal: false,
        customIconUrl: undefined,
      },
      {
        id: 10459,
        identifier: 'CUSTOM_COLOR',
        parameters: {},
        rule: "Includes: '#006fcf' included in the creative",
        description:
          'Indicates if specified colors are present in the creative.',
        name: 'AmEx Blue',
        platformIdentifier: 'FACEBOOK',
        result: 'NEW_CRITERIA',
        isOptional: 0,
        isCustom: 1,
        customValues: ['[0,111,207]'],
        isBestPractice: false,
        category: 'Other',
        mediaTypes: ['VIDEO'],
        isGlobal: false,
        customIconUrl: undefined,
      },
    ];

    beforeEach(() => {
      mockScorecardSDKService = {
        getScorecardByMediaIdAsPromise: jest.fn(),
        getScorecardAsPromise: jest.fn(),
        createScorecardAsPromise: jest.fn(),
        updateScorecardAsPromise: jest.fn(),
        getScorecardsAsPromise: jest.fn(),
      } as any;

      const fakeConfig = {
        get: jest.fn().mockImplementation((key: string, def: any) => def),
      };

      service = new ResultService(
        fakeConfig as any, // ConfigService
        {} as any, // CriteriaService
        mockScorecardSDKService,
        {} as any, // ScoreService
        {} as any, // ScoringCriteriaService
        {} as any, // CriteriaSetService
        {} as any, // WorkspaceService
        {} as any, // MediaService
        {} as any, // ProjectService
        {} as any, // LocalProjectService
        {} as any, // Mapper
      );

      jest
        .spyOn(service, 'getMediaScoreDetailsOnly')
        .mockResolvedValue(mediaDetailsResponse as any);
      jest
        .spyOn(service, 'getCriteriaForListOfWorkspaces')
        .mockResolvedValue(workspaceCriteriaResponse as any);
      jest
        .spyOn(service as any, 'buildIndividualMediaScoresResponse')
        .mockResolvedValue(builtScores as any);
      jest.spyOn(service, 'fetchMediaUrl').mockResolvedValue({
        mediaUrl: 'https://…mp4',
        mediaProcessingState: 'COMPLETE',
      } as any);

      mockScorecardSDKService.getScorecardByMediaIdAsPromise.mockResolvedValue({
        result: { id: '504215' },
      } as any);
      mockScorecardSDKService.getScorecardAsPromise.mockResolvedValue({
        result: {
          id: '504215',
          platforms: ['FACEBOOK'],
          score: 35,
        },
      } as any);
    });

    it('returns the correctly shaped success response', async () => {
      const response = await service.getScoringResultsByMediaV2(
        mediaIdStr,
        undefined,
        workspaceId,
        'Bearer token',
      );

      expect(response).toEqual({
        data: {
          status: 'OK',
          result: {
            scorePercentage: 35,
            platform: 'FACEBOOK',
            mediaProcessingState: 'COMPLETE',
            mediaUrl: 'https://…mp4',
            scores: builtScores,
          },
        },
        statusCode: 200,
      });

      expect(
        mockScorecardSDKService.getScorecardByMediaIdAsPromise,
      ).toHaveBeenCalledWith(mediaIdNum);
      expect(service.getMediaScoreDetailsOnly).toHaveBeenCalledWith(
        workspaceId,
        mediaIdNum,
      );
      expect(
        mockScorecardSDKService.getScorecardAsPromise,
      ).toHaveBeenCalledWith(Number('504215'));
      expect(service.getCriteriaForListOfWorkspaces).toHaveBeenCalledWith(
        [workspaceId.toString()],
        'ASC',
        'platform',
        'FACEBOOK',
        'VIDEO',
      );
      expect(
        (service as any).buildIndividualMediaScoresResponse,
      ).toHaveBeenCalledWith(
        mediaDetailsResponse.result[0].mediaDetailsWithScores[0].scores,
        workspaceCriteriaResponse.result,
      );
      expect(service.fetchMediaUrl).toHaveBeenCalledWith(
        'Bearer token',
        mediaIdNum,
      );
    });
  });
});
