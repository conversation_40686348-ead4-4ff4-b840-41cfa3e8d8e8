import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateResultDto } from '../dto/create-result.dto';
import axios from 'axios';
import {
  CreateScorecardRequestDto,
  CriteriaSetService,
  GetScorecardsQueryparamsDto,
  GetWorkspaceCriteriaSets200Response,
  ReadCriteriaSetDto,
  ReadScoreRequestDto,
  ScorecardService,
  MediaService,
  ScoreService as ScoreSDKService,
  GetMediaIdsForBatch200Response,
  CriteriaStatusDto,
  ScoringCriteriaService,
  GetScorecardsResponseDto as GetScorecardsResponseDtoSdk,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ProjectService } from '@vidmob/vidmob-studio-service-sdk';
import { ProjectService as LocalProjectService } from '../../../studio/project/services/project.service';
import { CriteriaService } from '../../criteria/services/criteria.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  DEFAULT_PER_PAGE_FOR_DUP_NAMES,
  DEFAULT_OFFSET_FOR_DUP_NAMES,
  PLUGIN_MEDIA,
  DEFATUL_SORT_ORDER_FOR_DUP_NAMES,
  DEFATUL_SORT_BY_FOR_DUP_NAMES,
  MAX_SULFIX,
  SUBMITTED_STATUS,
  platformIdentifierMapping,
  PARTNER_ASSET_MEDIA_TYPE,
  ITERATION_MEDIA_TYPE,
  DEFAULT_GET_SCORECARDS_PAGINATION_PARAMS,
} from '../../../../constants/scoring.constants';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { GetScorecardsResponseDto } from '../dto/get-scorecards-response.dto';
import { ReadResultsDto } from '../dto/read-results.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { CreateScorecardIterationMediaDto } from '../../../studio/project/dto/create-scorecard-iteration-media.dto';
import { CreateScorecardMediaResponseDto } from 'src/plugin/studio/project/dto/create-scorecard-media-response.dto';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';
import { CreateResultResponseDto } from '../dto/create-result-response.dto';
import { fetchAllPages } from '../../../../utils/fetchAllPages';
import { ScoreDto } from '../dto/read-individual-media-scores-response.dto';
import { MediaProcessingState } from '../dto/media-processing-state.dto';

@Injectable()
export class ResultService {
  baseUrlApi: string;
  baseUrlSubmitBatch: string;

  constructor(
    private configService: ConfigService,
    private criteriaService: CriteriaService,
    private scorecardSDKService: ScorecardService,
    private scoreService: ScoreSDKService,
    private readonly scoringCriteriaService: ScoringCriteriaService,
    private readonly scoringCriteriaSetsService: CriteriaSetService,
    private readonly workspaceService: WorkspaceService,
    private readonly mediaService: MediaService,
    private readonly projectService: ProjectService,
    private readonly localProjectService: LocalProjectService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.baseUrlApi = this.configService.get<string>('baseVidMobApiUrl', '');
    this.baseUrlSubmitBatch = this.configService.get<string>(
      'baseUrlSubmitBatch',
      '',
    );
  }

  async createResult(
    userId: number,
    createResultDto: CreateResultDto,
    authorization: string,
  ): Promise<CreateResultResponseDto> {
    const platformIdentifier = createResultDto.platformIdentifier;

    const scoringPlatform =
      platformIdentifierMapping[platformIdentifier.toUpperCase()];

    if (!scoringPlatform) {
      throw new BadRequestException(
        `Invalid platform identifier: ${platformIdentifier}`,
      );
    }

    createResultDto.platformIdentifier = scoringPlatform;

    const batchDetails = await this.resolveBatchDetails(
      userId,
      createResultDto,
      authorization,
    );

    if (!batchDetails.result) {
      const errorMessage = `Failed to create batch for workspace ${createResultDto.workspaceId}`;
      throw new Error(errorMessage);
    }

    const mediaId = await this.createPartnerAssetBulk(
      batchDetails.result,
      createResultDto,
      authorization,
    );

    if (!mediaId) {
      const errorMessage = `Failed to create media for batch ${batchDetails.result.id}`;
      throw new Error(errorMessage);
    }

    const batchId = Number(batchDetails.result.id);

    await this.scorecardSDKService.updateScorecardAsPromise(batchId, {
      mediaIds: [mediaId],
      status: SUBMITTED_STATUS,
    });

    return { mediaId: mediaId, scorecardId: batchId };
  }

  private async resolveBatchDetails(
    userId: number,
    createResultDto: CreateResultDto,
    authorization: string,
  ) {
    return await this.createComplianceBatchFromPayload(
      userId,
      createResultDto,
      authorization,
    );
  }

  private async createComplianceBatchFromPayload(
    userId: number,
    createResultDto: CreateResultDto,
    authorization: string,
  ) {
    const complianceBatchPayload = await this.createComplianceBatchPayload(
      createResultDto,
      userId,
      authorization,
    );

    return await this.createComplianceBatch(
      userId,
      complianceBatchPayload,
      authorization,
    );
  }

  private async createComplianceBatch(
    userId: number,
    createScorecardRequestDto: CreateScorecardRequestDto,
    authorization: string,
  ) {
    const isAllowed = await this.criteriaService.isUserAllowedToReadCreate(
      userId,
      createScorecardRequestDto.workspaceId,
      authorization,
    );

    if (!isAllowed) {
      const errorMessage = `User does not have the correct permissions to create the scorecard for workspace ${createScorecardRequestDto.workspaceId}`;
      throw new ForbiddenException(errorMessage);
    }

    return await this.scorecardSDKService.createScorecardAsPromise(
      userId,
      createScorecardRequestDto,
    );
  }

  async getScoringResultsByMedia(
    mediaId: number,
    scorecardId: number | undefined,
    authorization: string,
  ) {
    try {
      let resolvedScorecardId = scorecardId;

      if (!resolvedScorecardId) {
        const scorecardResponse =
          await this.scorecardSDKService.getScorecardByMediaIdAsPromise(
            mediaId,
          );

        resolvedScorecardId = Number(scorecardResponse.result.id);
      }

      const scorecard = await this.scorecardSDKService.getScorecardAsPromise(
        resolvedScorecardId,
      );
      const platform = scorecard.result.platforms;

      const { mediaUrl, mediaProcessingState } = await this.fetchMediaUrl(
        authorization,
        mediaId,
      );

      const response = await this.submitMediaForScoring(
        mediaId,
        resolvedScorecardId,
        platform,
        authorization,
      );

      return this.constructSuccessResponse(
        response,
        mediaUrl,
        scorecard,
        mediaProcessingState,
      );
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getScoringResultsByMediaV2(
    mediaId: string,
    scorecardId: number | undefined,
    workspaceId: number,
    authorization: string,
  ) {
    try {
      let resolvedScorecardId = scorecardId;
      const mediaIdAsNumber = Number(mediaId);

      if (!resolvedScorecardId) {
        const scorecardResponse =
          await this.scorecardSDKService.getScorecardByMediaIdAsPromise(
            mediaIdAsNumber,
          );

        resolvedScorecardId = Number(scorecardResponse.result.id);
      }

      const mediaDetailsResponse = await this.getMediaScoreDetailsOnly(
        Number(workspaceId),
        mediaIdAsNumber,
      );

      const mediaObject =
        mediaDetailsResponse.result?.[0]?.mediaDetailsWithScores?.[0]
          ?.mediaObject;

      const { applicabilityType } = mediaObject ?? {};

      const mediaScoresList: CriteriaStatusDto[] =
        mediaDetailsResponse.result?.[0]?.mediaDetailsWithScores[0]?.scores ??
        [];

      const scorecard = await this.scorecardSDKService.getScorecardAsPromise(
        resolvedScorecardId,
      );
      const platform = scorecard.result.platforms;
      const platformString = platform
        .map((platform: any) => platform.toString())
        .join(',');
      const workspaceCriteria = await this.getCriteriaForListOfWorkspaces(
        [workspaceId.toString()],
        'ASC',
        'platform',
        platformString,
        applicabilityType,
      );

      const scores = await this.buildIndividualMediaScoresResponse(
        mediaScoresList,
        workspaceCriteria.result,
      );

      const { mediaUrl, mediaProcessingState } = await this.fetchMediaUrl(
        authorization,
        mediaIdAsNumber,
      );

      return this.constructSuccessResponseV2(
        scores,
        mediaUrl,
        scorecard.result.score,
        mediaProcessingState,
        scorecard.result.platforms[0],
      );
    } catch (error) {
      return this.handleError(error);
    }
  }

  private async buildIndividualMediaScoresResponse(
    mediaScoresList: CriteriaStatusDto[],
    criteriaList: any[],
  ): Promise<ScoreDto[]> {
    return criteriaList.reduce<ScoreDto[]>((acc, criteria) => {
      const score = mediaScoresList.find(
        (score) => score.criteriaId == criteria.id,
      );

      if (!score) {
        return acc;
      }

      const {
        id,
        identifier,
        parameters,
        name,
        platform,
        isOptional,
        isCustom,
        customValues,
        rule,
        description,
        isBestPractice,
        category,
        applicability,
        criteriaSet,
        customIconUrl,
      } = criteria;

      const result = score.detailedStatus || score.status;

      acc.push({
        id,
        identifier,
        parameters,
        rule,
        description,
        name,
        platformIdentifier: platform,
        result,
        isOptional,
        isCustom,
        customValues,
        isBestPractice: Boolean(isBestPractice),
        category,
        mediaTypes: applicability.split(','),
        isGlobal: Boolean(criteriaSet?.isGlobal),
        customIconUrl,
      });

      return acc;
    }, []);
  }

  public async getCriteriaForListOfWorkspaces(
    workspaceIds: string[],
    sortOrder: 'ASC' | 'DESC' = 'ASC',
    sortBy: string,
    platformsString: string,
    applicabilityTypesString = '',
  ): Promise<any> {
    const identifier = undefined;
    const isOptional = undefined;
    const ownerIds = undefined;
    const searchText = undefined;
    const globalStatuses = undefined;
    const categories = undefined;
    const startDate = undefined;
    const endDate = undefined;
    const criteriaGroupIds = undefined;
    const offset = 0;
    const perPage = Number.MAX_SAFE_INTEGER;
    return await this.scoringCriteriaService.getCriteriaForListOfWorkspacesAsPromise(
      workspaceIds,
      sortOrder,
      identifier,
      sortBy,
      platformsString,
      applicabilityTypesString,
      isOptional,
      ownerIds,
      searchText,
      globalStatuses,
      categories,
      startDate,
      endDate,
      criteriaGroupIds,
      offset,
      perPage,
    );
  }

  async fetchMediaUrl(authorization: string, mediaId: number) {
    try {
      const response = await axios.get(
        `${this.baseUrlApi}/api/v1/media/${mediaId}`,
        {
          headers: { Authorization: authorization },
        },
      );
      const media = response.data.result;
      return {
        mediaUrl: media.mediaUrl,
        mediaProcessingState: media.processingState,
        mediaType: media.mediaType,
        deviceIdentifier: media.deviceIdentifier,
      };
    } catch (error) {
      if (
        error.response &&
        error.response.data &&
        error.response.data.error &&
        error.response.data.error.code === 'vidmob.media.accessViolation'
      ) {
        return {
          mediaUrl: null,
          mediaProcessingState: null,
          mediaType: null,
        };
      }
      throw error;
    }
  }

  async submitMediaForScoring(
    mediaId: number,
    scorecardId: number,
    platform: string[],
    authorization: string,
  ) {
    const payload = { batchId: scorecardId, channels: platform };

    const mediaResults = await axios.post(
      `${this.baseUrlSubmitBatch}/api/v1/media/contentAudit/${mediaId}`,
      payload,
      {
        headers: { Authorization: authorization },
      },
    );

    const scores = mediaResults.data.result.scores;

    for (const key in scores) {
      if (scores.hasOwnProperty(key)) {
        const translatedScore = await this.translateScore(scores[key]);
        // Replace the original score with the translated full score including rule, description and defaultDisplayName
        scores[key] = translatedScore.result;
      }
    }

    return mediaResults;
  }

  async translateScore(score: any) {
    try {
      const translatedScore =
        await this.scoreService.getScoringTranslateResultAsPromise(score);

      return translatedScore;
    } catch (error) {
      return {
        status: 'OK',
        result: {
          ...score,
          rule: '',
          description: '',
          defaultDisplayName: '',
        },
      };
    }
  }

  constructSuccessResponse(
    response: any,
    mediaUrl: any,
    scorecard: any,
    mediaProcessingState: any,
  ) {
    const scorePercentage = scorecard.result.score;
    const platform = scorecard.result.platforms[0];

    const modifiedResult = {
      scorePercentage,
      platform,
      mediaProcessingState,
      mediaUrl,
      ...response.data.result,
    };
    return {
      data: { ...response.data, result: modifiedResult },
      statusCode: response.status,
    };
  }

  constructSuccessResponseV2(
    response: ScoreDto[],
    mediaUrl: string,
    scorePercentage: number,
    mediaProcessingState: MediaProcessingState,
    platform: GetScorecardsResponseDtoSdk.PlatformsEnum,
  ) {
    const modifiedResult = {
      scorePercentage,
      platform,
      mediaProcessingState,
      mediaUrl,
      scores: response,
    };
    return {
      data: { status: 'OK', result: modifiedResult },
      statusCode: 200,
    };
  }
  handleError(error: any) {
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unexpected error occurred';
    const status =
      typeof error.response?.status === 'number' ? error.response?.status : 500;
    if (axios.isAxiosError(error)) {
      return { data: { status: 'Error', message }, statusCode: status };
    }

    return {
      data: {
        status: 'Error',
        message: error.response.error.message || 'An unexpected error occurred',
      },
      statusCode: typeof error.status === 'number' ? error.status : 500,
    };
  }

  /**
   * Straightforward call to get media score details without reading impressions.
   * Will respond with any scores regardless of impressions in range or dates.
   * @param mediaScoreDetailsRequestDto
   * @param paginationOptions
   */

  async getMediaScoreDetailsOnly(workspaceId: number, mediaId: number) {
    try {
      return await this.scoreService.getSingleMediaDetailsAsPromise({
        workspaceId,
        mediaId,
      });
    } catch (error) {
      return { result: null };
      console.error('Error fetching media score details:', error);
    }
  }

  private async createPartnerAssetBulk(
    batchDetails: any,
    createResultDto: CreateResultDto,
    authorization: string,
  ) {
    const { partnerAssetFolderId, person } = batchDetails;

    const fileName = createResultDto.fileName;
    const workspaceId = createResultDto.workspaceId;
    const personId = person.id;
    const partnerAssetFolderIdAsNumber = Number(partnerAssetFolderId);

    const payload = {
      media: [
        {
          source: 7,
          duration: 0,
          file: {},
          parentFolderId: partnerAssetFolderIdAsNumber,
          name: fileName,
          assetIdentifier: `vidmob-tus-${personId}-${workspaceId}-${fileName}-${new Date().getTime()}`,
          fileType: 'DOCUMENT',
          deviceIdentifier: createResultDto.deviceIdentifier,
        },
      ],
      partnerId: workspaceId,
    };

    const response = await axios.post(
      `${this.baseUrlApi}/api/v1/partnerAssetBulk?partnerId=${workspaceId}`,
      payload,
      {
        headers: { Authorization: authorization },
      },
    );

    return response.data.result[0].media.id;
  }

  async createScorecardWithIterationMedia(
    outputVideoId: number,
    dto: CreateScorecardIterationMediaDto,
    authorization: string,
    userId: number,
  ): Promise<CreateScorecardMediaResponseDto> {
    const iterationMedia = await this.createIterationMedia(
      outputVideoId,
      dto,
      authorization,
    );

    if (!iterationMedia || !iterationMedia.media) {
      throw new NotFoundException(`Iteration media could not be created.`);
    }

    const iterationMediaId = iterationMedia.media.id;

    const brandGovernanceResponse =
      await this.workspaceService.checkBrandGovernanceAsPromise(
        dto.workspaceId,
      );

    if (brandGovernanceResponse.result.brandGovernance) {
      const platformIdentifier = dto.platformIdentifier;

      const scoringPlatform =
        platformIdentifierMapping[platformIdentifier.toUpperCase()];

      dto.platformIdentifier = scoringPlatform;

      const scorecard = await this.createScorecardForIterationMedia(
        dto,
        iterationMediaId,
        authorization,
        userId,
      );

      if (!scorecard || !scorecard.result) {
        throw new NotFoundException('Scorecard could not be created.');
      }

      const scorecardId = Number(scorecard.result.id);
      await this.scorecardSDKService.updateScorecardAsPromise(scorecardId, {
        mediaIds: [iterationMediaId],
        status: SUBMITTED_STATUS,
      });

      return {
        mediaId: iterationMediaId,
        scorecardId: scorecardId,
      };
    } else {
      return {
        mediaId: iterationMediaId,
        scorecardId: null,
      };
    }
  }

  private async createIterationMedia(
    outputVideoId: number,
    dto: CreateScorecardIterationMediaDto,
    authorization: string,
  ): Promise<any> {
    const url = `${this.baseUrlApi}/api/v2/outputVideo/${outputVideoId}/iterationMedia`;
    try {
      const { data } = await axios.post(url, dto, {
        headers: { Authorization: authorization },
      });

      if (data.status !== 'OK')
        throw new BadRequestException('Failed to create iteration media');

      return data.result;
    } catch (error) {
      return {
        data: error.response
          ? error.response.data
          : 'An unexpected error occurred',
        statusCode: error.response
          ? error.response.status
          : HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  private async createScorecardForIterationMedia(
    dto: CreateScorecardIterationMediaDto,
    mediaId: number,
    authorization: string,
    userId: number,
  ): Promise<any> {
    if (
      !(await this.criteriaService.isUserAllowedToReadCreate(
        userId,
        dto.workspaceId,
        authorization,
      ))
    ) {
      throw new ForbiddenException(
        `User lacks permissions for workspace ${dto.workspaceId}`,
      );
    }

    let scorecardName = dto.scorecardName
      ? dto.scorecardName.trim()
      : dto.fileName.trim();

    const baseName = scorecardName.replace(/\.\w+$/, '');
    const extensionMatch = scorecardName.match(/(\.\w+)$/);
    const extension = extensionMatch ? extensionMatch[0] : '';

    scorecardName = await this.getScorecardNameWithSuffix(
      baseName,
      extension,
      userId,
      authorization,
      dto.workspaceId,
    );
    const criteriaSetId = await this.fetchCriteriaSetId(dto.workspaceId);

    try {
      return await this.scorecardSDKService.createScorecardAsPromise(userId, {
        workspaceId: dto.workspaceId,
        name: scorecardName,
        batchType: PLUGIN_MEDIA,
        platforms: [dto.platformIdentifier],
        criteriaSetId,
        mediaIds: [mediaId],
      });
    } catch (error) {
      throw new HttpException(
        `Failed to create scorecard: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getMediaScoreDetails(
    workspaceId: number,
    scorecardId: number,
    paginationOptions: PaginationOptions,
  ) {
    const mediaScoreDetailsRequestDto: ReadScoreRequestDto = {
      scorecardId: scorecardId,
      workspaceId: workspaceId,
      filters: {},
    };

    return this.scoreService.getMediaScoreDetailsAsPromise(
      mediaScoreDetailsRequestDto,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  private async createComplianceBatchPayload(
    createResultDto: CreateResultDto,
    userId: number,
    authorization: string,
  ): Promise<CreateScorecardRequestDto> {
    let scorecardName = createResultDto.scorecardName
      ? createResultDto.scorecardName.trim()
      : createResultDto.fileName.trim();

    const baseName = scorecardName.replace(/\.\w+$/, '');
    const extensionMatch = scorecardName.match(/(\.\w+)$/);
    const extension = extensionMatch ? extensionMatch[0] : '';

    scorecardName = await this.getScorecardNameWithSuffix(
      baseName,
      extension,
      userId,
      authorization,
      createResultDto.workspaceId,
    );

    const criteriaSetId = await this.fetchCriteriaSetId(
      createResultDto.workspaceId,
    );

    return {
      workspaceId: createResultDto.workspaceId,
      name: scorecardName,
      batchType: PLUGIN_MEDIA,
      platforms: [createResultDto.platformIdentifier],
      criteriaSetId: criteriaSetId,
      mediaIds: [],
      markets: null,
    };
  }

  private async getScorecardNameWithSuffix(
    baseName: string,
    extension: string,
    userId: number,
    authorization: string,
    workspaceId: number,
  ): Promise<string> {
    const escapeRegExp = (string) =>
      string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Utility function to escape regex special characters
    const escapedBaseName = escapeRegExp(baseName);

    const getScorecardsQueryParamDto: GetScorecardsQueryparamsDto = {
      workspaceId: workspaceId,
      sortOrder: DEFATUL_SORT_ORDER_FOR_DUP_NAMES,
      sortBy: DEFATUL_SORT_BY_FOR_DUP_NAMES,
      searchText: baseName,
    };
    const paginationOptions = {
      offset: DEFAULT_OFFSET_FOR_DUP_NAMES,
      perPage: DEFAULT_PER_PAGE_FOR_DUP_NAMES,
    };

    const scorecardsResponse = await this.fetchScorecardsWithDupNames(
      userId,
      authorization,
      paginationOptions,
      getScorecardsQueryParamDto,
    );

    let scorecardName = baseName + extension;

    // Check if the exact name already exists
    const nameExists = scorecardsResponse.result.some(
      (scorecard) => scorecard.name === scorecardName,
    );

    if (nameExists || scorecardsResponse.result.length > 0) {
      // Check if any scorecard exists with the base name
      const regexPattern = new RegExp(
        `^${escapedBaseName}(?:.*?)(\\d+)?${extension}$`,
      );
      let maxSuffix = MAX_SULFIX;

      scorecardsResponse.result.forEach((scorecard) => {
        const match = scorecard.name.match(regexPattern);
        if (match && match[1]) {
          const suffixNumber = parseInt(match[1], 10);
          if (suffixNumber >= maxSuffix) {
            maxSuffix = suffixNumber;
          }
        }
      });

      const newSuffix = maxSuffix + 1; // This ensures the first duplicate starts with suffix 2
      scorecardName = `${baseName} ${newSuffix}${extension}`;
    }

    return scorecardName;
  }

  private async fetchCriteriaSetId(workspaceId: number) {
    const criteriaSets: GetWorkspaceCriteriaSets200Response =
      await this.scoringCriteriaSetsService.getWorkspaceCriteriaSetsAsPromise(
        workspaceId,
      );

    if (!criteriaSets) {
      const message = 'No criteria set found for workspace id: ' + workspaceId;
      return Promise.reject(new Error(message));
    }

    let batchCriteriaSet: ReadCriteriaSetDto | undefined =
      criteriaSets.result.find((value) => value.name === 'DEFAULT');

    if (!batchCriteriaSet) {
      batchCriteriaSet = criteriaSets.result[0];
    }

    const criteriaSetId = Number(batchCriteriaSet.id);
    return criteriaSetId;
  }

  async fetchScorecardsWithDupNames(
    userId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
  ) {
    getScorecardsQueryParamDto.types = PLUGIN_MEDIA;
    getScorecardsQueryParamDto.creators = userId.toString();

    const { offset, perPage } = paginationOptions;
    const {
      workspaceId,
      markets,
      platforms,
      types,
      creators,
      statuses,
      sortBy,
      sortOrder,
      startDate,
      endDate,
      searchText,
      brands,
    } = getScorecardsQueryParamDto;

    // Check for read permissions
    const isAllowedToRead =
      await this.criteriaService.isUserAllowedToReadCreate(
        userId,
        getScorecardsQueryParamDto.workspaceId,
        authorization,
      );
    if (!isAllowedToRead) {
      throw new ForbiddenException(
        `User does not have the correct permissions to get list of scorecards for workspace ${workspaceId}`,
      );
    }

    const platformsAsString: string = platforms as unknown as string;

    return await this.scorecardSDKService.getScorecardsAsPromise(
      workspaceId,
      sortOrder,
      sortBy,
      types,
      platformsAsString,
      startDate,
      endDate,
      markets,
      creators,
      statuses,
      searchText,
      brands,
      undefined,
      offset,
      perPage,
    );
  }

  async getScorecards(
    userId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
    filterMediaType = PARTNER_ASSET_MEDIA_TYPE,
  ) {
    const { offset, perPage } = paginationOptions;

    getScorecardsQueryParamDto.types = PLUGIN_MEDIA;
    getScorecardsQueryParamDto.creators = userId.toString();

    const {
      workspaceId,
      markets,
      platforms,
      types,
      creators,
      statuses,
      sortBy,
      sortOrder,
      startDate,
      endDate,
      searchText,
      brands,
    } = getScorecardsQueryParamDto;

    const isAllowedToRead =
      await this.criteriaService.isUserAllowedToReadCreate(
        userId,
        getScorecardsQueryParamDto.workspaceId,
        authorization,
      );

    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to get list of scorecards for workspace ${workspaceId}`;
      throw new ForbiddenException(errorMessage);
    }

    const platformsAsString: string = platforms as unknown as string;

    const scorecardsResponse =
      await this.scorecardSDKService.getScorecardsAsPromise(
        workspaceId,
        sortOrder,
        sortBy,
        types,
        platformsAsString,
        startDate,
        endDate,
        markets,
        creators,
        statuses,
        searchText,
        brands,
        undefined,
        DEFAULT_GET_SCORECARDS_PAGINATION_PARAMS.offset,
        perPage > DEFAULT_GET_SCORECARDS_PAGINATION_PARAMS.perPage
          ? perPage
          : DEFAULT_GET_SCORECARDS_PAGINATION_PARAMS.perPage,
      );

    const enrichedScorecardsPromises: any = scorecardsResponse.result.map(
      async (scorecard) => {
        const scorecardId = Number(scorecard.id);

        const mediaId = await this.getMediaIdByBatch(scorecardId);

        const { mediaUrl, mediaProcessingState, mediaType, deviceIdentifier } =
          await this.fetchMediaUrl(authorization, Number(mediaId));

        const responseWithMedia: any = {
          ...scorecard,
          mediaId: mediaId,
          processingState: mediaProcessingState,
          mediaUrl: mediaUrl,
          mediaType: mediaType,
          deviceIdentifier: deviceIdentifier,
        };
        return responseWithMedia;
      },
    );

    const enrichedScorecards = await Promise.all(enrichedScorecardsPromises);

    const filteredScorecards = enrichedScorecards.filter(
      (scorecard) => scorecard.mediaType === filterMediaType,
    );

    const paginatedResults = filteredScorecards.slice(offset, offset + perPage);

    const mappedResults: ReadResultsDto[] = this.classMapper.mapArray(
      paginatedResults,
      GetScorecardsResponseDto,
      ReadResultsDto,
    );

    const total = filteredScorecards.length;

    return new PaginatedResultArray<ReadResultsDto>(mappedResults, total);
  }

  async getMediaIdByBatch(batchId: number) {
    const response: GetMediaIdsForBatch200Response =
      await this.mediaService.getMediaIdsForBatchAsPromise(batchId);

    return response.result[0];
  }

  async getScorecardDataForIterationMediaMock(
    userId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
  ) {
    type AnyObject = { [key: string]: any };

    const mocks = [
      {
        projectId: '35908',
        projectName: 'Payouts - Off-Platform',
        outputVideoId: '12345',
        outputVideoName: 'This is an Instagram output',
        iterationMedia: 'Draft 1.1',
      },
      {
        projectId: '35908',
        projectName: 'Payouts - Off-Platform',
        outputVideoId: '12345',
        outputVideoName: 'This is an Instagram output',
        iterationMedia: 'Draft 1.2',
      },
      {
        projectId: '35999',
        projectName: 'Really great stuff',
        outputVideoId: '12346',
        outputVideoName: 'This is another output',
        iterationMedia: 'Draft 1.1',
      },
      {
        projectId: '36002',
        projectName: 'Very cool project',
        outputVideoId: '12347',
        outputVideoName: 'This is a compelling output',
        iterationMedia: 'Draft 1.1',
      },
      {
        projectId: '36002',
        projectName: 'Very cool project',
        outputVideoId: '12347',
        outputVideoName: 'This is a compelling output',
        iterationMedia: 'Draft 1.2',
      },
    ];

    function interweaveArrayWithMocks(
      array: AnyObject[],
      mocks: AnyObject[],
    ): AnyObject[] {
      const result: AnyObject[] = [];
      const mocksLength = mocks.length;

      for (let i = 0; i < array.length; i++) {
        const combinedObject = { ...array[i], ...mocks[i % mocksLength] };
        result.push(combinedObject);
      }

      return result;
    }

    const scorecards = await this.getScorecards(
      userId,
      authorization,
      paginationOptions,
      getScorecardsQueryParamDto,
    );

    const newItems = interweaveArrayWithMocks(scorecards.items, mocks);
    const total = scorecards.totalCount;

    return new PaginatedResultArray<any>(newItems, total);
  }

  async getScorecardDataForIterationMedia(
    userId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
  ) {
    const scorecards = await this.getScorecards(
      userId,
      authorization,
      paginationOptions,
      getScorecardsQueryParamDto,
      ITERATION_MEDIA_TYPE,
    );

    const fetchProjectsByStatus = async (status: string) => {
      return fetchAllPages(
        (paginationOptions) =>
          this.localProjectService.getEditorProjects(
            authorization,
            paginationOptions,
            status,
          ),
        paginationOptions,
      );
    };

    const projects = await fetchProjectsByStatus('2,3');
    const accessibleProjectIds = new Set(projects.map((project) => project.id));

    const enhancedScorecards = await Promise.all(
      scorecards.items.map(async (scorecard) => {
        const mediaDetails = await this.fetchProjectMediaDetails(
          scorecard.mediaId,
        );

        const isAccessible = accessibleProjectIds.has(mediaDetails.projectId);

        return { ...scorecard, ...mediaDetails, isAccessible };
      }),
    );

    return new PaginatedResultArray<any>(
      enhancedScorecards,
      scorecards.totalCount,
    );
  }

  async fetchProjectMediaDetails(mediaId: number) {
    if (!mediaId) {
      return {};
    }

    const projectMediaDetails: any =
      await this.projectService.projectControllerGetProjectFromMediaAsPromise(
        mediaId,
      );

    return projectMediaDetails.result;
  }

  async submitResult(batchId: number, authorization: string): Promise<any> {
    const submitBatchEndpoint = `${this.baseUrlSubmitBatch}/api/v1/compliance/batch/${batchId}/submit`;

    try {
      const response = await axios.post(
        submitBatchEndpoint,
        { batchId: batchId },
        {
          headers: { Authorization: authorization },
        },
      );

      return {
        data: {
          status: 'OK',
          result: response.data.result,
        },
        statusCode: response.status,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          data: {
            status: 'Error',
            message: error.response?.data || 'An error occurred',
          },
          statusCode: error.response?.status || 500,
        };
      } else {
        return {
          data: { message: 'An unexpected error occurred' },
          statusCode: 500,
        };
      }
    }
  }
}
