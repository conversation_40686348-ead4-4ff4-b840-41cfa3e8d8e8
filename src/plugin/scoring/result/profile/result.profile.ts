import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { format } from 'date-fns';
import { Injectable } from '@nestjs/common';
import { GetScorecardsResponseDto } from '../dto/get-scorecards-response.dto';
import { ReadResultsDto } from '../dto/read-results.dto';

@Injectable()
export class ResultProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        GetScorecardsResponseDto,
        ReadResultsDto,
        forMember(
          (dest) => dest.scorecardStatus,
          mapFrom((src) => src.status),
        ),
        forMember(
          (dest) => dest.isOutdated,
          mapFrom((src) => src.isOutdated),
        ),
        forMember(
          (dest) => dest.scorecardId,
          mapFrom((src) => src.id),
        ),
        forMember(
          (dest) => dest.mediaId,
          mapFrom((src) => src.mediaId),
        ),
        forMember(
          (dest) => dest.deviceIdentifier,
          mapFrom((src) => src.deviceIdentifier),
        ),
        forMember(
          (dest) => dest.mediaProcessingState,
          mapFrom((src) => src.processingState),
        ),
        forMember(
          (dest) => dest.mediaUrl,
          mapFrom((src) => src.mediaUrl),
        ),
        forMember(
          (dest) => dest.mediaRecognitionStatus,
          mapFrom((src) => src.mediaRecognitionStatus),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.name),
        ),
        forMember(
          (dest) => dest.scorePercentage,
          mapFrom((src) => src.score),
        ),
        forMember(
          (dest) => dest.channel,
          mapFrom((src) => src.platforms[0]),
        ),
        forMember(
          (dest) => dest.dateCreated,
          mapFrom((src) => {
            const date = new Date(src.dateCreated);
            return format(date, 'MMM d, yyyy'); // Format the date as "Feb 2, 2022"
          }),
        ),
        forMember(
          (dest) => dest.mimeType,
          mapFrom((_src) => 'mp4'),
        ),
        forMember(
          (dest) => dest.version,
          mapFrom((_src) => '1.0'),
        ),
      );
    };
  }
}
