import { PermissionSubResource } from '../../auth/enums/permission.subresource.enum';
import { PermissionDomain } from '../../auth/enums/permission.domain.enum';
import { PermissionAction } from '../../auth/enums/permission.action.enum';
import {
  partner<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  partnerFromParamsHandler,
} from '../../auth/decorators/permission.decorator';

export const readDetails = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DETAILS,
    },
  ],
};

export const readWorkspaceScoring = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.SCORING,
    },
  ],
};

export const readWorkspaceScoringFromBody = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partner<PERSON>rom<PERSON>ody<PERSON>and<PERSON>,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.SCORING,
    },
  ],
};
