import { Test, TestingModule } from '@nestjs/testing';
import { CriteriaController } from './criteria.controller';
import { CriteriaService } from '../services/criteria.service';

describe('CriteriaController', () => {
  let controller: CriteriaController;
  let service: CriteriaService;

  const mockCriteriaService = {
    getWorkspaceCriteriaList: jest.fn(),
  };

  const mockResponse = {
    status: 'OK',
    result: [
      {
        name: '',
        identifier: 'FRAMED_FOR_MOBILE',
        dateCreated: '2024-02-01T17:39:56.000Z',
        id: 6875,
        criteriaSetId: 3,
        parameters: {
          aspectRatios: ['4:5'],
        },
        mediaTypes: ['IMAGE', 'VIDEO', 'ANIMATED_IMAGE'],
        platform: 'ALL_PLATFORMS',
        isBestPractice: false,
        isOptional: false,
        owner: null,
        custom: null,
      },
      {
        name: null,
        identifier: 'FRAMED_FOR_MOBILE',
        dateCreated: '2024-01-30T04:34:29.000Z',
        id: 6688,
        criteriaSetId: 3,
        parameters: {
          aspectRatios: ['1:1', '16:9', '4:5'],
        },
        mediaTypes: ['IMAGE', 'VIDEO'],
        platform: 'LINKEDIN',
        isBestPractice: true,
        isOptional: false,
        owner: null,
        custom: null,
      },
    ],
    pagination: {
      offset: 0,
      perPage: 2,
      nextOffset: 2,
      totalSize: 219,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CriteriaController],
      providers: [
        {
          provide: CriteriaService,
          useValue: mockCriteriaService,
        },
      ],
    }).compile();

    controller = module.get<CriteriaController>(CriteriaController);
    service = module.get<CriteriaService>(CriteriaService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getWorkspaceCriteriaList', () => {
    it('should return criteria list for a given workspace', async () => {
      const workspaceId = 1;
      const paginationOptions = { offset: 0, perPage: 10 };
      const userId = 123; // Mock user ID
      const authorization = 'Bearer token'; // Mock authorization token

      mockCriteriaService.getWorkspaceCriteriaList.mockResolvedValue(
        mockResponse,
      );
      const mockQueryParams: any = {
        mediaTypes: undefined,
        platforms: ['FACEBOOK'],
        sortBy: undefined,
        sortOrder: undefined,
        isOptional: undefined,
        ownerIds: undefined,
        searchText: undefined,
      };

      const req = { userId, headers: { authorization } };

      const result = await controller.getWorkspaceCriteriaList(
        paginationOptions,
        workspaceId,
        mockQueryParams,
        req,
      );

      expect(result).toEqual(mockResponse);
      expect(mockCriteriaService.getWorkspaceCriteriaList).toHaveBeenCalledWith(
        authorization,
        userId,
        workspaceId,
        paginationOptions,
        mockQueryParams,
      );
    });
  });
});
