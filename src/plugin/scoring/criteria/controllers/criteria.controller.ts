import { Controller, Get, Query, HttpStatus } from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiOkResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { CriteriaService } from '../services/criteria.service';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { Request } from '@nestjs/common';
import { GetCriteriaInCriteriaSets200Response } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { GetCriteriaQueryParamsDto } from '@vidmob/vidmob-soa-scoring-service-sdk';

@ApiTags('Plugin Scoring')
@ApiSecurity('Bearer Token')
@Controller('plugin/scoring')
export class CriteriaController {
  constructor(private criteriaService: CriteriaService) {}

  @Get('criteria')
  @ApiOperation({ summary: 'Get scoring criteria for a workspace' })
  @ApiQuery({
    name: 'workspaceId',
    type: Number,
    required: true,
    description:
      'The ID of the workspace for which to retrieve scoring criteria list.',
  })
  @ApiQuery({
    name: 'offset',
    type: 'number',
    required: false,
    description: 'The offset for pagination.',
  })
  @ApiQuery({
    name: 'perPage',
    type: 'number',
    required: false,
    description: 'Number of items to return per page.',
  })
  @ApiOkResponse({
    description: 'Scoring criteria returned successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  async getWorkspaceCriteriaList(
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('workspaceId') workspaceId: number,
    @Query() getCriteriaQueryParamsDto: GetCriteriaQueryParamsDto,
    @Request() req: any,
  ): Promise<GetCriteriaInCriteriaSets200Response> {
    const authorization = req.headers.authorization;
    return this.criteriaService.getWorkspaceCriteriaList(
      authorization,
      req.userId,
      workspaceId,
      paginationOptions,
      getCriteriaQueryParamsDto,
    );
  }
}
