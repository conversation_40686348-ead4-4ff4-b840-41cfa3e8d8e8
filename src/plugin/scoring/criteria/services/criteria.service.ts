import {
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import axios from 'axios';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  CriteriaSetService,
  GetCriteriaQueryParamsDto,
  ReadCriteriaSetDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { GetWorkspaceCriteriaSets200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getWorkspaceCriteriaSets200Response';
import { PermissionAction } from '../../../../auth/enums/permission.action.enum';
import { PermissionSubResource } from '../../../../auth/enums/permission.subresource.enum';
import { ScoringAuthService } from '../../scoring-auth/scoring-auth.service';
import { ConfigService } from '@nestjs/config';
import { platformIdentifierMapping } from '../../../../constants/scoring.constants';

@Injectable()
export class CriteriaService {
  baseUrlApi: string;
  private readonly logger = new Logger(CriteriaService.name);

  constructor(
    private readonly scoringCriteriaSetsService: CriteriaSetService,
    private readonly scoringAuthService: ScoringAuthService,
    private configService: ConfigService,
  ) {
    this.baseUrlApi = this.configService.get<string>('baseVidMobApiUrl', '');
  }

  async getWorkspaceCriteriaList(
    authorization: string,
    userId: number,
    workspaceId: number,
    paginationOptions: PaginationOptions,
    queryParams: GetCriteriaQueryParamsDto,
  ) {
    const isAllowedToRead = await this.isUserAllowedToReadCreate(
      userId,
      workspaceId,
      authorization,
    );

    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to get list of criteria for workspace ${workspaceId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    const criteriaSets: GetWorkspaceCriteriaSets200Response =
      await this.scoringCriteriaSetsService.getWorkspaceCriteriaSetsAsPromise(
        workspaceId,
      );

    const defaultCriteriaSet: ReadCriteriaSetDto | undefined =
      criteriaSets.result.find((value) => value.name === 'DEFAULT');

    if (!defaultCriteriaSet) {
      const message =
        'No default criteria set found for workspace id: ' + workspaceId;
      this.logger.error(message);
      throw new NotFoundException(message);
    }

    const criteriaSetIds = [defaultCriteriaSet.id?.toString()];

    const globalCriteriaSet: ReadCriteriaSetDto | undefined =
      criteriaSets.result.find(
        (value) => value.workspaceId === null && value.name === 'GLOBAL',
      );

    if (globalCriteriaSet) {
      criteriaSetIds.push(globalCriteriaSet.id?.toString());
    }

    const {
      mediaTypes,
      platforms,
      sortBy,
      sortOrder,
      isOptional,
      ownerIds,
      searchText,
      globalStatuses,
      categories,
    } = queryParams;

    // TS incorrectly thinks these values should have been transformed into arrays by the DTO.
    // But those transforms only happen in the scoring service. Which is why the below assertions are needed for now.

    const platformsStr: string = platforms as unknown as string;
    const mediaTypesStr: string = mediaTypes as unknown as string;
    const isOptionalStr: string = isOptional as unknown as string;
    const globalStatusesStr: string = globalStatuses as unknown as string;

    const scoringPlatforms = platformsStr
      ? platformsStr
          .split(',')
          .map((platform) => platformIdentifierMapping[platform.toUpperCase()])
          .join(',')
      : null;

    return await this.scoringCriteriaSetsService.getCriteriaInCriteriaSetsAsPromise(
      criteriaSetIds, // criteriaSetIds
      sortOrder, // sortOrder
      undefined, // identifier
      sortBy, // sortBy
      scoringPlatforms, // platforms
      mediaTypesStr, // mediaTypes
      isOptionalStr, // isOptional
      ownerIds, // ownerIds
      searchText, // searchText
      globalStatusesStr, // globalStatuses
      categories, // categories
      undefined, // startDate
      undefined, // endDate
      undefined, // criteriaGroupIds
      paginationOptions.offset, // offset
      paginationOptions.perPage, // perPage
    );
  }

  /**
   * TODO This should ideally be done by the auth guards but since multiple permissions checks are not supported, we have to execute the query manually.
   * At some point, when the guards can do multiple checks, this logic needs to be removed from here.
   * 1. Checks if the user has READ DETAILS permission for the partner
   * 2. Checks if the user is a partner manager for the partner
   * @param userId
   * @param createScorecardRequestDto
   * @private
   */
  async isUserAllowedToReadCreate(
    userId: number,
    workspaceId: number,
    authorization: string,
  ): Promise<boolean> {
    const isUserOrgAdmin = await this.scoringAuthService.getIsUserOrgAdmin(
      userId,
      workspaceId,
    );

    if (isUserOrgAdmin) {
      return true;
    }

    const permissionCheckResult =
      await this.scoringAuthService.checkPermissions(
        userId,
        workspaceId,
        PermissionSubResource.SCORING,
        PermissionAction.READ,
      );

    if (permissionCheckResult.length > 0) {
      return true;
    }

    const creatorHasWorkspaceAssociation =
      await this.checkCreatorAssociationWithWorkspace(
        userId,
        workspaceId,
        authorization,
      );

    if (creatorHasWorkspaceAssociation) {
      return true;
    }

    const isPartnerManager = await this.scoringAuthService.isUserPartnerManager(
      userId,
      workspaceId,
    );

    return isPartnerManager;
  }

  async checkCreatorAssociationWithWorkspace(
    userId: number,
    workspaceId: number,
    authorization: string,
  ): Promise<boolean> {
    const queryParams = new URLSearchParams({
      status: '0,1,2',
      extraFields: 'partner',
    });

    const endpoint = `${this.baseUrlApi}/api/v2/editor/${userId}/project?${queryParams}`;
    try {
      const response = await axios.get(endpoint, {
        headers: { Authorization: authorization },
      });
      const projects = response.data.result;

      // Map partner IDs from projects and check if any match the workspaceId
      const partnerIds = projects.map((project: any) => project.partner?.id);
      return partnerIds.includes(workspaceId);
    } catch (error) {
      console.error('Error fetching user projects:', error);

      return false;
    }
  }
}
