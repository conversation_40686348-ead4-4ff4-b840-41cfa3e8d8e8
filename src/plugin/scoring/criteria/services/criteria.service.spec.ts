import { Test, TestingModule } from '@nestjs/testing';
import { CriteriaService } from './criteria.service';
import { CriteriaSetService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoringAuthService } from '../../scoring-auth/scoring-auth.service';
import { ConfigService } from '@nestjs/config';
import { ReadCriteriaSetDto } from '@vidmob/vidmob-soa-scoring-service-sdk';

describe('CriteriaService', () => {
  let service: CriteriaService;
  let criteriaSetService: jest.Mocked<CriteriaSetService>;
  let scoringAuthService: jest.Mocked<ScoringAuthService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockCriteriaSetService = {
      getWorkspaceCriteriaSetsAsPromise: jest.fn(),
      getCriteriaForCriteriaSetsAsPromise: jest.fn(),
      getCriteriaInCriteriaSetsAsPromise: jest.fn(),
    };

    const mockScoringAuthService = {
      getIsUserOrgAdmin: jest.fn(),
      checkPermissions: jest.fn(),
      isUserPartnerManager: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CriteriaService,
        { provide: CriteriaSetService, useValue: mockCriteriaSetService },
        { provide: ScoringAuthService, useValue: mockScoringAuthService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<CriteriaService>(CriteriaService);
    criteriaSetService = module.get(CriteriaSetService);
    scoringAuthService = module.get(ScoringAuthService);
    configService = module.get(ConfigService);

    configService.get.mockReturnValue('http://example.com');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getWorkspaceCriteriaList', () => {
    it('should return criteria list when user is allowed to read', async () => {
      const workspaceId = 1;
      const userId = 123;
      const authorization = 'Bearer some-token';
      const paginationOptions = { offset: 0, perPage: 10 };

      const mockCriteriaSet: ReadCriteriaSetDto = {
        organizationId: '1',
        id: 1,
        name: 'DEFAULT',
        workspaceId: workspaceId,
        dateCreated: '2024-02-02T23:45:40.793Z',
        lastUpdated: '2024-02-02T23:45:40.793Z',
      };

      const mockQueryParams: any = {
        mediaTypes: undefined,
        platforms: 'FACEBOOK',
        sortBy: undefined,
        sortOrder: undefined,
        isOptional: undefined,
        ownerIds: undefined,
        searchText: undefined,
      };

      scoringAuthService.getIsUserOrgAdmin.mockResolvedValue(false);
      scoringAuthService.checkPermissions.mockResolvedValue([true]);
      criteriaSetService.getWorkspaceCriteriaSetsAsPromise.mockResolvedValue({
        status: 'OK',
        result: [mockCriteriaSet],
      });
      criteriaSetService.getCriteriaInCriteriaSetsAsPromise.mockResolvedValue({
        status: 'OK',
        result: [],
        pagination: { offset: 0, perPage: 10, nextOffset: 10, totalSize: 0 },
      });

      const result = await service.getWorkspaceCriteriaList(
        authorization,
        userId,
        workspaceId,
        paginationOptions,
        mockQueryParams,
      );

      expect(result).toEqual({
        status: 'OK',
        result: [],
        pagination: { offset: 0, perPage: 10, nextOffset: 10, totalSize: 0 },
      });
      expect(
        criteriaSetService.getWorkspaceCriteriaSetsAsPromise,
      ).toHaveBeenCalledWith(workspaceId);
      expect(
        criteriaSetService.getCriteriaInCriteriaSetsAsPromise,
      ).toHaveBeenCalledWith(
        [mockCriteriaSet.id.toString()], // criteriaSetIds
        undefined, // sortOrder
        undefined, // identifier
        undefined, // sortBy
        mockQueryParams.platforms, // platforms
        undefined, // mediaTypes
        undefined, // isOptional
        undefined, // ownerIds
        undefined, // searchText
        undefined, // globalStatuses
        undefined, // categories
        undefined, // startDate
        undefined, // endDate
        undefined, // criteriaGroupIds
        paginationOptions.offset, // offset
        paginationOptions.perPage, // perPage
      );
    });
  });

  describe('isUserAllowedToReadCreate', () => {
    it('should return true if user is an organization admin', async () => {
      const userId = 123;
      const workspaceId = 1;
      const authorization = 'Bearer some-token';

      scoringAuthService.getIsUserOrgAdmin.mockResolvedValue(true);

      const result = await service.isUserAllowedToReadCreate(
        userId,
        workspaceId,
        authorization,
      );

      expect(result).toBeTruthy();
      expect(scoringAuthService.getIsUserOrgAdmin).toHaveBeenCalledWith(
        userId,
        workspaceId,
      );
    });
  });
});
