import { Injectable, Logger } from '@nestjs/common';
import // Import necessary DTOs and services here
'@vidmob/vidmob-soa-scoring-service-sdk';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { PermissionSubResource } from '../../../auth/enums/permission.subresource.enum';
import { PermissionAction } from '../../../auth/enums/permission.action.enum';

@Injectable()
export class ScoringAuthService {
  private readonly logger = new Logger(ScoringAuthService.name);

  constructor(
    @InjectDataSource()
    private readonly datasource: DataSource,
  ) {}

  public async checkPermissions(
    userId: number,
    partnerId: number,
    subResource: PermissionSubResource,
    action: PermissionAction,
    resource = 'partner',
  ) {
    const sql = `SELECT pe.id
                   FROM permission pe
                          JOIN role_permission rp ON rp.permission_id = pe.id
                          JOIN partner_person pp ON pp.role_id = rp.role_id
                          JOIN partner pa ON pp.partner_id = pa.id
                   WHERE pe.resource = ?
                     AND pe.subresource = ?
                     AND pe.type = ?
                     AND pp.person_id = ?
                     AND pa.id = ?
                     AND pp.active = 1`;

    return await this.datasource.query(sql, [
      resource,
      subResource,
      action,
      userId,
      partnerId,
    ]);
  }

  public async getIsUserOrgAdmin(userId: number, workspaceId: number) {
    const subResource = 'workspace_all';
    const action = 'update';
    const resource = 'organization';

    const sql = `SELECT pe.id
                   FROM permission pe
                          JOIN role_permission rp ON rp.permission_id = pe.id
                          JOIN organization_person_role opr ON opr.role_id = rp.role_id
                          JOIN organization org ON org.id = opr.organization_id
                          JOIN partner pa ON pa.organization_id = org.id
                   WHERE pe.resource = ?
                     AND pe.subresource = ?
                     AND pe.type = ?
                     AND opr.person_id = ?
                     AND pa.id = ?`;

    const result = await this.datasource.query(sql, [
      resource,
      subResource,
      action,
      userId,
      workspaceId,
    ]);

    return result.length > 0;
  }

  public async isUserPartnerManager(
    userId: number,
    partnerId: number,
  ): Promise<boolean> {
    const sql = `SELECT pm.id FROM partner_manager pm WHERE (pm.role in ('ACCOUNT_MANAGER', 'PROJECT_MANAGER')) AND pm.manager_id = ? AND pm.partner_id = ?`;
    const result = await this.datasource.query(sql, [userId, partnerId]);

    return result.length > 0;
  }
}
