import { Test, TestingModule } from '@nestjs/testing';
import { ScoringAuthService } from './scoring-auth.service';
import { DataSource } from 'typeorm';

describe('ScoringAuthService', () => {
  let service: ScoringAuthService;
  let dataSource: DataSource;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScoringAuthService,
        {
          provide: DataSource,
          useValue: {
            query: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ScoringAuthService>(ScoringAuthService);
    dataSource = module.get<DataSource>(DataSource);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('checkPermissions', () => {
    it('should return permissions', async () => {
      const mockResult = [{ id: 1 }];
      jest.spyOn(dataSource, 'query').mockResolvedValue(mockResult);

      const result = await service.checkPermissions(
        1,
        1,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        'workspace',
        'some-action',
      );
      expect(result).toEqual(mockResult);
      expect(dataSource.query).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Array),
      );
    });
  });

  describe('getIsUserOrgAdmin', () => {
    it('should return true if user is org admin', async () => {
      jest.spyOn(dataSource, 'query').mockResolvedValue([{ id: 1 }]);

      const result = await service.getIsUserOrgAdmin(1, 1);
      expect(result).toBeTruthy();
      expect(dataSource.query).toHaveBeenCalled();
    });
  });

  describe('isUserPartnerManager', () => {
    it('should return true if user is partner manager', async () => {
      jest.spyOn(dataSource, 'query').mockResolvedValue([{ id: 1 }]);

      const result = await service.isUserPartnerManager(1, 1);
      expect(result).toBeTruthy();
      expect(dataSource.query).toHaveBeenCalled();
    });
  });
});
