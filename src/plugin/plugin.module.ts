import { Modu<PERSON> } from '@nestjs/common';
import { CriteriaController } from './scoring/criteria/controllers/criteria.controller';
import { ResultController } from './scoring/result/controllers/result.controller';
import { CriteriaService } from './scoring/criteria/services/criteria.service';
import { ResultService } from './scoring/result/services/result.service';
import { ScoringAuthService } from './scoring/scoring-auth/scoring-auth.service';
import { ConfigModule } from '@nestjs/config';
import { UserController } from './user/controllers/user.controller';
import { UserService } from './user/services/user.service';
import { UserProfile } from './user/mapper/user.profile';
import { OrganizationController } from './organization/controllers/organization.controller';
import { OrganizationService } from './organization/services/organization.service';
import { OrganizationProfile } from './organization/mapper/organization.profile';
import { WorkspaceController } from './workspace/controllers/workspace.controller';
import { WorkspaceService } from './workspace/services/workspace.service';
import { AuthService } from '../auth/services/auth.service';
import { WorkspaceProfile } from './workspace/mapper/workspace.profile';
import { ResultProfile } from '../plugin/scoring/result/profile/result.profile';
import { ProjectController } from '../plugin/studio/project/controllers/project.controller';
import { ProjectService } from '../plugin/studio/project/services/project.service';
import { ProjectProfile } from './studio/project/mapper/project.profile';
import { MessageChannelController } from './studio/message/controllers/message-channel.controller';
import { MessageChannelService } from './studio/message/services/message-channel.service';
import { MessageController } from './studio/message/controllers/message.controller';
import { MessageService } from './studio/message/services/message.service';

@Module({
  controllers: [
    CriteriaController,
    MessageChannelController,
    MessageController,
    ProjectController,
    OrganizationController,
    ResultController,
    UserController,
    WorkspaceController,
  ],
  providers: [
    AuthService,
    CriteriaService,
    MessageChannelService,
    MessageService,
    OrganizationProfile,
    OrganizationService,
    ProjectProfile,
    ProjectService,
    ResultProfile,
    ResultService,
    ScoringAuthService,
    UserService,
    UserProfile,
    WorkspaceProfile,
    WorkspaceService,
  ],
  imports: [ConfigModule],
})
export class PluginModule {}
