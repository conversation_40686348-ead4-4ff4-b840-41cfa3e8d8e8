import { PermissionSubResource } from '../../auth/enums/permission.subresource.enum';
import { PermissionDomain } from '../../auth/enums/permission.domain.enum';
import { PermissionAction } from '../../auth/enums/permission.action.enum';
import { organizationFromParamsHandler } from '../../auth/decorators/permission.decorator';

export const readWorkspaces = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const checkOrganizationReadAllWorkspaces = {
  statements: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ALL,
    },
  ],
};
