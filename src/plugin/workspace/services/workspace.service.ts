import { Injectable, NotFoundException } from '@nestjs/common';
import {
  FindWorkspacesByOrganization200Response,
  OrganizationService,
  WorkspaceService as WorkspaceServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { SearchParamsDto } from '../dto/search-params.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { PermissionDomain } from '../../../auth/enums/permission.domain.enum';
import { AuthService } from '../../../auth/services/auth.service';
import { checkOrganizationReadAllWorkspaces } from '../workspace.permissions';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { ReadWorkspaceDto as ReadLocalWorkspaceDto } from '../dto/read-workspace.dto';
import { FEATURE_BRAND_GOVERNANCE } from '../../../constants/workspace.constants';

@Injectable()
export class WorkspaceService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly workspaceService: WorkspaceServiceSdk,
    private readonly authService: AuthService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async canReadWorkspaceAll(authorization: string, organizationId: string) {
    const domain = PermissionDomain.ORGANIZATION;

    const responseObservable = await this.authService.isResourcePermissionValid(
      domain,
      organizationId,
      authorization,
      checkOrganizationReadAllWorkspaces.statements,
    );

    return responseObservable;
  }

  async getBrandGovernanceWorkspacesByOrganizationId(
    organizationId: string,
    userId: number,
    paginationOptions: PaginationOptions,
    searchParams: SearchParamsDto,
  ): Promise<PaginatedResultArray<ReadLocalWorkspaceDto>> {
    const response =
      await this.workspaceService.findAllUserWorkspaceForAllOrgsAsPromise(
        userId,
        organizationId,
        FEATURE_BRAND_GOVERNANCE,
      );

    // Check if results are empty after filtering
    if (response.result.length === 0) {
      throw new NotFoundException(
        'This organization does not have any workspaces enabled with the Brand Governance feature.',
      );
    }

    // Filter out all workspaces where isPersonal is true
    let results = response.result.filter((workspace) => !workspace.isPersonal);

    if (searchParams.search && searchParams.search.trim() !== '') {
      const searchTerm = searchParams.search.trim().toLowerCase();
      results = results.filter((workspace) =>
        workspace.name.toLowerCase().includes(searchTerm),
      );
    }

    // Apply pagination
    const start = paginationOptions.offset;
    const end = start + paginationOptions.perPage;
    const paginatedResults = results.slice(start, end);

    // Map the paginated results to ReadLocalWorkspaceDto
    const mappedResults = this.classMapper.mapArray(
      paginatedResults,
      ReadLocalWorkspaceDto,
      ReadLocalWorkspaceDto,
    );

    // Total count after applying the search filter but before pagination
    const total = results.length;

    return new PaginatedResultArray<ReadLocalWorkspaceDto>(
      mappedResults,
      total,
    );
  }

  async getWorkspacesByOrganizationId(
    organizationId: string,
    userId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    searchParams: SearchParamsDto,
  ): Promise<PaginatedResultArray<ReadLocalWorkspaceDto>> {
    const canReadAll = await this.canReadWorkspaceAll(
      authorization,
      organizationId,
    );

    if (canReadAll) {
      return this.getWorkspacesByOrganizationIdAndSearch(
        organizationId,
        searchParams,
        paginationOptions,
      );
    } else {
      return this.getWorkspaceByOrganizationIdAndUserAndSearch(
        organizationId,
        userId,
        paginationOptions,
        searchParams,
      );
    }
  }

  async getWorkspaceByOrganizationIdAndUserAndSearch(
    organizationId: string,
    userId: number,
    paginationOptions: PaginationOptions,
    searchParams: SearchParamsDto,
  ) {
    const { search, market, brand } = searchParams;
    const findWorkspacesByOrganization200Response: FindWorkspacesByOrganization200Response =
      await this.organizationService.findWorkspacesByOrganizationAndUserAsPromise(
        organizationId,
        userId,
        search,
        market,
        brand,
        paginationOptions.offset,
        paginationOptions.perPage,
      );

    const results = findWorkspacesByOrganization200Response.result;

    const mappedResults = this.classMapper.mapArray(
      results,
      ReadLocalWorkspaceDto,
      ReadLocalWorkspaceDto,
    );
    const total = findWorkspacesByOrganization200Response.pagination?.totalSize;

    return new PaginatedResultArray<ReadLocalWorkspaceDto>(
      mappedResults,
      total,
    );
  }

  async getWorkspacesByOrganizationIdAndSearch(
    organizationId: string,
    searchParams: SearchParamsDto,
    paginationOptions: PaginationOptions,
  ) {
    const { market, search, brand } = searchParams;

    try {
      const findWorkspacesByOrganization200Response: FindWorkspacesByOrganization200Response =
        await this.organizationService.findWorkspacesByOrganizationAsPromise(
          organizationId,
          search,
          market,
          brand,
          paginationOptions.offset,
          paginationOptions.perPage,
        );

      const results = findWorkspacesByOrganization200Response.result;

      const mappedResults = this.classMapper.mapArray(
        results,
        ReadLocalWorkspaceDto,
        ReadLocalWorkspaceDto,
      );

      const total =
        findWorkspacesByOrganization200Response.pagination?.totalSize;
      return new PaginatedResultArray<ReadLocalWorkspaceDto>(
        mappedResults,
        total,
      );
    } catch (e) {
      throw e;
    }
  }
}
