import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceService } from './workspace.service';
import {
  OrganizationService,
  WorkspaceService as WorkspaceServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { AuthService } from '../../../auth/services/auth.service';
import { SearchParamsDto } from '../dto/search-params.dto';
import { FEATURE_BRAND_GOVERNANCE } from '../../../constants/workspace.constants';

// Mock WorkspaceServiceSdk response
const mockWorkspaceServiceResponse = {
  result: [
    {
      id: 1,
      name: 'Test Workspace',
      logoUrl: 'test.png',
      isPrimary: false,
      organizationId: 'orgId',
    },
  ],
  pagination: { totalSize: 1, offset: 0, perPage: 10, nextOffset: 10 },
};

// Mock WorkspaceServiceSdk
const mockWorkspaceServiceSdk = {
  findAllUserWorkspaceForAllOrgsAsPromise: jest
    .fn()
    .mockResolvedValue(mockWorkspaceServiceResponse),
};

// Mock Mapper
const mockMapper = {
  mapArray: jest.fn().mockImplementation((source) => source),
};

describe('WorkspaceService', () => {
  let service: WorkspaceService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceService,
        { provide: OrganizationService, useValue: {} },
        { provide: WorkspaceServiceSdk, useValue: mockWorkspaceServiceSdk },
        { provide: AuthService, useValue: {} },
        { provide: 'automapper:nestjs:default', useValue: mockMapper },
      ],
    }).compile();

    service = module.get<WorkspaceService>(WorkspaceService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBrandGovernanceWorkspacesByOrganizationId', () => {
    it('should return paginated and filtered workspaces', async () => {
      const organizationId = 'orgId';
      const userId = 1;
      const paginationOptions = { offset: 0, perPage: 10 };
      const searchParams = new SearchParamsDto();

      const result = await service.getBrandGovernanceWorkspacesByOrganizationId(
        organizationId,
        userId,
        paginationOptions,
        searchParams,
      );

      expect(
        mockWorkspaceServiceSdk.findAllUserWorkspaceForAllOrgsAsPromise,
      ).toHaveBeenCalledWith(userId, organizationId, FEATURE_BRAND_GOVERNANCE);
      expect(result.items).toHaveLength(1);
      expect(result.totalCount).toEqual(1);
      expect(mockMapper.mapArray).toHaveBeenCalled();
    });
  });

  it('should throw an error when no workspaces with Brand Governance are found', async () => {
    const organizationId = 'orgId';
    const userId = 1;
    const paginationOptions = { offset: 0, perPage: 10 };
    const searchParams = new SearchParamsDto();

    // Mocking the service to return an empty result set
    mockWorkspaceServiceSdk.findAllUserWorkspaceForAllOrgsAsPromise.mockResolvedValue(
      {
        result: [], // No workspaces returned
        pagination: { totalSize: 0, offset: 0, perPage: 10, nextOffset: 0 },
      },
    );

    await expect(
      service.getBrandGovernanceWorkspacesByOrganizationId(
        organizationId,
        userId,
        paginationOptions,
        searchParams,
      ),
    ).rejects.toThrow(
      'This organization does not have any workspaces enabled with the Brand Governance feature.',
    );

    expect(
      mockWorkspaceServiceSdk.findAllUserWorkspaceForAllOrgsAsPromise,
    ).toHaveBeenCalledWith(userId, organizationId, FEATURE_BRAND_GOVERNANCE);
  });
});
