import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceController } from './workspace.controller';
import { WorkspaceService } from '../services/workspace.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { SearchParamsDto } from '../dto/search-params.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadWorkspaceDto } from '../dto/read-workspace.dto';

describe('WorkspaceController', () => {
  let controller: WorkspaceController;
  let service: jest.Mocked<WorkspaceService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceController],
      providers: [
        {
          provide: WorkspaceService,
          useValue: {
            getBrandGovernanceWorkspacesByOrganizationId: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<WorkspaceController>(WorkspaceController);
    service = module.get<WorkspaceService>(
      WorkspaceService,
    ) as jest.Mocked<WorkspaceService>;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getWorkspacesByOrganizationIdAndSearch', () => {
    it('should return workspaces successfully', async () => {
      const mockReq = { userId: 1 };
      const organizationId = '123';
      const searchParams: SearchParamsDto = { search: 'Workspace' };
      const paginationOptions: PaginationOptions = { offset: 0, perPage: 10 };

      const successResponse = {
        status: 'OK',
        result: [
          {
            id: 456,
            name: 'my workspace',
            logoUrl: 'https://vidmob.com/imageLogo.png',
          },
          {
            id: 123,
            name: 'my workspace 2',
            logoUrl: 'https://vidmob.com/imageLogo2.png',
          },
        ],
        pagination: { offset: 0, perPage: 10, nextOffset: 10, totalSize: 2 },
      };

      service.getBrandGovernanceWorkspacesByOrganizationId.mockResolvedValue(
        successResponse as any,
      );

      const result = await controller.getWorkspacesByOrganizationIdAndSearch(
        mockReq,
        organizationId,
        searchParams,
        paginationOptions,
      );

      expect(result).toEqual(successResponse);
      expect(
        service.getBrandGovernanceWorkspacesByOrganizationId,
      ).toHaveBeenCalledWith(
        organizationId,
        mockReq.userId,
        paginationOptions,
        searchParams,
      );
    });
  });
});
