import { <PERSON>, Get, Logger, Param, Request, Query } from '@nestjs/common';
import { ApiParam, ApiQuery, ApiTags, ApiSecurity } from '@nestjs/swagger';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { WorkspaceService } from '../services/workspace.service';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import { readWorkspaces } from '../../workspace/workspace.permissions';
import { SearchParamsDto } from '../dto/search-params.dto';

@ApiTags('Plugin Organization')
@ApiSecurity('Bearer Token')
@Controller('plugin')
export class WorkspaceController {
  private readonly logger = new Logger(WorkspaceService.name);

  constructor(private readonly workspaceService: WorkspaceService) {}

  /**
   * Get all workspaces with BRAND-GOVERNANCE feature by organization.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param search - The search param to filter workspaces by name.
   * @param offset - Determines the index of the first workspace to return.
   * @param perPage - Determines the number of workspaces to return.
   * @returns - The workspaces with BRAND-GOVERNANCE feature filtered by organization.
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization ID represents the unique identifier of the organization to which all workspaces belong.',
  })
  @ApiQuery({
    name: 'search',
    description: 'The search param to filter workspaces by name.',
    example: '?search=my cool Workspace',
  })
  @ApiQuery({
    name: 'offset',
    description: 'Determines the index of the first workspace to return.',
  })
  @ApiQuery({
    name: 'perPage',
    description: 'Determines the number of workspaces to return.',
  })
  @Permissions(readWorkspaces)
  @Get('organization/:organizationId/workspace')
  async getWorkspacesByOrganizationIdAndSearch(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Query() searchParams: SearchParamsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { userId } = req;

    return await this.workspaceService.getBrandGovernanceWorkspacesByOrganizationId(
      organizationId,
      userId,
      paginationOptions,
      searchParams,
    );
  }
}
