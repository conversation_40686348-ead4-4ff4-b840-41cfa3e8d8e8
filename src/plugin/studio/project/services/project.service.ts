import {
  Injectable,
  ForbiddenException,
  HttpStatus,
  BadRequestException,
  HttpException,
} from '@nestjs/common';
import { AuthorizationService } from '@vidmob/vidmob-authorization-service-sdk';
import { ConfigService } from '@nestjs/config';
import { catchError, map, of } from 'rxjs';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import axios from 'axios';
import { buildPaginationQueryParams } from '../services/service.utils';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ListProjectSuccessResponseDto } from '../dto/listProjectsSuccessResponse.dto';
import { ListProjectClientResponseDto } from '../dto/listProjectsClientResponse.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import {
  DEFAULT_BRIEF_PER_PAGE,
  DEFAULT_MESSAGE_OFFSET,
  DEFAULT_MESSAGE_PER_PAGE,
} from '../../../../constants/scoring.constants';
import { ProjectQuestionsDto } from '../dto/projectQuestions.dto';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';
import { DefaultService as MediaConversionService } from '@vidmob/vidmob-media-conversion-service-sdk';
import {
  MediaConversionJobState,
  MediaVariantUrlResponseDto,
} from '../dto/media-variant-url-response.dto';
import { ScorecardService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { REVIEW_STATUS_ACCEPTED } from '../../../../constants/studio.constants';
import { OutputVideoDto } from '../dto/output-video.dto';
import { getProjectInsightsQuery } from './project-insight-graph-ql-queries';
import { CreateAnnotationIterationMediaDto } from '../dto/create-annotation-iteration.dto';
import { CreateAnnotationIterationResponseDto } from '../dto/create-annotation-iteration-response.dto';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';
import { ProjectInsightsService } from '@vidmob/vidmob-soa-analytics-service-sdk';

@Injectable()
export class ProjectService {
  baseUrlApi: string;
  baseUrlApiInsight: string;

  constructor(
    private authorizationService: AuthorizationService,
    private configService: ConfigService,
    private readonly workspaceService: WorkspaceService,
    private readonly mediaConversionService: MediaConversionService,
    private readonly scorecardService: ScorecardService,
    private readonly projectInsightsService: ProjectInsightsService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.baseUrlApi = this.configService.get<string>('baseVidMobApiUrl', '');
    this.baseUrlApiInsight = this.configService.get<string>(
      'baseUrlApiInsight',
      '',
    );
  }

  async getEditorProjects(
    authorization: string,
    paginationOptions: PaginationOptions,
    status = '0,1,2',
    searchTerm?: string,
  ): Promise<PaginatedResultArray<ListProjectClientResponseDto>> {
    const userAuthorization = this.authorizationService.authorize({
      authorization,
    });

    return new Promise((resolve, reject) => {
      const person$ = userAuthorization.pipe(
        map((response) => response.data.result),
        catchError((error) => {
          console.error('Error in user authorization:', error);
          reject(error);
          return of(null);
        }),
      );

      person$.subscribe(async (person) => {
        if (!person) {
          reject('Authorization failed or returned null.');
          return;
        }

        // Build the query string
        const queryParams = new URLSearchParams({
          editorId: person.userId.toString(),
          status: status,
          searchTerm: searchTerm || '',
          extraFields:
            'partner,milestones,projectPreviewMedia,projectPreviewThumbnails',
          sortBy: 'id,name',
          sortDirection: 'DESC',
          useDescOrderFor: 'id',
        });

        const paginationQueryParams =
          buildPaginationQueryParams(paginationOptions);
        paginationQueryParams.forEach((value, key) =>
          queryParams.append(key, value),
        );

        const listProjectEndpoint = `${this.baseUrlApi}/api/v2/editor/${person.userId}/project?${queryParams}`;

        try {
          const response = await axios.get(listProjectEndpoint, {
            headers: { Authorization: authorization },
          });
          const projects = response.data.result;

          const enrichedProjects = await Promise.all(
            projects.map(async (project) => {
              const scoringEnabledResponse =
                await this.workspaceService.checkBrandGovernanceAsPromise(
                  project.partner.id,
                );
              const scoringEnabled =
                scoringEnabledResponse.result.brandGovernance;
              return { ...project, scoringEnabled };
            }),
          );

          const mappedProjects = enrichedProjects.map((project: any) =>
            this.mapper.map(
              project,
              ListProjectSuccessResponseDto,
              ListProjectClientResponseDto,
            ),
          );

          const total = response.data.pagination.totalSize;

          const projectsWithPagination =
            new PaginatedResultArray<ListProjectClientResponseDto>(
              mappedProjects,
              total,
            );

          resolve(projectsWithPagination);
        } catch (error) {
          console.error('Error in getProjects:', error);
          reject(error);
        }
      });
    });
  }

  async getProjectById(projectId: string, authorization: string) {
    const projectEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}`;

    const queryParams = new URLSearchParams({
      extraFields:
        'partner,milestones,projectPreviewMedia,projectPreviewThumbnails',
    });

    try {
      const response = await axios.get(`${projectEndpoint}?${queryParams}`, {
        headers: { Authorization: authorization },
      });

      const project = response.data.result;

      const scoringEnabledResponse =
        await this.workspaceService.checkBrandGovernanceAsPromise(
          project.partner.id,
        );
      const scoringEnabled = scoringEnabledResponse.result.brandGovernance;
      const enrichedProject = { ...project, scoringEnabled };

      return this.mapper.map(
        enrichedProject,
        ListProjectSuccessResponseDto,
        ListProjectClientResponseDto,
      );
    } catch (error) {
      const statusCode = error.response?.status || 500;
      const errorMessage =
        error.response?.data?.error || 'An unexpected error occurred';

      throw new HttpException(errorMessage, statusCode);
    }
  }

  async getIterationAnnotations(
    iterationMediaId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
  ) {
    const queryParams = new URLSearchParams({
      extraFields: 'attachments,streams,rawText',
      perPage: paginationOptions.perPage.toString(),
      offset: paginationOptions.offset.toString(),
    });

    const iterationAnnotationEndpoint = `${this.baseUrlApi}/api/v2/iteration/${iterationMediaId}/annotation?${queryParams}`;
    try {
      const response = await axios.get(iterationAnnotationEndpoint, {
        headers: { Authorization: authorization },
      });

      const annotationResponse = {
        annotations: response.data.result,
        pagination: response.data.pagination,
      };

      return annotationResponse;
    } catch (error) {
      return error;
    }
  }

  async getIterationMentions(
    iterationMediaId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
  ) {
    const queryParams = new URLSearchParams({
      perPage: paginationOptions.perPage.toString(),
      offset: paginationOptions.offset.toString(),
    });

    const iterationMentionEndpoint = `${this.baseUrlApi}/api/v2/iteration/${iterationMediaId}/mention?${queryParams}`;
    try {
      const response = await axios.get(iterationMentionEndpoint, {
        headers: { Authorization: authorization },
      });

      const mentionResponse = {
        mentions: response.data.result,
        pagination: response.data.pagination,
      };

      return {
        statusCode: response.status,
        data: { status: 'OK', result: mentionResponse },
      };
    } catch (error) {
      return {
        statusCode: error.response.status,
        data: error.response.data.error,
      };
    }
  }

  async createIterationMediaAnnotation(
    iterationMediaId: number,
    dto: CreateAnnotationIterationMediaDto,
    authorization: string,
  ): Promise<CreateAnnotationIterationResponseDto> {
    if (!dto.startTime && !dto.text) {
      throw new BadRequestException(
        'Either startTime or text must be provided for creating an annotation.',
      );
    }

    const iterationAnnotationEndpoint = `${this.baseUrlApi}/api/v2/iteration/${iterationMediaId}/annotation`;
    try {
      const { data } = await axios.post(iterationAnnotationEndpoint, dto, {
        headers: { Authorization: authorization },
      });

      return data;
    } catch (error) {
      return error;
    }
  }

  async getProjectBriefList(
    projectId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
  ) {
    const queryParams = new URLSearchParams({
      extraFields: 'uploader',
      perPage: DEFAULT_BRIEF_PER_PAGE.toString(),
      offset: paginationOptions.offset.toString(),
    });

    const projectBriefEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/projectBrief?projectId=${projectId}&${queryParams}`;

    try {
      const response = await axios.get(projectBriefEndpoint, {
        headers: { Authorization: authorization },
      });

      const questionAnswers = await this.fetchProjectQuestionAnswers(
        projectId,
        authorization,
      );

      const briefResponse = {
        questionAnswers: questionAnswers,
        projectBrief: response.data.result,
        pagination: response.data.pagination,
      };

      return briefResponse;
    } catch (error) {
      throw new ForbiddenException(error);
    }
  }

  async fetchProjectQuestionAnswers(
    projectId: number,
    authorization: string,
  ): Promise<ProjectQuestionsDto[]> {
    const questionAnswerEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/questionAnswer?projectId=${projectId}&extraFields=attachments`;

    try {
      const response = await axios.get(questionAnswerEndpoint, {
        headers: { Authorization: authorization },
      });

      const projectQuestions = await Promise.all(
        response.data.result.map(async (question: any) => {
          let questionAttachments = null;

          if (question.allowAttachments) {
            const attachmentsResponse = await this.getProjectQuestionAttachment(
              projectId,
              question.id,
              authorization,
            );
            questionAttachments = attachmentsResponse.result.map((att) => ({
              id: att.media.id,
              name: att.media.name,
              downloadUrl: att.media.downloadUrl,
            }));
          }

          const mappedQuestion = {
            id: question.id,
            topic: question.topic,
            text: question.text,
            lastUpdatedDate: question.answers?.[0]?.lastUpdated || null,
            user: question.answers?.[0]?.user
              ? {
                  id: question.answers[0].user.id,
                  displayName: question.answers[0].user.displayName,
                }
              : null,
            questionType: question.questionType,
            answer:
              question.questionType === 'TEXT' && question.answers.length > 0
                ? question.answers[0].answer
                : '',
            answers: [],
            allowAttachments: question.allowAttachments || false,
            media: questionAttachments || [],
          };

          if (question.questionType === 'MULTIPLE_CHOICE') {
            question.questionOptionSets?.forEach((set) => {
              const selectedOptions = question.answers.flatMap((answer) =>
                answer.answerOptions
                  .map(
                    (option) =>
                      set.questionOptions.find(
                        (qo) => qo.id === option.questionOptionId,
                      )?.text,
                  )
                  .filter((opt) => opt),
              );
              if (selectedOptions.length > 0) {
                mappedQuestion.answers.push({
                  topic: set.text,
                  answer: [...new Set(selectedOptions)],
                });
              }
            });
          }

          return mappedQuestion;
        }),
      );

      return projectQuestions;
    } catch (error) {
      console.error('Error fetching project question answers:', error);
      throw new ForbiddenException('Failed to fetch project question answers');
    }
  }

  async getProjectQuestionAttachment(
    projectId: number,
    questionId: number,
    authorization: string,
  ) {
    const projectQuestionAttachmentEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/questionAttachment/${questionId}`;

    try {
      const response = await axios.get(projectQuestionAttachmentEndpoint, {
        headers: { Authorization: authorization },
      });

      return response.data;
    } catch (error) {
      return {
        data: error.response
          ? error.response.data
          : 'An unexpected error occurred',
        statusCode: error.response
          ? error.response.status
          : HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getProjectOutputGroup(
    projectId: number,
    authorization: string,
    paginationOptions?: PaginationOptions,
  ) {
    const searchParams = new URLSearchParams({
      projectId: projectId.toString(),
      perPage: paginationOptions?.perPage?.toString(),
      offset: paginationOptions?.offset?.toString(),
    });
    const projectOutputGroupEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/outputGroup?${searchParams}`;
    const outputVideoEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/outputVideo?projectId=${projectId}&extraFields=format,acceptedIterationMedia,mostRecentIterationMedia,variation,unreadIterationCounts,totalUnreadAnnotations&page=1&perPage=100`;

    try {
      const [groupResponse, videosResponse] = await Promise.all([
        axios.get(projectOutputGroupEndpoint, {
          headers: { Authorization: authorization },
        }),
        axios.get(outputVideoEndpoint, {
          headers: { Authorization: authorization },
        }),
      ]);

      const videoDetails = videosResponse.data.result;

      const transformedOutputGroups = await Promise.all(
        groupResponse.data.result.map(async (group: any) => {
          const outputVideos = await Promise.all(
            videoDetails
              .filter(
                (video: OutputVideoDto) => video.outputGroupId === group.id,
              )
              .map(async (video: OutputVideoDto) => {
                let mediaVariantUrl = null;

                try {
                  mediaVariantUrl =
                    (await this.getMediaVariantUrl(
                      projectId,
                      group?.sourceMedia?.id,
                    )) ||
                    group?.sourceMedia?.url ||
                    null;
                } catch (error) {
                  null;
                }

                return {
                  id: video.id,
                  outputType: video.outputType,
                  name: video.name || null,
                  mediaType: video.format.mediaType,
                  aspectRatio: video.format.spec.label,
                  formatDescrition: video.format.description,
                  url: mediaVariantUrl || group?.sourceMedia?.url || null,
                  placement: video.format.name || null,
                  channel: video.format.category.name.toUpperCase() || null,
                  variationType: video.variation?.variationType?.name || null,
                  currentIteration: video?.mostRecentIterationMedia || null,
                  totalUnreadAnnotations: video.totalUnreadAnnotations,
                };
              }),
          );

          const outputGroup: any = {
            id: group.id,
            name: group.name || null,
            outputGroupType: group.outputGroupType,
            outputVideos: outputVideos,
          };

          if (group.outputGroupType === 'VARIATION_ONLY') {
            outputGroup.sourceMedia = group?.sourceMedia;
          }

          return outputGroup;
        }),
      );

      return {
        outputGroups: transformedOutputGroups,
        pagination: groupResponse.data.pagination,
      };
    } catch (error) {
      return {
        data: error.response
          ? error.response.data
          : 'An unexpected error occurred',
        statusCode: error.response ? error.response.status : 500,
      };
    }
  }

  async getMediaVariantUrl(
    projectId: number,
    mediaId: number,
  ): Promise<string | null> {
    if (!mediaId) {
      return null;
    }

    try {
      const response =
        (await this.mediaConversionService.mediaVariantControllerGetMediaVariantUrlAsPromise(
          projectId,
          mediaId,
        )) as { status: string; result: MediaVariantUrlResponseDto };

      const mediaVariantUrl = response?.result?.mediaVariantUrl || null;
      const status = response?.result?.status;

      if (status === MediaConversionJobState.SUCCEEDED) {
        return mediaVariantUrl;
      } else {
        return null;
      }
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return null;
      } else {
        throw error;
      }
    }
  }

  async getProjectAssetsAndFolders(
    projectId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    searchTerm?: string,
  ) {
    const projectAssetFolderEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/folder`;

    let folderId: any;

    try {
      const folderResponse = await axios.get(projectAssetFolderEndpoint, {
        headers: { Authorization: authorization },
      });
      folderId = folderResponse.data.result.id;
    } catch (error) {
      console.error('Error fetching project asset folder:', error);
      throw new ForbiddenException('Failed to retrieve project asset folder.');
    }

    let folders = [];
    if (folderId) {
      const folderListEndpoint = `${this.baseUrlApi}/api/v2/folder/${folderId}/list`;
      try {
        const response = await axios.get(folderListEndpoint, {
          headers: { Authorization: authorization },
        });
        folders = response.data.result;
      } catch (error) {
        console.error('Error fetching folder list:', error);
        throw new ForbiddenException('Failed to retrieve folder list.');
      }
    }

    let assets = [];
    let pagination = {};

    if (folderId) {
      const assetListEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/media?folderId=${folderId}&extraFields=bundleInfo,uploader,streams&perPage=${paginationOptions.perPage}&offset=${paginationOptions.offset}`;

      try {
        const response = await axios.get(assetListEndpoint, {
          headers: { Authorization: authorization },
        });
        assets = response.data.result;
        pagination = response.data.pagination;
      } catch (error) {
        console.error('Error fetching asset list:', error);
        throw new ForbiddenException('Failed to retrieve asset list.');
      }
    }

    if (searchTerm) {
      folders = folders.filter((folder) =>
        folder.name.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      assets = assets.filter((asset) =>
        asset.media.name.toLowerCase().includes(searchTerm.toLowerCase()),
      );
    }

    pagination = {
      offset: pagination['offset'],
      perPage: pagination['perPage'],
      nextOffset: Math.min(
        pagination['offset'] + pagination['perPage'],
        assets.length,
      ),
      totalSize: assets.length,
    };

    return { folders, assets, pagination };
  }

  async getNestedFolderContents(
    projectId: number,
    folderId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    searchTerm?: string,
  ) {
    const folderListEndpoint = `${this.baseUrlApi}/api/v2/folder/${folderId}/list`;
    const assetListEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}/media?folderId=${folderId}&extraFields=bundleInfo,uploader,streams&perPage=${paginationOptions.perPage}&offset=${paginationOptions.offset}`;
    let folders = null;

    // Fetch folders
    folders = await axios
      .get(folderListEndpoint, {
        headers: { Authorization: authorization },
      })
      .then((response) => response.data.result)
      .catch((error) => {
        console.error('Error fetching nested folder list:', error);
        throw new ForbiddenException('Failed to retrieve nested folder list.');
      });

    // Fetch assets
    const assets = await axios
      .get(assetListEndpoint, {
        headers: { Authorization: authorization },
      })
      .then((response) => {
        return {
          assets: response.data.result,
          pagination: response.data.pagination,
        };
      })
      .catch((error) => {
        console.error('Error fetching asset list in nested folder:', error);
        throw new ForbiddenException(
          'Failed to retrieve asset list in nested folder.',
        );
      });

    if (searchTerm) {
      const lowerSearch = searchTerm.toLowerCase();
      folders = folders.filter((folder) =>
        folder.name.toLowerCase().includes(lowerSearch),
      );
      assets.assets = assets.assets.filter((asset) =>
        asset.media.name.toLowerCase().includes(lowerSearch),
      );

      // Recalculate pagination after filtering
      assets.pagination = {
        offset: paginationOptions.offset,
        perPage: paginationOptions.perPage,
        nextOffset: Math.min(
          paginationOptions.offset + paginationOptions.perPage,
          assets.assets.length,
        ),
        totalSize: assets.assets.length,
      };
    }

    return { folders, assets: assets.assets, pagination: assets.pagination };
  }

  async getProjectInsightsByProjectId(projectId: number) {
    const projectInsightsResponse =
      await this.projectInsightsService.getProjectInsightsAsPromise(
        projectId,
        0,
        Number.MAX_SAFE_INTEGER,
      );
    return projectInsightsResponse;
  }

  async getProjectInsights(projectId: number, authorization: string) {
    const projectQueryParams = new URLSearchParams({
      projectId: projectId.toString(),
      extraFields: 'partner',
    });

    const projectEndpoint = `${this.baseUrlApi}/api/v2/project/${projectId}?${projectQueryParams}`;

    try {
      const projectResponse = await axios.get(projectEndpoint, {
        headers: { Authorization: authorization },
      });

      const { partner } = projectResponse.data.result;
      const partnerId = partner.id;
      const organizationId = partner.organizationId;

      const insightsQueryParams = {
        query: getProjectInsightsQuery,
        variables: {
          organizationId: organizationId,
          workspaceIds: [partnerId],
          projectId: projectId,
        },
      };

      const insightsResponse = await axios.post(
        `${this.baseUrlApiInsight}/insights/v1/project-insights`,
        insightsQueryParams,
        {
          headers: { Authorization: authorization },
        },
      );

      return insightsResponse.data;
    } catch (error) {
      throw new Error(
        `Failed to fetch insights for project ${projectId}: ${error.message}`,
      );
    }
  }

  getProjectOutputGroupMock() {
    const mockData = {
      outputGroups: [
        {
          id: 11315,
          outputGroupType: 'HERO_AND_VARIATION',
          name: 'The best output group',
          outputVideos: [
            {
              id: 51473,
              outputType: 'HERO',
              mediaVariantUrl:
                'https://media.istockphoto.com/id/1321482127/video/cute-little-bichon-frise-running-through-house.mp4?s=mp4-640x640-is&k=20&c=fDb8bwEc6bj_YyUdoXZTXs607YEdP8g6X5w_icWpUb0=',
              channel: 'INSTAGRAM',
              placement: 'In Feed (4:5)',
              name: 'This is an Instagram output',
            },
            {
              id: 51474,
              outputType: 'VARIATION',
              mediaVariantUrl:
                'https://media.istockphoto.com/id/**********/photo/the-artist-draws-anime-comics-on-paper-storyboard-for-the-cartoon-the-illustrator-creates.jpg?s=612x612&w=is&k=20&c=aX6WneD2jDr5962etC5feRL75KvnHsVQ1Zn-k2-NVAs=',
              channel: 'FACEBOOK',
              placement: 'In Stream 2:3',
              name: 'This is a Facebook output',
              variationType: 'Cut down',
            },
            {
              id: 51475,
              outputType: 'VARIATION',
              mediaVariantUrl: null,
              channel: 'PINTEREST',
              placement: 'Collection 1:1',
              name: 'This is not a Facebook output',
              variationType: 'Format Change',
            },
          ],
        },
        {
          id: 11316,
          outputGroupType: 'VARIATION_ONLY',
          name: 'The best variation group',
          sourceMedia: {
            name: 'CoolGif.gif',
            mediaUrl:
              'https://media.istockphoto.com/id/**********/video/t-l-top-view-of-trucks-entering-container-terminal.mp4?s=mp4-640x640-is&k=20&c=yOQyXG0-qK-t9v6gzGCuNbC2fczLNgMiN_zoiYjDJB8=',
            downloadUrl:
              'https://media.istockphoto.com/id/**********/video/t-l-top-view-of-trucks-entering-container-terminal.mp4?s=mp4-640x640-is&k=20&c=yOQyXG0-qK-t9v6gzGCuNbC2fczLNgMiN_zoiYjDJB8=',
            fileType: 'IMAGE',
            aspectRatio: '1:2',
          },
          outputVideos: [
            {
              id: 51476,
              channel: 'FACEBOOK',
              placement: 'In Stream 2:3',
              name: 'This is a different Facebook output',
              mediaVariantUrl:
                'https://media.istockphoto.com/id/1457472452/video/festival-love.mp4?s=mp4-640x640-is&k=20&c=a0MDHRaaxNYjTPX6aFz9mM6WO_R58xbn6-TQaBOFoas=',
              variationType: 'Simple swap',
            },
            {
              id: 51478,
              outputType: 'VARIATION',
              mediaVariantUrl: null,
              channel: 'PINTEREST',
              placement: 'Collection 1:1',
              name: 'This is a Pinterest output',
              variationType: 'Simple swap',
            },
          ],
        },
      ],
    };

    return {
      outputGroups: mockData.outputGroups,
      pagination: {
        nextOffset: null,
        offset: 0,
        perPage: 10,
        totalSize: 2,
      },
    };
  }

  async getOutputVideoIterationMedia(
    outputVideoId: number,
    authorization: string,
    paginationOptions?: PaginationOptions,
  ) {
    const searchParams = new URLSearchParams({
      outputVideoId: outputVideoId.toString(),
      extraFields:
        'iteration,media,reviewStatus,context,outputVideo,unreadAnnotationCount',
      perPage: paginationOptions?.perPage?.toString(),
      offset: paginationOptions?.offset?.toString(),
    });

    const outputVideoIterationMediaEndpoint = `${this.baseUrlApi}/api/v2/outputVideo/${outputVideoId}/iterationMedia?${searchParams}`;

    try {
      const response = await axios.get(outputVideoIterationMediaEndpoint, {
        headers: { Authorization: authorization },
      });

      const iterationMedias = response.data.result;

      const transformedIterationMedia = await Promise.all(
        iterationMedias.map(async (iterationMedia: any) => {
          const mediaId = iterationMedia.media.id;
          const scorecardResponse =
            await this.scorecardService.getScorecardByMediaIdAsPromise(mediaId);

          const scorecard = scorecardResponse.result;
          const projectId = iterationMedia.outputVideo.projectId;

          let isOutdated = 0;

          if (scorecard) {
            const response: AxiosResponse = await firstValueFrom(
              this.scorecardService.getScorecard(Number(scorecard?.id)),
            );

            isOutdated = response.data.result.isOutdated;
          }

          let mediaVariantUrl = null;

          try {
            mediaVariantUrl = await this.getMediaVariantUrl(projectId, mediaId);
          } catch (error) {
            null;
          }

          if (!mediaVariantUrl) {
            mediaVariantUrl = iterationMedia.media.url || null;
          }

          return {
            id: iterationMedia.id,
            mediaId: mediaId,
            scorecardId: Number(scorecard?.id) || null,
            url: mediaVariantUrl,
            reviewStatus: iterationMedia.accepted
              ? REVIEW_STATUS_ACCEPTED
              : iterationMedia.reviewStatus,
            accepted: iterationMedia.accepted,
            name: iterationMedia.media.name,
            score: scorecard?.score || null,
            iteration: iterationMedia.iteration,
            iterationVersion: iterationMedia.iterationVersion,
            isFeedbackComplete: iterationMedia.isFeedbackComplete,
            scorecardStatus: scorecard?.status || null,
            iterationMedia: iterationMedia.media.iterationMedia,
            mediaProcessingState: iterationMedia.media.processingState,
            fileType: iterationMedia.media.fileType,
            unreadAnnotationCount: iterationMedia.unreadAnnotationCount,
            isOutdated: isOutdated,
          };
        }),
      );

      return {
        iterationMedia: transformedIterationMedia,
        pagination: response.data.pagination,
      };
    } catch (error) {
      throw new ForbiddenException(error);
    }
  }

  async getProjectMessageChannel(projectId: number, authorization: string) {
    const messageChannelEndpoint = `${this.baseUrlApi}/api/v2/messageChannel`;
    const messageEndpoint = (
      messageChannelId: number,
      offset = 0,
      perPage = 10,
    ) =>
      `${this.baseUrlApi}/api/v2/messageChannel/${messageChannelId}/message?extraFields=attachments,rawText&messageChannelId=${messageChannelId}&offset=${offset}&perPage=${perPage}`;

    try {
      const channelClientResponse = await axios.get(messageChannelEndpoint, {
        headers: { Authorization: authorization },
        params: {
          projectId: projectId,
          type: 'PROJECT_EDITOR',
        },
      });
      const channelVidmobResponse = await axios.get(messageChannelEndpoint, {
        headers: { Authorization: authorization },
        params: {
          projectId: projectId,
          type: 'ACCOUNT_MANAGER',
        },
      });

      const messageChannels = [
        ...[channelClientResponse.data.result],
        ...[channelVidmobResponse.data.result],
      ];

      const enrichedMessageChannels = [];

      for (const channel of messageChannels) {
        const {
          id,
          notifyEmail,
          notifyPush,
          participants,
          projectId,
          readOnly,
          type,
          unreadMessages,
        } = channel;

        let offset = DEFAULT_MESSAGE_OFFSET;
        const perPage = DEFAULT_MESSAGE_PER_PAGE;
        let messages = [];
        let moreMessages = true;

        while (moreMessages) {
          const messageResponse = await axios.get(
            messageEndpoint(id, offset, perPage),
            {
              headers: { Authorization: authorization },
            },
          );

          const messageData = messageResponse.data.result;

          messages = messages.concat(
            messageData.map((message: any) => ({
              id: message.id,
              attachmentIds: message.attachments?.map((att: any) => att.id),
              date: message.date,
              fromUser: {
                id: message.from.id,
                displayName: message.from.displayName,
                jobTitle: message.from.jobTitle,
                photo: message.from.photo,
                color: message.from.color,
              },
              isCurrentUser: message.isCurrentUser,
              messageChannelId: message.messageChannelId,
              projectId: message.projectId,
              read: message.read,
              text: message.text,
              type: message.type,
            })),
          );

          moreMessages = messageData.length === perPage;
          offset += perPage;
        }

        enrichedMessageChannels.push({
          id: id,
          notifyEmail,
          notifyPush,
          participants: participants.map((participant) => ({
            id: participant.id,
            displayName: participant.displayName,
            type: participant.type,
            lastReadMessageId: participant.lastReadMessageId,
          })),
          projectId,
          readOnly,
          type,
          unreadMessages,
          messages,
        });
      }

      return enrichedMessageChannels.map((channel) => ({
        id: channel.id,
        notifyEmail: channel.notifyEmail,
        notifyPush: channel.notifyPush,
        participants: channel.participants,
        projectId: channel.projectId,
        readOnly: channel.readOnly,
        type: channel.type,
        unreadMessages: channel.unreadMessages,
        messages: channel.messages,
      }));
    } catch (error) {
      throw new ForbiddenException(error);
    }
  }
}
