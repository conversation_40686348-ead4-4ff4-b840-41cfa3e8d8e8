import {
  errorText,
  fullInsightResultText,
  recommendationInsightResultText,
} from '../../../../constants/studio.constants';

export const linkInsightsToProjectQuery = `
  mutation MyMutation ($organizationId: String!, $workspaceIds: [Int]!, $projectId: Int!, $insightIds: [String]!, $recommendedFrom: String) {
    linkProjectInsight (organizationId: $organizationId, workspaceIds: $workspaceIds, projectId: $projectId, insightIds: $insightIds, recommendedFrom: $recommendedFrom) {
        error {
            ${errorText}
        }
        message
        result { 
            projectId
            processedInsightIds
            unprocessedInsightIds
        }
        status
        traceId
    }
  }
`;

export const unlinkInsightsFromProjectQuery = `
  mutation MyMutation ($organizationId: String!, $workspaceIds: [Int]!, $projectId: Int!, $insightIds: [String]!) {
    unlinkProjectInsight (organizationId: $organizationId, workspaceIds: $workspaceIds, projectId: $projectId, insightIds: $insightIds) {
        error {
            ${errorText}
        }
        message
        result { 
            projectId
            processedInsightIds
            unprocessedInsightIds
        }
        status
        traceId
    }
  }
`;

export const getProjectInsightsQuery = `
  query MyQuery ($organizationId: String!, $workspaceIds: [Int]!, $projectId: Int!) {
    getProjectInsights (organizationId: $organizationId, workspaceIds: $workspaceIds, projectId: $projectId) {
        error {
            ${errorText}
        }
        message
        result {
            projectId
            insights {
                ${fullInsightResultText}
            }
        }
        status
        traceId
    }
  }
`;

export const getInsightRecommendationsQuery = `
  query MyQuery ($getProjectBriefRecommendationsRequest: GetProjectBriefRecommendationsRequest) {
    getProjectBriefRecommendations(getProjectBriefRecommendationsRequest: $getProjectBriefRecommendationsRequest) {
        error {
            ${errorText}
        }
        message
        result {
            ${recommendationInsightResultText}
        }
        status
        traceId
      }
  }
`;
