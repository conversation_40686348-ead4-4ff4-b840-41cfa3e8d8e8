import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

export function buildPaginationQueryParams(
  paginationOptions: PaginationOptions,
): URLSearchParams {
  const queryParams = new URLSearchParams();
  if (paginationOptions.perPage !== undefined) {
    queryParams.append('perPage', paginationOptions.perPage.toString());
  }
  if (paginationOptions.offset !== undefined) {
    queryParams.append('offset', paginationOptions.offset.toString());
  }
  if (paginationOptions.queryId !== undefined) {
    queryParams.append('queryId', paginationOptions.queryId.toString());
  }
  return queryParams;
}
