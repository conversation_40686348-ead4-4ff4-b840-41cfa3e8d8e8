import { Test, TestingModule } from '@nestjs/testing';
import { ProjectService } from './project.service';
import { ResultService } from '../../../scoring/result/services/result.service';
import { AuthorizationService } from '@vidmob/vidmob-authorization-service-sdk';
import { DefaultService as MediaConversionService } from '@vidmob/vidmob-media-conversion-service-sdk';
import { ConfigService } from '@nestjs/config';
import { of } from 'rxjs';
import axios from 'axios';
import { ForbiddenException, HttpException, HttpStatus } from '@nestjs/common';
import { ProjectController } from '../controllers/project.controller';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';
import { ScorecardService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { getProjectInsightsQuery } from './project-insight-graph-ql-queries';
import { ListProjectSuccessResponseDto } from '../dto/listProjectsSuccessResponse.dto';
import { ListProjectClientResponseDto } from '../dto/listProjectsClientResponse.dto';
import { ProjectInsightsService } from '@vidmob/vidmob-soa-analytics-service-sdk';

jest.mock('axios');

const mockMapper = {
  map: jest.fn(),
  mapArray: jest.fn(),
};

const res: Response = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
} as any;

describe('ProjectService', () => {
  let controller: ProjectController;
  let service: ProjectService;
  const mockAxios = axios as jest.Mocked<typeof axios>;

  let mockAuthorizationService: Partial<AuthorizationService>;
  let mockResultService: Partial<ResultService>;
  let mockConfigService: Partial<ConfigService>;
  let workspaceService: Partial<WorkspaceService>;
  let mockMediaConversionService: Partial<MediaConversionService>;
  let mockScorecardService: Partial<ScorecardService>;
  let mockProjectInsightsService: Partial<ProjectInsightsService>;

  beforeEach(async () => {
    // Mock AuthorizationService
    mockAuthorizationService = {
      authorize: jest
        .fn()
        .mockReturnValue(of({ data: { result: { userId: '123' } } })),
    };

    jest.mock('axios');

    // Mock ConfigService
    mockConfigService = {
      get: jest.fn().mockReturnValue('http://example.com'),
    };

    mockMediaConversionService = {
      mediaVariantControllerGetMediaVariantUrlAsPromise: jest
        .fn()
        .mockReturnValue(null),
    };

    mockScorecardService = {
      getScorecardByMediaIdAsPromise: jest.fn().mockReturnValue(null),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProjectController],
      providers: [
        ProjectService,
        {
          provide: AuthorizationService,
          useValue: mockAuthorizationService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'automapper:nestjs:default',
          useValue: mockMapper,
        },
        {
          provide: MediaConversionService,
          useValue: mockMediaConversionService,
        },
        {
          provide: ScorecardService,
          useValue: mockScorecardService,
        },
        {
          provide: ResultService,
          useValue: mockResultService,
        },
        {
          provide: ProjectInsightsService,
          useValue: mockProjectInsightsService,
        },
        {
          provide: WorkspaceService,
          useValue: {
            getWorkspace: jest.fn(),
            checkBrandGovernanceAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ProjectService>(ProjectService);
    controller = module.get<ProjectController>(ProjectController);
    workspaceService = module.get(WorkspaceService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getEditorProjects', () => {
    it('should make an HTTP request and return project data', async () => {
      const mockProjects = [
        { id: 1, name: 'Project 1', status: 0, partner: { id: 100 } },
      ];
      const mockPagination = {
        offset: 0,
        perPage: 10,
        nextOffset: 10,
        totalSize: 1,
      };
      jest
        .spyOn(workspaceService as any, 'checkBrandGovernanceAsPromise')
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            brandGovernance: true,
          },
        });

      jest.spyOn(service as any, 'getEditorProjects').mockResolvedValueOnce({
        status: 'OK',
        result: [
          {
            id: 123,
            name: 'Adobe Plugin Project Test',
            status: 2,
            workspaceId: 1234,
            scoringEnabled: true,
            milestoneIdentifier: 'CREATOR_EDITOR_DRAFT_BEING_REVIEWED',
            milestoneText: 'Draft is being reviewed.',
            projectPreviewMedia:
              'https://vidmob-storage-dev/some-awesome-media.png',
            previewMediaDownloadUrl:
              'https://vidmob-storage-dev/some-awesome-media-to-download.png',
            projectThumbnailUrl:
              'https://vidmob-storage-dev/some-awesome-thumbnail.png',
          },
        ],
        pagination: mockPagination,
      });

      const authorization = 'Bearer mock-token';
      const paginationOptions: PaginationOptions = {
        perPage: 10,
        offset: 0,
        queryId: 'query123',
      };

      const response: any = await service.getEditorProjects(
        authorization,
        paginationOptions,
      );

      expect(response.result).toBeInstanceOf(Object);
      expect(response.result[0]).toHaveProperty('id');
      expect(response.result[0]).toHaveProperty('name');
      expect(response.result[0]).toHaveProperty('status');
      expect(response.result[0]).toHaveProperty('workspaceId');
      expect(response.result[0]).toHaveProperty('scoringEnabled');
      expect(response.result[0]).toHaveProperty('milestoneIdentifier');
      expect(response.result[0]).toHaveProperty('milestoneText');
      expect(response.result[0]).toHaveProperty('projectPreviewMedia');
      expect(response.result[0]).toHaveProperty('previewMediaDownloadUrl');
      expect(response.result[0]).toHaveProperty('projectThumbnailUrl');

      expect(response.pagination).toBeInstanceOf(Object);
      expect(response.pagination).toHaveProperty('offset');
      expect(response.pagination).toHaveProperty('perPage');
      expect(response.pagination).toHaveProperty('nextOffset');
      expect(response.pagination).toHaveProperty('totalSize');
    });
  });

  describe('getProjectById', () => {
    it('should return project details with enriched data', async () => {
      const mockProjectId = '123';
      const mockAuthorization = 'Bearer mock-token';
      const mockResponse = {
        data: {
          result: {
            id: 123,
            name: 'Adobe Plugin Project Test',
            partner: { id: 1234 },
          },
        },
      };

      jest
        .spyOn(workspaceService as any, 'checkBrandGovernanceAsPromise')
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            brandGovernance: true,
          },
        });

      jest.spyOn(service as any, 'getProjectById').mockResolvedValueOnce({
        status: 'OK',
        result: {
          id: 123,
          name: 'Adobe Plugin Project Test',
          status: 2,
          workspaceId: 1234,
          scoringEnabled: true,
          milestoneIdentifier: 'CREATOR_EDITOR_DRAFT_BEING_REVIEWED',
          milestoneText: 'Draft is being reviewed.',
          projectPreviewMedia:
            'https://vidmob-storage-dev/some-awesome-media.png',
          previewMediaDownloadUrl:
            'https://vidmob-storage-dev/some-awesome-media-to-download.png',
          projectThumbnailUrl:
            'https://vidmob-storage-dev/some-awesome-thumbnail.png',
        },
      });

      const response: any = await service.getProjectById(
        mockProjectId,
        mockAuthorization,
      );

      const result = response.result;

      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(Object);
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('name');
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('workspaceId');
      expect(result).toHaveProperty('scoringEnabled');
      expect(result).toHaveProperty('milestoneIdentifier');
      expect(result).toHaveProperty('milestoneText');
      expect(result).toHaveProperty('projectPreviewMedia');
      expect(result).toHaveProperty('previewMediaDownloadUrl');
      expect(result).toHaveProperty('projectThumbnailUrl');
    });

    it('should throw HttpException on error', async () => {
      const mockProjectId = '123';
      const mockAuthorization = 'Bearer mock-token';
      const mockError = { message: 'Forbidden', response: { status: 403 } };

      mockAxios.get.mockRejectedValue(mockError);

      await expect(
        service.getProjectById(mockProjectId, mockAuthorization),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getProjectBriefList', () => {
    const projectId = 123;
    const authorization = 'Bearer mock-token';
    const paginationOptions = { perPage: 50, offset: 0, queryId: 'query123' };

    const queryParams = new URLSearchParams({
      extraFields: 'uploader',
      perPage: paginationOptions.perPage.toString(),
      offset: paginationOptions.offset.toString(),
    });

    const projectBriefEndpoint = `http://example.com/api/v2/project/${projectId}/projectBrief?projectId=${projectId}&${queryParams}`;

    it('should successfully retrieve project briefs', async () => {
      const mockQuestionAnswers: any = [
        {
          topic: 'Example Topic',
          text: 'Example question text',
          lastUpdatedDate: new Date().toISOString(),
          user: { id: 1, displayName: 'Test User' },
          questionType: 'TEXT',
          answer: 'Example answer',
          answers: [],
          allowAttachments: false,
          media: [],
        },
      ];
      const mockResponse = {
        data: {
          pagination: 'any',
          projectBrief: undefined,
          questionAnswers: mockQuestionAnswers,
        },
      };
      (axios.get as jest.Mock).mockResolvedValue(mockResponse);

      jest
        .spyOn(service, 'fetchProjectQuestionAnswers')
        .mockResolvedValue(mockQuestionAnswers);

      const result = await service.getProjectBriefList(
        projectId,
        authorization,
        paginationOptions,
      );

      expect(result).toEqual(mockResponse.data);

      expect(axios.get).toHaveBeenCalledWith(projectBriefEndpoint, {
        headers: { Authorization: authorization },
      });
    });

    it('should throw a ForbiddenException on error', async () => {
      const mockError = new Error('Forbidden');
      const paginationOptions = { perPage: 10, offset: 0, queryId: 'query123' };
      (axios.get as jest.Mock).mockRejectedValue(mockError);

      await expect(
        service.getProjectBriefList(
          projectId,
          authorization,
          paginationOptions,
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('getProjectBriefs', () => {
    it('should return project briefs for a given project ID', async () => {
      const projectId = 123;
      const mockAuthorization = 'Bearer mock-token';

      const paginationOptions = { perPage: 10, offset: 0, queryId: 'query123' };

      const mockQuestionAnswers: any = [
        {
          topic: 'Example Topic',
          text: 'Example question text',
          lastUpdatedDate: new Date().toISOString(),
          user: { id: 1, displayName: 'Test User' },
          questionType: 'TEXT',
          answer: 'Example answer',
          answers: [],
          allowAttachments: false,
          media: [],
        },
      ];

      const mockResponse = {
        questionAnswers: mockQuestionAnswers,
        projectBrief: 'any',
        pagination: 'any',
      };

      jest
        .spyOn(service, 'getProjectBriefList')
        .mockResolvedValue(mockResponse);

      const req = {
        headers: {
          authorization: mockAuthorization,
        },
      };

      const result = await controller.getProjectBriefs(
        projectId,
        req,
        paginationOptions,
      );

      expect(result).toEqual(mockResponse);
      expect(service.getProjectBriefList).toHaveBeenCalledWith(
        projectId,
        mockAuthorization,
        paginationOptions,
      );
    });
  });

  describe('getProjectOutputGroup', () => {
    const mockedAxios = axios as jest.Mocked<typeof axios>;

    const mockProjectOutputGroupResponse = {
      data: {
        result: [
          {
            id: 1,
            name: 'Output 1',
            outputGroupType: 'type1',
            sourceMedia: { id: 1, url: 'http://example.com/video1' },
          },
          {
            id: 2,
            name: 'Output 2',
            outputGroupType: 'type2',
            sourceMedia: { id: 2, url: 'http://example.com/video2' },
          },
        ],
        pagination: { total: 2, perPage: 10, offset: 0 },
      },
      status: HttpStatus.OK,
    };

    const mockOutputVideoResponse = {
      data: {
        result: [
          {
            id: 101,
            outputGroupId: 1,
            outputType: 'type1',
            format: {
              mediaType: 'video',
              name: 'Format 1',
              category: { name: 'PLATFORM1' },
              spec: { label: '16:9' },
              description: 'Format 1',
            },
            mostRecentIterationMedia: {
              media: { url: 'http://example.com/video1' },
            },
            variation: { variationType: { name: 'type1' } },
          },
          {
            id: 102,
            outputGroupId: 2,
            outputType: 'type2',
            format: {
              mediaType: 'video',
              name: 'Format 2',
              category: { name: 'PLATFORM2' },
              description: 'Format 2',
              spec: { label: '16:9' },
            },
            mostRecentIterationMedia: {
              media: { url: 'http://example.com/video2' },
            },
            variation: { variationType: { name: 'type2' } },
          },
        ],
      },
      status: HttpStatus.OK,
    };

    beforeEach(() => {
      mockedAxios.get.mockClear();
    });

    it('should return project output group with scoringEnabled on successful API response', async () => {
      mockedAxios.get
        .mockResolvedValueOnce(mockProjectOutputGroupResponse)
        .mockResolvedValueOnce(mockOutputVideoResponse);

      const result = await service.getProjectOutputGroup(123, 'Bearer token');

      const expectedOutputGroups = [
        {
          id: 1,
          name: 'Output 1',
          outputGroupType: 'type1',
          outputVideos: [
            {
              id: 101,
              outputType: 'type1',
              name: null,
              mediaType: 'video',
              aspectRatio: '16:9',
              formatDescrition: 'Format 1',
              url: 'http://example.com/video1',
              placement: 'Format 1',
              channel: 'PLATFORM1',
              variationType: 'type1',
              currentIteration: { media: { url: 'http://example.com/video1' } },
              totalUnreadAnnotations: undefined,
            },
          ],
        },
        {
          id: 2,
          name: 'Output 2',
          outputGroupType: 'type2',
          outputVideos: [
            {
              id: 102,
              outputType: 'type2',
              name: null,
              mediaType: 'video',
              aspectRatio: '16:9',
              formatDescrition: 'Format 2',
              url: 'http://example.com/video2',
              placement: 'Format 2',
              channel: 'PLATFORM2',
              variationType: 'type2',
              currentIteration: { media: { url: 'http://example.com/video2' } },
              totalUnreadAnnotations: undefined,
            },
          ],
        },
      ];

      expect(result).toEqual({
        outputGroups: expectedOutputGroups,
        pagination: mockProjectOutputGroupResponse.data.pagination,
      });

      expect(axios.get).toHaveBeenCalledTimes(2);
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/outputGroup'),
        expect.any(Object),
      );
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/outputVideo'),
        expect.any(Object),
      );
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/project'),
        expect.any(Object),
      );
    });

    it('should handle API error response', async () => {
      const mockError = {
        response: { data: 'Error message', status: HttpStatus.FORBIDDEN },
      };
      mockedAxios.get.mockRejectedValueOnce(mockError);

      const result = await service.getProjectOutputGroup(123, 'Bearer token');

      expect(result).toEqual({
        data: 'Error message',
        statusCode: HttpStatus.FORBIDDEN,
      });
    });

    it('should handle unexpected errors', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network failure'));

      const result = await service.getProjectOutputGroup(123, 'Bearer token');

      expect(result).toEqual({
        data: 'An unexpected error occurred',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    });
  });

  describe('getProjectInsights', () => {
    it('should return project insights on successful API response', async () => {
      const mockProjectData = {
        status: 'OK',
        result: {
          id: 29230,
          partner: {
            id: 23142,
            organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
          },
        },
      };

      const mockInsightsData = {
        status: 'OK',
        result: {
          data: {
            getProjectInsights: {
              error: null,
              message:
                'Successfully fetched 3 insights attached to project 29077',
              result: {
                projectId: 29077,
                insights: [
                  // Insight objects here...
                ],
              },
              status: 'OK',
              traceId: 'cc7c26ef-6cc4-4b6b-a562-f2402d7c0b4f',
            },
          },
        },
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockProjectData });
      mockAxios.post.mockResolvedValueOnce({ data: mockInsightsData });

      const projectId = 29230;
      const authorization = 'Bearer mock-token';

      const result = await service.getProjectInsights(projectId, authorization);

      expect(result).toEqual(mockInsightsData);
      expect(mockAxios.get).toHaveBeenCalledWith(
        `http://example.com/api/v2/project/${projectId}?projectId=${projectId}&extraFields=partner`,
        { headers: { Authorization: authorization } },
      );
      expect(mockAxios.post).toHaveBeenCalledWith(
        'http://example.com/insights/v1/project-insights',
        {
          query: getProjectInsightsQuery,
          variables: {
            organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
            workspaceIds: [23142],
            projectId: projectId,
          },
        },
        { headers: { Authorization: authorization } },
      );
    });

    it('should handle unexpected errors', async () => {
      const mockError = new Error('Network failure');
      mockAxios.get.mockRejectedValueOnce(mockError);

      const projectId = 29230;
      const authorization = 'Bearer mock-token';

      await expect(
        service.getProjectInsights(projectId, authorization),
      ).rejects.toThrow(
        `Failed to fetch insights for project ${projectId}: Network failure`,
      );

      expect(mockAxios.get).toHaveBeenCalledWith(
        `http://example.com/api/v2/project/${projectId}?projectId=${projectId}&extraFields=partner`,
        { headers: { Authorization: authorization } },
      );
    });
  });

  describe('createIterationMediaAnnotation', () => {
    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should return created annotation data on successful request', async () => {
      const iterationMediaId = 123;
      const dto = { text: 'New annotation' };
      const authorization = 'Bearer someToken';
      const mockResponse = { id: 1, text: 'New annotation' };

      mockAxios.post.mockResolvedValueOnce({ data: mockResponse });

      const response = await service.createIterationMediaAnnotation(
        iterationMediaId,
        dto,
        authorization,
      );

      expect(response).toEqual(mockResponse);
      expect(mockAxios.post).toHaveBeenCalledWith(
        `http://example.com/api/v2/iteration/${iterationMediaId}/annotation`,
        dto,
        { headers: { Authorization: authorization } },
      );
    });

    it('should throw error on failure', async () => {
      const iterationMediaId = 123;
      const dto = { text: 'New annotation' };
      const authorization = 'Bearer someToken';
      const error = new Error('Service failed');

      service.createIterationMediaAnnotation = jest
        .fn()
        .mockImplementation(() => {
          throw new Error(
            `Error when creating iteration annotation for iterationMedia (${iterationMediaId}). ${error.message}`,
          );
        });

      const mockReq = { headers: { authorization } };

      await expect(
        controller.createIterationMediaAnnotation(
          iterationMediaId,
          dto,
          mockReq,
        ),
      ).rejects.toThrow(
        `Error when creating iteration annotation for iterationMedia (${iterationMediaId}). Service failed`,
      );
    });

    it('should throw bad request exception when text and startTime are not informed', async () => {
      const iterationMediaId = 123;
      const dto = {};
      const authorization = 'Bearer some';

      const mockReq = { headers: { authorization } };

      await expect(
        controller.createIterationMediaAnnotation(
          iterationMediaId,
          dto,
          mockReq,
        ),
      ).rejects.toThrow(
        'Either startTime or text must be provided for creating an annotation.',
      );
    });
  });

  describe('getIterationAnnotations', () => {
    it('should successfully get annotations for a specific iteration', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const paginationOptions = { offset: 0 };
      const expectedResponse = [{ id: 1, name: 'Annotation 1' }];
      service.getIterationAnnotations = jest
        .fn()
        .mockResolvedValue(expectedResponse);

      const result = await controller.getIterationAnnotations(
        iterationMediaId,
        req,
        paginationOptions,
      );

      expect(service.getIterationAnnotations).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should throw error on service failure', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const paginationOptions = { offset: 0 };
      const error = new Error('Service failed');

      service.getIterationAnnotations = jest.fn().mockImplementation(() => {
        throw new Error(
          `Error when getting iteration annotations for iterationMedia (${iterationMediaId}). ${error.message}`,
        );
      });

      await expect(
        controller.getIterationAnnotations(
          iterationMediaId,
          req,
          paginationOptions,
        ),
      ).rejects.toThrow(
        `Error when getting iteration annotations for iterationMedia (${iterationMediaId}). Service failed`,
      );

      expect(service.getIterationAnnotations).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
    });
  });

  describe('getIterationMentions', () => {
    it('should successfully get mentions for a specific iteration', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const res = { status: jest.fn().mockReturnThis(), json: jest.fn() };
      const paginationOptions = { offset: 0 };
      const expectedResponse = {
        statusCode: 200,
        data: {
          mentions: [
            {
              id: 12345,
              displayName: 'Editor',
              jobTitle: null,
              photo: null,
              color: '#93D6AC',
              relationType: 'EDITOR',
            },
          ],
          pagination: {},
        },
      };

      service.getIterationMentions = jest
        .fn()
        .mockResolvedValue(expectedResponse);

      await controller.getIterationMentions(
        iterationMediaId,
        req,
        res,
        paginationOptions,
      );

      expect(res.status).toHaveBeenCalledWith(expectedResponse.statusCode);
      expect(res.json).toHaveBeenCalledWith(expectedResponse.data);

      expect(service.getIterationMentions).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
    });

    it('should throw error on service failure', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const res = { status: jest.fn().mockReturnThis(), json: jest.fn() };
      const paginationOptions = { offset: 0 };
      const error = new Error('Service failed');

      service.getIterationMentions = jest.fn().mockImplementation(() => {
        throw new Error(
          `Error when getting iteration mentions for iterationMedia (${iterationMediaId}). ${error.message}`,
        );
      });

      await expect(
        controller.getIterationMentions(
          iterationMediaId,
          req,
          res,
          paginationOptions,
        ),
      ).rejects.toThrow(
        `Error when getting iteration mentions for iterationMedia (${iterationMediaId}). Service failed`,
      );

      expect(service.getIterationMentions).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
    });
  });

  describe('getProjectMessageChannel', () => {
    const projectId = 123;
    const authorization = 'Bearer mock-token';
    const messageChannelEndpoint = 'http://example.com/api/v2/messageChannel';

    it('should successfully retrieve project message channels with messages', async () => {
      const mockMessageChannels = {
        id: 1,
        notifyEmail: true,
        notifyPush: true,
        participants: [
          {
            id: 1,
            displayName: 'User 1',
            type: 'PROJECT_OWNER',
            lastReadMessageId: null,
          },
        ],
        projectId: 123,
        readOnly: false,
        type: 'PROJECT_EDITOR',
        unreadMessages: 5,
      };

      const mockMessages = [
        {
          id: 1,
          attachments: [{ id: 1 }],
          date: '2024-07-01T14:20:43Z',
          from: {
            id: 1,
            displayName: 'User 1',
            jobTitle: 'Tester',
            photo: 'url',
            color: 'red',
          },
          isCurrentUser: false,
          messageChannelId: 1,
          projectId: 123,
          read: false,
          text: 'Message text',
          type: 'USER',
        },
      ];

      mockAxios.get
        .mockResolvedValueOnce({ data: { result: mockMessageChannels } }) // For message channels
        .mockResolvedValueOnce({ data: { result: mockMessageChannels } }) // For message channels
        .mockResolvedValueOnce({ data: { result: mockMessages } }) // For messages
        .mockResolvedValueOnce({ data: { result: mockMessages } }); // For messages

      const result = await service.getProjectMessageChannel(
        projectId,
        authorization,
      );

      expect(result).toEqual([
        {
          id: 1,
          notifyEmail: true,
          notifyPush: true,
          participants: [
            {
              id: 1,
              displayName: 'User 1',
              type: 'PROJECT_OWNER',
              lastReadMessageId: null,
            },
          ],
          projectId: 123,
          readOnly: false,
          type: 'PROJECT_EDITOR',
          unreadMessages: 5,
          messages: [
            {
              id: 1,
              attachmentIds: [1],
              date: '2024-07-01T14:20:43Z',
              fromUser: {
                id: 1,
                displayName: 'User 1',
                jobTitle: 'Tester',
                photo: 'url',
                color: 'red',
              },
              isCurrentUser: false,
              messageChannelId: 1,
              projectId: 123,
              read: false,
              text: 'Message text',
              type: 'USER',
            },
          ],
        },
        {
          id: 1,
          notifyEmail: true,
          notifyPush: true,
          participants: [
            {
              id: 1,
              displayName: 'User 1',
              type: 'PROJECT_OWNER',
              lastReadMessageId: null,
            },
          ],
          projectId: 123,
          readOnly: false,
          type: 'PROJECT_EDITOR',
          unreadMessages: 5,
          messages: [
            {
              id: 1,
              attachmentIds: [1],
              date: '2024-07-01T14:20:43Z',
              fromUser: {
                id: 1,
                displayName: 'User 1',
                jobTitle: 'Tester',
                photo: 'url',
                color: 'red',
              },
              isCurrentUser: false,
              messageChannelId: 1,
              projectId: 123,
              read: false,
              text: 'Message text',
              type: 'USER',
            },
          ],
        },
      ]);

      expect(mockAxios.get).toHaveBeenCalledWith(messageChannelEndpoint, {
        headers: { Authorization: authorization },
        params: {
          projectId: projectId,
          type: expect.stringMatching(/PROJECT_EDITOR|ACCOUNT_MANAGER/),
        },
      });
    });

    it('should throw a ForbiddenException on error', async () => {
      const mockError = new Error('Forbidden');
      mockAxios.get.mockRejectedValue(mockError);

      await expect(
        service.getProjectMessageChannel(projectId, authorization),
      ).rejects.toThrow(ForbiddenException);
    });
  });
});
