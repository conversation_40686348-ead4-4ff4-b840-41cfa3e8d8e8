import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { Injectable } from '@nestjs/common';
import { ListProjectClientResponseDto } from '../dto/listProjectsClientResponse.dto';
import { ListProjectSuccessResponseDto } from '../dto/listProjectsSuccessResponse.dto';

@Injectable()
export class ProjectProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  get profile(): MappingProfile {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        ListProjectSuccessResponseDto,
        ListProjectClientResponseDto,

        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.id),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.name),
        ),
        forMember(
          (dest) => dest.status,
          mapFrom((src) => src.status),
        ),
        forMember(
          (dest) => dest.workspaceId,
          mapFrom((src) => src.partner.id),
        ),
        forMember(
          (dest) => dest.draftReview,
          mapFrom((src) => src.partner?.draftReview || null),
        ),
        forMember(
          (dest) => dest.scoringEnabled,
          mapFrom((src) => src.scoringEnabled),
        ),
        forMember(
          (dest) => dest.milestoneIdentifier,
          mapFrom((src) => src.milestones[0].identifier),
        ),
        forMember(
          (dest) => dest.milestoneText,
          mapFrom((src) => src.milestones[0].milestoneText),
        ),
        forMember(
          (dest) => dest.projectPreviewMedia,
          mapFrom((src) => src.projectPreviewMedia?.url || null),
        ),
        forMember(
          (dest) => dest.previewMediaDownloadUrl,
          mapFrom((src) => src.projectPreviewMedia?.downloadUrl || null),
        ),
        forMember(
          (dest) => dest.projectThumbnailUrl,
          mapFrom((src) => src.projectPreviewThumbnails?.[0]?.url || null),
        ),
      );
    };
  }
}
