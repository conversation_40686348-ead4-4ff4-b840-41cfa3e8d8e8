import { ApiProperty } from '@nestjs/swagger';

export enum DraftReview {
  AUTO_APPROVED = 'AUTO_APPROVED',
  REQUIRE_REVIEW = 'REQUIRE_REVIEW',
}

export class ListProjectClientResponseDto {
  @ApiProperty({ example: 29230 })
  id: number;

  @ApiProperty({ example: 'Adobe Plugin Project' })
  name: string;

  @ApiProperty({ example: 2 })
  status: number;

  @ApiProperty({ example: 23142 })
  workspaceId: number;

  @ApiProperty({ example: true })
  scoringEnabled: boolean;

  @ApiProperty({ example: 'CREATIVE_DIRECTOR_CLIENT_FILLING_BRIEF' })
  milestoneIdentifier: string;

  @ApiProperty({ example: 'Client is filling out the brief.' })
  milestoneText: string;

  @ApiProperty({ example: 'https://www.vidmob.com/some-awesome-image.png' })
  projectPreviewMedia?: string;

  @ApiProperty({ example: 'https://www.vidmob.com/some-awesome-image.png' })
  previewMediaDownloadUrl?: string;

  @ApiProperty({ example: 'https://www.vidmob.com/some-awesome-image.png' })
  projectThumbnailUrl?: string;

  @ApiProperty({
    enum: DraftReview,
    example: DraftReview.AUTO_APPROVED,
    nullable: true,
  })
  draftReview?: DraftReview | null;
}
