import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Optional,
  IsString,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AutoMap } from '@automapper/classes';

export class FormatDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap()
  @IsString()
  name: string;

  @AutoMap()
  @IsString()
  identifier: string;

  @ValidateNested()
  @Type(() => FormatCategoryDto)
  category: FormatCategoryDto;

  @AutoMap()
  @IsString()
  description: string;

  @AutoMap()
  @IsString()
  tooltip: string;

  @AutoMap()
  @IsOptional()
  @IsString()
  imageUrl: string | null;

  @AutoMap()
  @IsOptional()
  @IsString()
  videoUrl: string | null;

  @AutoMap()
  @IsString()
  iconUrl: string;

  @ValidateNested()
  @Type(() => FormatSpecDto)
  spec: FormatSpecDto;

  @AutoMap()
  @IsString()
  safeZoneOverlayImageUrl: string;

  @AutoMap()
  @IsString()
  mediaType: string;
}

export class FormatCategoryDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap()
  @IsString()
  name: string;

  @AutoMap()
  @IsString()
  identifier: string;

  @AutoMap()
  @IsOptional()
  @IsString()
  tooltip: string | null;

  @AutoMap()
  @IsOptional()
  @IsString()
  imageUrl: string | null;

  @AutoMap(() => Number)
  @IsNumber()
  sequence: number;
}

export class FormatSpecDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap()
  @IsString()
  label: string;

  @AutoMap()
  @IsString()
  identifier: string;

  @AutoMap()
  @IsString()
  type: string;

  @AutoMap(() => Number)
  @IsNumber()
  width: number;

  @AutoMap(() => Number)
  @IsNumber()
  height: number;

  @AutoMap(() => Number)
  @IsNumber()
  maxSize: number;
}

export class MediaDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap()
  @IsString()
  name: string;

  @AutoMap()
  @IsOptional()
  @IsString()
  displayName: string | null;

  @AutoMap()
  @IsString()
  fileType: string;

  @AutoMap()
  @IsString()
  mediaType: string;

  @AutoMap()
  @IsString()
  processingState: string;

  @AutoMap()
  @IsString()
  mimeType: string;

  @AutoMap()
  @IsDate()
  dateUploaded: Date;

  @AutoMap()
  @IsDate()
  dateCreated: Date;

  @AutoMap()
  @IsOptional()
  @IsDate()
  dateTaken: Date | null;

  @AutoMap()
  @IsString()
  description: string;

  @AutoMap()
  @IsString()
  assetIdentifier: string;

  @AutoMap(() => Number)
  @IsNumber()
  height: number;

  @AutoMap(() => Number)
  @IsNumber()
  width: number;

  @AutoMap()
  @IsString()
  url: string;

  @AutoMap()
  @IsString()
  downloadUrl: string;

  @AutoMap(() => Number)
  @IsNumber()
  size: number;

  @AutoMap(() => Number)
  @IsNumber()
  duration: number;

  @AutoMap(() => Number)
  @IsNumber()
  durationPrecise: number;
}

export class ThumbnailDto {
  @AutoMap()
  @IsString()
  url: string;

  @AutoMap(() => Number)
  @IsNumber()
  height: number;

  @AutoMap(() => Number)
  @IsNumber()
  width: number;
}

export class ThumbnailsDto {
  @ValidateNested()
  @Type(() => ThumbnailDto)
  '640': ThumbnailDto;

  @ValidateNested()
  @Type(() => ThumbnailDto)
  '750': ThumbnailDto;

  @ValidateNested()
  @Type(() => ThumbnailDto)
  '1242': ThumbnailDto;
}

export class IterationMediaDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap()
  @IsBoolean()
  accepted: boolean;

  @AutoMap()
  @IsDate()
  dateCreated: Date;

  @AutoMap(() => Number)
  @IsNumber()
  editorId: number;

  @AutoMap(() => Number)
  @IsNumber()
  iteration: number;

  @AutoMap(() => Number)
  @IsNumber()
  iterationVersion: number;

  @AutoMap(() => Number)
  @IsNumber()
  outputVideoId: number;

  @AutoMap()
  @IsOptional()
  @IsString()
  artifactId: string | null;

  @AutoMap()
  @IsString()
  artifactType: string;

  @AutoMap()
  @IsBoolean()
  isFeedbackComplete: boolean;

  @AutoMap()
  @IsString()
  reviewStatus: string;

  @ValidateNested()
  @Type(() => ThumbnailsDto)
  thumbnails: ThumbnailsDto;

  @AutoMap(() => MediaDto)
  @ValidateNested()
  @Type(() => MediaDto)
  media: MediaDto;
}

export class VariationTypeDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap()
  @IsString()
  identifier: string;

  @AutoMap()
  @IsString()
  name: string;

  @AutoMap()
  @IsBoolean()
  active: boolean;
}

export class VariationDto {
  @AutoMap()
  @IsOptional()
  @IsString()
  description: string | null;

  @ValidateNested()
  @Type(() => VariationTypeDto)
  variationType: VariationTypeDto;
}

export class OutputVideoDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap(() => Number)
  @IsNumber()
  projectId: number;

  @AutoMap(() => Number)
  @IsNumber()
  videoNumber: number;

  @AutoMap()
  @IsString()
  outputSubtype: string;

  @AutoMap()
  @IsString()
  name: string;

  @AutoMap()
  @IsBoolean()
  accepted: boolean;

  @ValidateNested()
  @Type(() => IterationMediaDto)
  acceptedIterationMedia: IterationMediaDto;

  @AutoMap()
  @IsString()
  outputFileType: string;

  @AutoMap(() => Number)
  @IsNumber()
  totalIterations: number;

  @AutoMap(() => Number)
  @IsNumber()
  currentIteration: number;

  @AutoMap(() => Number)
  @IsNumber()
  iterationsLeft: number;

  @AutoMap(() => Number)
  @IsNumber()
  totalUnviewedIterations: number;

  @AutoMap(() => Number)
  @IsNumber()
  totalUnreadAnnotations: number;

  @ValidateNested()
  @Type(() => FormatDto)
  format: FormatDto;

  @ValidateNested()
  @Type(() => IterationMediaDto)
  mostRecentIterationMedia: IterationMediaDto;

  @AutoMap(() => Number)
  @IsNumber()
  outputGroupId: number;

  @AutoMap()
  @IsOptional()
  @IsString()
  outputGroupName: string | null;

  @AutoMap()
  @IsString()
  outputType: string;

  @ValidateNested()
  @Type(() => VariationDto)
  @IsOptional()
  variation: VariationDto | null;
}

export class OutputGroupDto {
  @AutoMap(() => Number)
  @IsNumber()
  id: number;

  @AutoMap()
  @IsOptional()
  @IsString()
  name: string | null;

  @AutoMap(() => Number)
  @IsNumber()
  projectId: number;

  @AutoMap()
  @IsString()
  outputGroupType: string;

  @ValidateNested({ each: true })
  @Type(() => OutputVideoDto)
  outputVideos: OutputVideoDto[];

  @ValidateNested()
  @IsOptional()
  @Type(() => MediaDto)
  sourceMedia: MediaDto | null;
}
