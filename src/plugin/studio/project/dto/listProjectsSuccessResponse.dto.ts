import { ApiProperty } from '@nestjs/swagger';

class UserDto {
  @ApiProperty({ example: 111111 })
  id: number;

  @ApiProperty({ example: 'Client' })
  displayName: string;

  @ApiProperty({ example: null })
  jobTitle: string | null;

  @ApiProperty({ example: 'https://example.com/avatar/avatar61.png' })
  photo: string;
}

class MilestoneDto {
  @ApiProperty({ example: 963963 })
  id: number;

  @ApiProperty({ example: 'PROJECT_CREATE' })
  milestone: string;

  @ApiProperty({ example: 'Client is filling out the brief.' })
  milestoneText: string;

  @ApiProperty({ example: 0 })
  milestoneProjectStatus: number;

  @ApiProperty({ example: 'CREATIVE_DIRECTOR' })
  roleCategory: string;

  @ApiProperty({ example: false })
  roleActionRequired: boolean;

  @ApiProperty({ example: null })
  additionalData: any;

  @ApiProperty({ example: false })
  completed: boolean;

  @ApiProperty({ example: '2023-10-06T17:08:06Z' })
  dateCreated: string;

  @ApiProperty({ example: '2023-10-06T17:08:06Z' })
  lastUpdated: string;

  @ApiProperty({ example: 'CREATIVE_DIRECTOR_CLIENT_FILLING_BRIEF' })
  identifier: string;
}

class projectPreviewMediaDto {
  @ApiProperty({ example: 'https://example.com/preview/preview61.png' })
  url: string;

  @ApiProperty({ example: 'https://example.com/preview/preview61.png' })
  downloadUrl: string;
}

class ActionRequiredRoleDto {
  @ApiProperty({ example: 'CLIENT' })
  roleCategory: string;

  @ApiProperty({ type: [UserDto] })
  users: UserDto[];

  @ApiProperty({ example: false })
  hasMore: boolean;
}

export enum DraftReview {
  AUTO_APPROVED = 'AUTO_APPROVED',
  REQUIRE_REVIEW = 'REQUIRE_REVIEW',
}

class PartnerDto {
  @ApiProperty({ example: 369 })
  id: number;

  @ApiProperty({ example: false })
  personal: boolean;

  @ApiProperty({ example: null })
  logoUrl: string | null;

  @ApiProperty({ example: 'my new name' })
  name: string;

  @ApiProperty({ example: true })
  isPrimary: boolean;

  @ApiProperty({
    enum: DraftReview,
    example: DraftReview.AUTO_APPROVED,
    nullable: true,
  })
  draftReview?: DraftReview | null;

  @ApiProperty({ example: 'aaaaaaaa-aaaaa-99aaa-9999-9999999' })
  organizationId: string;

  @ApiProperty({ example: 'A' })
  industry: string;

  @ApiProperty({ example: 'codepartner' })
  code: string;

  @ApiProperty({ example: true })
  isEnterprise: boolean;

  @ApiProperty({ example: null })
  publicAccountTypeName: string | null;

  @ApiProperty({ example: 'ENTERPRISE' })
  accountTypeIdentifier: string;

  @ApiProperty({ example: false })
  notifyFavoriteEditorEnabled: boolean;

  @ApiProperty({ example: false })
  snapIntegrationEnabled: boolean;

  @ApiProperty({ example: true })
  draftDownloadEnabled: boolean;
}

class FileTypeSummaryDto {
  @ApiProperty({ example: 0 })
  totalMedia: number;

  @ApiProperty({ example: {} })
  fileTypes: Record<string, any>;
}

class ProjectPreviewThumbnailDto {
  @ApiProperty({
    example: 'https://vidmob.s3.amazonaws.com/someimage.png',
  })
  url: string;

  @ApiProperty({ example: 360 })
  height: number;

  @ApiProperty({ example: 640 })
  width: number;
}

export class ListProjectSuccessResponseDto {
  @ApiProperty({ example: 999999 })
  id: number;

  @ApiProperty({ example: 'Sample Project' })
  name: string;

  @ApiProperty({ example: null })
  description: string | null;

  @ApiProperty({ example: 0 })
  status: number;

  @ApiProperty({ example: false })
  allowNewMedia: boolean;

  @ApiProperty({ example: '2023-10-06T17:08:02Z' })
  dateCreated: string;

  @ApiProperty({ example: 0 })
  totalOutputVideos: number;

  @ApiProperty({ example: null })
  numberOfApprovedConceptsRequired: number | null;

  @ApiProperty({ example: null })
  numberOfApprovedVidscriptsRequired: number | null;

  @ApiProperty({ example: null })
  reportFlightDateBegin: string | null;

  @ApiProperty({ example: null })
  reportFlightDateEnd: string | null;

  @ApiProperty({ example: null })
  reportAnalysisPeriodBegin: string | null;

  @ApiProperty({ example: null })
  reportAnalysisPeriodEnd: string | null;

  @ApiProperty({ example: 'CREATIVE' })
  productType: string;

  @ApiProperty({ example: false })
  hasOutputGroups: boolean;

  @ApiProperty({ example: false })
  requiresIdeation: boolean;

  @ApiProperty({ example: false })
  restrictConceptCreatorInformation: boolean;

  @ApiProperty({ type: FileTypeSummaryDto })
  fileTypeSummary: FileTypeSummaryDto;

  @ApiProperty({ type: [MilestoneDto] })
  milestones: MilestoneDto[];

  @ApiProperty({ type: [ActionRequiredRoleDto] })
  actionRequiredRoles: ActionRequiredRoleDto[];

  @ApiProperty({ type: PartnerDto })
  partner: PartnerDto;

  @ApiProperty({ type: [projectPreviewMediaDto] })
  projectPreviewMedia: projectPreviewMediaDto;

  @ApiProperty({ type: [ProjectPreviewThumbnailDto] })
  projectPreviewThumbnails: ProjectPreviewThumbnailDto[];

  @ApiProperty({ example: false })
  scoringEnabled: boolean;
}
