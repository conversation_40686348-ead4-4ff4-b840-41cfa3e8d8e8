import { AutoMap } from '@automapper/classes';

export class UserDto {
  @AutoMap()
  id: number;

  @AutoMap()
  displayName: string;
}

export class MediaDto {
  @AutoMap()
  id: number;

  @AutoMap()
  name: string;

  @AutoMap()
  downloadUrl: string;
}

export class AnswerOptionDto {
  @AutoMap()
  topic: string;

  @AutoMap(() => String)
  answer: string[];
}

export class QuestionDto {
  @AutoMap()
  topic: string;

  @AutoMap()
  text: string;

  @AutoMap()
  lastUpdatedDate: string;

  @AutoMap(() => UserDto)
  user: UserDto;

  @AutoMap()
  questionType: string;

  @AutoMap()
  answer: string;

  @AutoMap(() => AnswerOptionDto)
  answers: AnswerOptionDto[];

  @AutoMap()
  allowAttachments: boolean;

  @AutoMap(() => MediaDto)
  media: MediaDto[];
}

export class ProjectQuestionsDto {
  @AutoMap()
  projectId: number;

  @AutoMap(() => QuestionDto)
  items: QuestionDto[];
}
