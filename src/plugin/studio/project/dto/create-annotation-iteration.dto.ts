import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsIn,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAnnotationIterationMediaDto {
  @ApiProperty({
    description: 'The start time of the annotation.',
    example: '0.5',
  })
  @IsOptional()
  @IsNumber()
  startTime?: number;

  @ApiProperty({
    description: 'The duration of the annotation.',
    example: '1.5',
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty({
    description: 'The text of the annotation.',
    example: 'I really like this version',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500000)
  text?: string;

  @ApiProperty({
    description: 'Identifier of the reaction.',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  reactionId?: number;

  @ApiProperty({
    description: 'Identifier of the annotation being replied to',
    example: 67890,
  })
  @IsOptional()
  @IsNumber()
  replyToId?: number;

  @ApiProperty({
    description: 'Identifier of the attachment.',
    example: 11223,
  })
  @IsOptional()
  attachmentId?: number;

  @ApiProperty({
    description: 'Scope of the annotation.',
    example: 'SINGLE',
  })
  @IsString()
  @IsOptional()
  @IsIn(['PROJECT', 'GROUP', 'SINGLE'])
  scope?: string;
}
