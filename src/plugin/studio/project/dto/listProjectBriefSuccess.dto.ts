import { ApiProperty } from '@nestjs/swagger';

class MediaDto {
  @ApiProperty({
    example: 'https://some_url.com.png',
  })
  thumbnail: string;

  @ApiProperty({ example: 188978 })
  id: number;

  @ApiProperty({ example: '_v0.0.1__Poke_Clicker_Backup_Save.txt' })
  name: string;

  @ApiProperty()
  displayName: string | null;

  @ApiProperty({ example: true })
  isReady: boolean;

  @ApiProperty({ example: 'DOCUMENT' })
  fileType: string;

  @ApiProperty({ example: 'PROJECT_BRIEF' })
  mediaType: string;

  @ApiProperty({ example: 'COMPLETE' })
  processingState: string;

  @ApiProperty({ example: 'text/plain' })
  mimeType: string;

  @ApiProperty({ example: '2023-10-02T15:44:27Z' })
  dateUploaded: string;

  @ApiProperty({ example: '2023-10-02T15:44:14Z' })
  dateCreated: string;

  @ApiProperty({ example: '2023-10-02T15:44:14Z' })
  dateTaken: string;

  @ApiProperty({ example: null })
  description: string | null;

  @ApiProperty({
    example: 'briefdocument-29077_v0.0.1__poke_clicker_backup_save.txt',
  })
  assetIdentifier: string;

  @ApiProperty()
  height: number | null;

  @ApiProperty()
  width: number | null;

  @ApiProperty({
    example: 'https://some_url.com',
  })
  url: string;

  @ApiProperty({
    example: 'https://some_url.com',
  })
  downloadUrl: string;

  @ApiProperty({ example: 168128 })
  size: number;
}

class BriefDto {
  @ApiProperty({ example: 3803 })
  id: number;

  @ApiProperty({ example: 29077 })
  projectId: number;

  @ApiProperty({ example: 'Brief Document of project 29077' })
  description: string;

  @ApiProperty({ example: 'BRIEF' })
  documentType: string;

  @ApiProperty({ example: '2023-10-02T15:44:15Z' })
  dateCreated: string;

  @ApiProperty({ example: '2023-10-02T15:44:15Z' })
  lastUpdated: string;

  @ApiProperty({ type: MediaDto })
  media: MediaDto;
}

export class ProjectBriefSuccessResponseDto {
  @ApiProperty({ example: 'OK' })
  status: string;

  @ApiProperty({ type: [BriefDto] })
  result: BriefDto[];
}
