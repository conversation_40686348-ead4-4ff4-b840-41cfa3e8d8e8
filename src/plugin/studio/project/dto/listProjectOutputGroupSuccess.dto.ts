import { ApiProperty } from '@nestjs/swagger';

class FormatSpecDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  label: string;

  @ApiProperty()
  identifier: string;

  @ApiProperty()
  type: string;

  @ApiProperty()
  width: number;

  @ApiProperty()
  height: number;

  @ApiProperty()
  maxSize: number;
}

class FormatCategoryDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  identifier: string;

  @ApiProperty()
  tooltip: string;

  @ApiProperty()
  imageUrl: string;

  @ApiProperty()
  sequence: number;
}

class FormatDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  identifier: string;

  @ApiProperty()
  category: FormatCategoryDto;

  @ApiProperty()
  description: string;

  @ApiProperty()
  tooltip: string;

  @ApiProperty()
  imageUrl: string;

  @ApiProperty()
  videoUrl: string;

  @ApiProperty()
  iconUrl: string;

  @ApiProperty()
  spec: FormatSpecDto;

  @ApiProperty()
  safeZoneOverlayImageUrl: string;

  @ApiProperty()
  mediaType: string;
}

class HeroOutputVideoDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  projectId: number;

  @ApiProperty()
  videoNumber: number;

  @ApiProperty()
  outputSubtype: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  accepted: boolean;

  @ApiProperty()
  outputFileType: string;

  @ApiProperty()
  totalIterations: number;

  @ApiProperty()
  currentIteration: number;

  @ApiProperty()
  iterationsLeft: number;

  @ApiProperty()
  format: FormatDto;

  @ApiProperty()
  outputGroupId: number;

  @ApiProperty()
  outputGroupName: string;

  @ApiProperty()
  outputType: string;

  @ApiProperty()
  maxDraftRounds: number;

  @ApiProperty()
  currentDraftRounds: number;

  @ApiProperty()
  remainingDraftRounds: number;
}

class ResultDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  projectId: number;

  @ApiProperty()
  outputGroupType: string;

  @ApiProperty()
  heroOutputVideo: HeroOutputVideoDto;
}

export class SuccessProjectOutputGroupResponseDto {
  @ApiProperty()
  status: string;

  @ApiProperty({ type: [ResultDto] })
  result: ResultDto[];
}
