import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum PlatformType {
  ALL_PLATFORMS = 'ALL_PLATFORMS',
  TWITTER = 'TWITTER',
  FACEBOOK = 'FACEBOOK',
  TIKTOK = 'TIKTOK',
  PINTEREST = 'PINTEREST',
  LINKEDIN = 'LINKEDIN',
  DV360 = 'DV360',
  ADWORDS = 'ADWORDS',
  VERIZONNATIVE = 'VERIZONNATIVE',
  SNAPCHAT = 'SNAPCHAT',
  AMAZON = 'AMAZON',
  REDDIT = 'REDDIT',
}

export class CreateScorecardIterationMediaDto {
  // workspaceId
  @ApiProperty({
    description: 'The ID of the workspace.',
    example: 123,
  })
  @IsNumber()
  @IsNotEmpty()
  workspaceId: number;

  @ApiProperty({
    description: 'Identifier of the platform',
    example: 'FACEBOOK',
  })
  @IsString()
  @IsNotEmpty()
  platformIdentifier: string;

  @ApiProperty({
    description: 'The name of the file that will be uploaded.',
    example: 'vidMobTemp.mov',
  })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  // scorecardName
  @ApiProperty({
    description: 'The name of the scorecard.',
    example: 'VidMob Scorecard',
  })
  @IsString()
  @IsOptional()
  scorecardName?: string;

  @ApiProperty({
    description:
      'The id of the iterationMedia if this is a new version of an already existing iteration media.',
    example: 123,
  })
  @IsNumber()
  @IsOptional()
  iterationMediaId?: number;

  @ApiProperty({
    description:
      'The number of hours spent on the iteration media, which can be a fractional value.',
    example: 0.5,
  })
  @IsNumber({
    maxDecimalPlaces: 2,
  })
  totalHours: number;

  @ApiProperty({
    description: 'The context text from the creator',
    example: 'context text',
  })
  @IsString()
  @IsOptional()
  contextTextFromCreator?: string;

  @ApiProperty({
    description: 'The device identifier of the media.',
    example: 'Adobe After Effects',
  })
  @IsString()
  @MaxLength(190)
  @IsOptional()
  deviceIdentifier?: string;
}
