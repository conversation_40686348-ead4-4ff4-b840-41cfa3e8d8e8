import { ApiProperty } from '@nestjs/swagger';

class PersonDto {
  @ApiProperty({
    example: 12345,
    description: 'The ID of the person',
  })
  id: number;

  @ApiProperty({
    example: '<PERSON>',
    description: 'The display name of the person',
  })
  displayName: string;

  @ApiProperty({
    example: 'Creator',
    description: 'The job title of the person',
  })
  jobTitle: string | null;

  @ApiProperty({
    example: 'image.jpg',
    description: 'The photo URL of the person',
  })
  photo: string | null;

  @ApiProperty({
    example: '#EBBA81',
    description: 'The color associated with the person',
  })
  color: string | null;
}

class MediaClipDto {
  @ApiProperty({
    example: 1111,
    description: 'The ID of the media clip',
  })
  id: number;

  @ApiProperty({
    example: 1234,
    description: 'The media ID associated with the clip',
  })
  mediaId: number;

  @ApiProperty({
    example: 'TEMPORAL',
    description: 'The location type of the media clip',
  })
  locationType: string;

  @ApiProperty({
    example: 0.0,
    description: 'The start time of the media clip',
  })
  startTime: number;

  @ApiProperty({
    example: 0.0,
    description: 'The duration of the media clip',
  })
  duration: number;
}

export class CreateAnnotationIterationResponseDto {
  @ApiProperty({
    example: 12345,
    description: 'The ID of the annotation iteration media',
  })
  id: number;

  @ApiProperty({
    example: 'attachment',
    description: 'The text of the annotation iteration media',
  })
  text: string;

  @ApiProperty({
    example: 'PROJECT',
    description: 'The visibility of the annotation iteration media',
  })
  visibility: string;

  @ApiProperty({
    example: 'DRAFT',
    description: 'The context type of the annotation iteration media',
  })
  contextType: string;

  @ApiProperty({
    example: 'OUTPUT_VIDEO',
    description: 'The context subtype of the annotation iteration media',
  })
  contextSubType: string;

  @ApiProperty({
    example: false,
    description: 'Indicates if the annotation iteration media is unread',
  })
  unread: boolean;

  @ApiProperty({
    type: PersonDto,
    description:
      'Details of the person associated with the annotation iteration media',
  })
  person: PersonDto;

  @ApiProperty({
    example: '2024-06-03T19:12:44Z',
    description: 'The creation date of the annotation iteration media',
  })
  dateCreated: string;

  @ApiProperty({
    example: '2024-06-03T19:12:44Z',
    description: 'The last updated date of the annotation iteration media',
  })
  lastUpdated: string;

  @ApiProperty({
    example: 'SINGLE',
    description: 'The scope of the annotation iteration media',
  })
  scope: string;

  @ApiProperty({
    example: null,
    description:
      'The annotation set associated with the annotation iteration media',
  })
  annotationSet: string | null;

  @ApiProperty({
    example: null,
    description:
      'The annotation type associated with the annotation iteration media',
  })
  annotationType: string | null;

  @ApiProperty({
    example: false,
    description: 'Indicates if the annotation iteration media is checked',
  })
  checked: boolean;

  @ApiProperty({
    example: [12345],
    description:
      'List of attachment IDs associated with the annotation iteration media',
  })
  attachmentIds: number[];

  @ApiProperty({
    type: MediaClipDto,
    description:
      'Details of the media clip associated with the annotation iteration media',
  })
  mediaClip: MediaClipDto;

  @ApiProperty({
    example: 12345,
    description: 'The ID of the annotation being replied to',
  })
  replyTo: number;
}
