import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  Request,
  ParseIntPipe,
  HttpException,
  Res,
  Version,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiResponse,
  ApiBody,
  ApiCreatedResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { ProjectService } from '../services/project.service';
import { ResultService } from '../../../scoring/result/services/result.service';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ListProjectSuccessResponseDto } from '../dto/listProjectsSuccessResponse.dto';
import { ProjectBriefSuccessResponseDto } from '../dto/listProjectBriefSuccess.dto';
import { SuccessProjectOutputGroupResponseDto } from '../dto/listProjectOutputGroupSuccess.dto';
import { CreateScorecardIterationMediaDto } from '../dto/create-scorecard-iteration-media.dto';
import { CreateAnnotationIterationMediaDto } from '../dto/create-annotation-iteration.dto';
import { CreateScorecardMediaResponseDto } from '../dto/create-scorecard-media-response.dto';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import { readWorkspaceScoringFromBody } from '../../../scoring/scoring.permissions';
import { CreateAnnotationIterationResponseDto } from '../dto/create-annotation-iteration-response.dto';
import { ListProjectClientResponseDto } from '../dto/listProjectsClientResponse.dto';
import { readProjectInsightProjectLevel } from '../../studio.permissions';

@ApiTags('Plugin Studio Project')
@ApiSecurity('Bearer Token')
@Controller('plugin/studio/project')
export class ProjectController {
  constructor(
    private projectService: ProjectService,
    private resultService: ResultService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get list of projects where current user is an Editor',
  })
  @ApiResponse({
    status: 200,
    description: 'List of projects returned successfully',
    type: ListProjectSuccessResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Error response',
    type: null,
  })

  @ApiQuery({
    name: 'perPage',
    type: 'number',
    required: false,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'offset',
    type: 'number',
    required: false,
    description: 'Offset for pagination',
  })
  @ApiQuery({
    name: 'queryId',
    type: 'string',
    required: false,
    description: 'Query identifier for pagination',
  })
  @ApiQuery({
    name: 'status',
    type: 'string',
    required: false,
    description: 'Filter projects by status, comma-separated values',
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    required: false,
    description: 'Search term to filter projects',
  })
  async getEditorProjectList(
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('status') status?: string,
    @Query('search') search?: string,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getEditorProjects(
      authorization,
      paginationOptions,
      status,
      search,
    );
  }

  @Get(':projectId')
  @ApiOperation({ summary: 'Get project details by projectId' })
  @ApiResponse({
    status: 200,
    description: 'Project details returned successfully',
    type: ListProjectClientResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Error response',
    type: null,
  })

  @ApiParam({
    name: 'projectId',
    type: 'string',
    required: true,
    description: 'The ID of the project',
  })
  async getProjectById(
    @Param('projectId') projectId: string,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getProjectById(projectId, authorization);
  }

  @Get(':projectId/brief')
  @ApiOperation({ summary: 'Get briefs for a specific project' })
  @ApiResponse({
    status: 200,
    description: 'List of briefs returned successfully',
    type: ProjectBriefSuccessResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Error response',
    type: null,
  })

  @ApiParam({
    name: 'projectId',
    type: 'number',
    required: true,
    description: 'The ID of the project',
  })
  async getProjectBriefs(
    @Param('projectId') projectId: number,
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getProjectBriefList(
      projectId,
      authorization,
      paginationOptions,
    );
  }

  @Get('iteration/:iterationMediaId/annotation')
  @ApiOperation({ summary: 'Get annotations for a specific iteration' })
  @ApiResponse({
    status: 200,
    description: 'List of annotations returned successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Error response',
    type: null,
  })

  @ApiParam({
    name: 'iterationMediaId',
    type: 'number',
    required: true,
    description: 'The ID of the iteration media',
  })
  async getIterationAnnotations(
    @Param('iterationMediaId') iterationMediaId: number,
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getIterationAnnotations(
      iterationMediaId,
      authorization,
      paginationOptions,
    );
  }

  @Get('iteration/:iterationMediaId/mention')
  @ApiOperation({ summary: 'Get mentions for a specific iteration' })
  @ApiResponse({
    status: 200,
    description: 'List of iteration mentions returned successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Error response',
    type: null,
  })

  @ApiParam({
    name: 'iterationMediaId',
    type: 'number',
    required: true,
    description: 'The ID of the iteration media',
  })
  async getIterationMentions(
    @Param('iterationMediaId') iterationMediaId: number,
    @Request() req: any,
    @Res() res: any,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const authorization = req.headers.authorization;
    const response = await this.projectService.getIterationMentions(
      iterationMediaId,
      authorization,
      paginationOptions,
    );

    return res.status(response.statusCode).json(response.data);
  }

  @Get(':projectId/outputGroup')
  @ApiOperation({ summary: 'List project output group by projectId' })
  @ApiResponse({
    status: 200,
    description: 'Project output group returned successfully',
    type: SuccessProjectOutputGroupResponseDto,
  })

  @ApiParam({
    name: 'projectId',
    type: 'number',
    required: true,
    description: 'The ID of the project',
  })
  // @ApiQuery({
  //   name: 'search',
  //   type: 'string',
  //   required: false,
  //   description: 'The name of the output group to filter by',
  // })
  async getProjectOutputGroup(
    @Param('projectId') projectId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getProjectOutputGroup(
      projectId,
      authorization,
      paginationOptions,
    );
  }

  @Get(':projectId/assetFolder')
  @ApiOperation({
    summary: 'Retrieve folders and assets for a specific project',
  })
  @ApiResponse({
    status: 200,
    description: 'List of folders and assets returned successfully',
    type: null,
  })

  @ApiParam({
    name: 'projectId',
    type: 'number',
    required: true,
    description: 'The ID of the project',
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    required: false,
    description: 'Search term to filter projects',
  })
  async getProjectAssetsAndFolders(
    @Param('projectId') projectId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
    @Query('search') search?: string,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getProjectAssetsAndFolders(
      projectId,
      authorization,
      paginationOptions,
      search,
    );
  }

  @Get(':projectId/folder/:folderId/AssetFolder')
  @ApiOperation({
    summary:
      'Retrieve nested folders and assets for a specific project and folder',
  })

  @ApiParam({
    name: 'projectId',
    required: true,
    description: 'The ID of the project',
    type: Number,
  })
  @ApiParam({
    name: 'folderId',
    required: true,
    description: 'The ID of the folder',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    required: false,
    description: 'Search term to filter folders and assets',
  })
  async getNestedFolderContents(
    @Param('projectId') projectId: number,
    @Param('folderId') folderId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
    @Query('search') search?: string,
  ) {
    const authorization = req.headers.authorization;

    return await this.projectService.getNestedFolderContents(
      projectId,
      folderId,
      authorization,
      paginationOptions,
      search,
    );
  }

  @Get(':projectId/insights')
  @ApiOperation({ summary: 'Get insights for a specific project' })
  @ApiResponse({
    status: 200,
    description: 'Insights returned successfully',
    type: null,
  })

  @ApiParam({
    name: 'projectId',
    type: 'number',
    required: true,
    description: 'The ID of the project',
  })
  async getProjectInsights(
    @Param('projectId') projectId: number,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getProjectInsights(
      projectId,
      authorization,
    );
  }

  @Get(':projectId/insights')
  @Version('2')
  @ApiOperation({ summary: 'Get insights for a specific project' })
  @ApiResponse({
    status: 200,
    description: 'Insights returned successfully',
    type: null,
  })

  @ApiParam({
    name: 'projectId',
    type: 'number',
    required: true,
    description: 'The ID of the project',
  })
  @Permissions(readProjectInsightProjectLevel)
  async getProjectInsightsByProjectId(@Param('projectId') projectId: number) {
    return await this.projectService.getProjectInsightsByProjectId(projectId);
  }

  @Get('outputVideo/:outputVideoId/iterationMedia')
  @ApiOperation({ summary: 'Get iteration media for a specific project' })
  @ApiResponse({
    status: 200,
    description: 'Output video iteration media returned successfully',
    type: null,
  })

  @ApiParam({
    name: 'outputVideoId',
    type: 'number',
    required: true,
    description: 'The ID of the output video',
  })
  async getOutputVideoIterationMedia(
    @Param('outputVideoId') outputVideoId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getOutputVideoIterationMedia(
      outputVideoId,
      authorization,
      paginationOptions,
    );
  }

  @Permissions(readWorkspaceScoringFromBody)
  @Post('outputVideo/:outputVideoId/iterationMedia')
  @ApiOperation({ summary: 'Create a scorecard with iteration media' })
  @ApiParam({ name: 'outputVideoId', type: 'number' })
  @ApiBody({ type: CreateScorecardIterationMediaDto })
  @ApiCreatedResponse({ type: CreateScorecardMediaResponseDto })
  @HttpCode(HttpStatus.CREATED)
  async createScorecardWithIterationMedia(
    @Param('outputVideoId', ParseIntPipe) outputVideoId: number,
    @Body() dto: CreateScorecardIterationMediaDto,
    @Request() req: any,
  ): Promise<CreateScorecardMediaResponseDto> {
    const authorization = req.headers.authorization;
    try {
      return await this.resultService.createScorecardWithIterationMedia(
        outputVideoId,
        dto,
        authorization,
        req['userId'],
      );
    } catch (error) {
      throw new HttpException(
        {
          status: error.status,
          message: error.message,
        },
        error.status,
      );
    }
  }

  @Post('iteration/:iterationMediaId/annotation')
  @ApiOperation({ summary: 'Create an annotation on iteration media' })
  @ApiParam({
    name: 'iterationMediaId',
    type: 'number',
    required: true,
    description: 'The ID of the iteration media',
  })
  @ApiBody({ type: CreateAnnotationIterationMediaDto })
  @ApiCreatedResponse({ type: CreateAnnotationIterationResponseDto })
  async createIterationMediaAnnotation(
    @Param('iterationMediaId') iterationMediaId: number,
    @Body() dto: CreateAnnotationIterationMediaDto,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    try {
      return await this.projectService.createIterationMediaAnnotation(
        iterationMediaId,
        dto,
        authorization,
      );
    } catch (error) {
      throw new HttpException(
        {
          status: error.status,
          message: error.message,
        },
        error.status,
      );
    }
  }

  @Get(':projectId/messageChannel')
  @ApiOperation({ summary: 'Get message channel for a specific project' })
  @ApiResponse({
    status: 200,
    description: 'Message channel returned successfully',
    type: null,
  })

  @ApiParam({
    name: 'projectId',
    type: 'number',
    required: true,
    description: 'The ID of the project',
  })
  async getProjectMessageChannel(
    @Param('projectId') projectId: number,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.projectService.getProjectMessageChannel(
      projectId,
      authorization,
    );
  }
}
