import { Test, TestingModule } from '@nestjs/testing';
import { ProjectController } from './project.controller';
import { ProjectService } from '../services/project.service';
import { Response } from 'express';
import { ForbiddenException, HttpException, HttpStatus } from '@nestjs/common';
import { ResultService } from '../../../scoring/result/services/result.service';
import { CreateScorecardIterationMediaDto } from '../dto/create-scorecard-iteration-media.dto';
import { CreateAnnotationIterationMediaDto } from '../dto/create-annotation-iteration.dto';

describe('ProjectController', () => {
  let controller: ProjectController;
  let resultService: jest.Mocked<ResultService>;
  let service: ProjectService;

  const res: Response = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(),
  } as any;

  beforeEach(async () => {
    const mockResultService = {
      createScorecardWithIterationMedia: jest.fn(),
    };
    const mockProjectService = {
      getEditorProjects: jest.fn(),
      getProjectOutputGroup: jest.fn(),
      getProjectAssetsAndFolders: jest.fn(),
      getNestedFolderContents: jest.fn(),
      getProjectInsights: jest.fn(),
      getProjectMessageChannel: jest.fn(),
      getProjectById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProjectController],
      providers: [
        {
          provide: ProjectService,
          useValue: mockProjectService,
        },
        {
          provide: ResultService,
          useValue: mockResultService,
        },
      ],
    }).compile();

    controller = module.get<ProjectController>(ProjectController);
    service = module.get(ProjectService) as jest.Mocked<ProjectService>;
    resultService = module.get(ResultService) as jest.Mocked<ResultService>;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getEditorProjectList', () => {
    it('should return a list of projects', async () => {
      const mockAuthorization = 'Bearer mock-token';
      const mockPaginationOptions = { perPage: 10, offset: 0 };
      const mockStatus = '0,1,2';
      const mockSearch = '';
      const mockResponse: any = { data: 'mockData' };

      jest.spyOn(service, 'getEditorProjects').mockResolvedValue(mockResponse);

      const req = {
        headers: {
          authorization: mockAuthorization,
        },
      };

      expect(
        await controller.getEditorProjectList(
          req,
          mockPaginationOptions,
          mockStatus,
          mockSearch,
        ),
      ).toEqual(mockResponse);
      expect(service.getEditorProjects).toHaveBeenCalledWith(
        mockAuthorization,
        mockPaginationOptions,
        mockStatus,
        mockSearch,
      );
    });
  });

  describe('getProjectById', () => {
    it('should return project details', async () => {
      const mockProjectId = '123';
      const mockAuthorization = 'Bearer mock-token';
      const mockResponse: any = { data: 'mockData' };

      jest.spyOn(service, 'getProjectById').mockResolvedValue(mockResponse);

      const req = {
        headers: {
          authorization: mockAuthorization,
        },
      };

      const result = await controller.getProjectById(mockProjectId, req);

      expect(result).toEqual(mockResponse);
      expect(service.getProjectById).toHaveBeenCalledWith(
        mockProjectId,
        mockAuthorization,
      );
    });

    it('should throw an error if service fails', async () => {
      const mockProjectId = '123';
      const mockAuthorization = 'Bearer mock-token';
      const mockError = new HttpException('Forbidden', 403);

      jest.spyOn(service, 'getProjectById').mockRejectedValue(mockError);

      const req = {
        headers: {
          authorization: mockAuthorization,
        },
      };

      await expect(
        controller.getProjectById(mockProjectId, req),
      ).rejects.toThrow(HttpException);
    });
  });

  // describe('getProjectOutputGroup', () => {
  //   it('should return project outputs with status 200 on success', async () => {
  //     const mockProjectId = 1;
  //     const mockAuthorization = 'Bearer valid-token';
  //     const mockServiceResponse = { data: 'some data', statusCode: 200 };

  //     jest
  //       .spyOn(service, 'getProjectOutputGroup')
  //       .mockResolvedValue(mockServiceResponse);

  //     await controller.getProjectOutputGroup(
  //       mockProjectId,
  //       { headers: { authorization: mockAuthorization } },
  //       res,
  //     );

  //     expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
  //     expect(res.json).toHaveBeenCalledWith(mockServiceResponse.data);
  //   });

  //   it('should return 403 when unauthorized', async () => {
  //     const mockProjectId = 1;
  //     const mockAuthorization = 'Bearer invalid-token';
  //     const mockServiceResponse = {
  //       data: {
  //         message:
  //           'User is not authorized to access this resource with an explicit deny',
  //       },
  //       statusCode: 403,
  //     };

  //     jest
  //       .spyOn(service, 'getProjectOutputGroup')
  //       .mockResolvedValue(mockServiceResponse);

  //     await controller.getProjectOutputGroup(
  //       mockProjectId,
  //       { headers: { authorization: mockAuthorization } },
  //       res,
  //     );

  //     expect(res.status).toHaveBeenCalledWith(HttpStatus.FORBIDDEN);
  //     expect(res.json).toHaveBeenCalledWith(mockServiceResponse.data);
  //   });

  //   it('should return 404 when project not found', async () => {
  //     const mockProjectId = 999; // assuming this ID does not exist
  //     const mockAuthorization = 'Bearer valid-token';
  //     const mockServiceResponse = {
  //       data: {
  //         status: 'error',
  //         error: {
  //           message: 'Project not found.',
  //         },
  //       },
  //       statusCode: 404,
  //     };

  //     jest
  //       .spyOn(service, 'getProjectOutputGroup')
  //       .mockResolvedValue(mockServiceResponse);

  //     await controller.getProjectOutputGroup(
  //       mockProjectId,
  //       { headers: { authorization: mockAuthorization } },
  //       res,
  //     );

  //     expect(res.status).toHaveBeenCalledWith(HttpStatus.NOT_FOUND);
  //     expect(res.json).toHaveBeenCalledWith(mockServiceResponse.data);
  //   });
  // });

  describe('getProjectAssetsAndFolders', () => {
    const projectId = 123;
    const authorization = 'Bearer mock-token';
    const paginationOptions = { limit: 10, offset: 0 };
    const search = '';

    it('should return folders and assets for a specific project', async () => {
      const mockResponse = {
        folders: [
          {
            id: 123456,
            name: 'Marketing Materials',
            dateCreated: '2024-04-01T10:00:00Z',
            isSystemFolder: false,
            parentFolderId: 123455,
          },
        ],
        assets: [
          {
            id: 654321,
            name: 'Company Brochure.pdf',
            dateCreated: '2024-04-01T10:30:00Z',
            fileSize: 102400,
            fileType: 'application/pdf',
            folderId: 123456,
          },
        ],
        pagination: {
          offset: 0,
          perPage: 10,
          nextOffset: 10,
          totalSize: 1,
        },
      };

      jest
        .spyOn(service, 'getProjectAssetsAndFolders')
        .mockResolvedValue(mockResponse);

      const req = { headers: { authorization } };
      const result = await controller.getProjectAssetsAndFolders(
        projectId,
        paginationOptions,
        req,
        search,
      );

      expect(service.getProjectAssetsAndFolders).toHaveBeenCalledWith(
        projectId,
        authorization,
        paginationOptions,
        search,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw ForbiddenException when unauthorized', async () => {
      jest
        .spyOn(service, 'getProjectAssetsAndFolders')
        .mockRejectedValue(new ForbiddenException()); // Correct usage of spyOn

      const req = { headers: { authorization } };
      await expect(
        controller.getProjectAssetsAndFolders(
          projectId,
          paginationOptions,
          req,
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('getNestedFolderContents', () => {
    const projectId = 123;
    const folderId = 456;
    const authorization = 'BEARER mock-token';
    const paginationOptions = { limit: 10, offset: 0 };
    const search = '';

    it('should return nested folders and assets for a specific project and folder', async () => {
      const mockResponse = {
        folders: [
          {
            id: 123456,
            name: 'Marketing Materials',
            dateCreated: '2024-04-01T10:00:00Z',
            isSystemFolder: false,
            parentFolderId: 123455,
          },
        ],
        assets: [
          {
            id: 654321,
            name: 'Company Brochure.pdf',
            dateCreated: '2024-04-01T10:30:00Z',
            fileSize: 102400,
            fileType: 'application/pdf',
            folderId: 123456,
          },
        ],
        pagination: {
          offset: 0,
          perPage: 10,
          nextOffset: 10,
          totalSize: 1,
        },
      };

      jest
        .spyOn(service, 'getNestedFolderContents')
        .mockResolvedValue(mockResponse);

      const req = { headers: { authorization } };
      const result = await controller.getNestedFolderContents(
        projectId,
        folderId,
        paginationOptions,
        req,
        search,
      );

      expect(service.getNestedFolderContents).toHaveBeenCalledWith(
        projectId,
        folderId,
        authorization,
        paginationOptions,
        search,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw ForbiddenException when unauthorized', async () => {
      jest
        .spyOn(service, 'getNestedFolderContents')
        .mockRejectedValue(new ForbiddenException());

      const req = { headers: { authorization } };
      await expect(
        controller.getNestedFolderContents(
          projectId,
          folderId,
          paginationOptions,
          req,
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('getProjectInsights', () => {
    const projectId = 123;
    const authorization = 'some-token';
    const paginationOptions = { perPage: 10, offset: 0 };

    it('should return insights for a specific project', async () => {
      const mockResponse = {
        result: [
          {
            id: 123456,
            name: 'User Engagement',
            dateCreated: '2024-04-01T10:00:00Z',
            type: 'engagement',
            value: 0.75,
          },
        ],
        pagination: {
          offset: 0,
          perPage: 10,
          nextOffset: 10,
          totalSize: 1,
        },
      };

      jest.spyOn(service, 'getProjectInsights').mockResolvedValue(mockResponse);

      const req = { headers: { authorization } };
      const result = await controller.getProjectInsights(projectId, req);

      expect(service.getProjectInsights).toHaveBeenCalledWith(
        projectId,
        authorization,
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createScorecardWithIterationMedia', () => {
    it('should successfully create scorecard with iteration media', async () => {
      const outputVideoId = 123;
      const dto: CreateScorecardIterationMediaDto =
        new CreateScorecardIterationMediaDto();
      const authorization = 'Bearer some-token';
      const userId = 456;
      const req = { userId, headers: { authorization } } as unknown as Request;

      const expectedResponse = {
        mediaId: 196150,
        scorecardId: 21002,
      };

      resultService.createScorecardWithIterationMedia.mockResolvedValue(
        expectedResponse,
      );

      const result = await controller.createScorecardWithIterationMedia(
        outputVideoId,
        dto,
        req,
      );

      expect(
        resultService.createScorecardWithIterationMedia,
      ).toHaveBeenCalledWith(outputVideoId, dto, authorization, userId);
      expect(result).toEqual(expectedResponse);
    });

    it('should throw HttpException on service failure', async () => {
      const outputVideoId = 123;
      const dto: CreateScorecardIterationMediaDto =
        new CreateScorecardIterationMediaDto();
      const authorization = 'Bearer some-token';
      const userId = 456;
      const req = { userId, headers: { authorization } } as unknown as Request;
      const error = new HttpException(
        'Service Unavailable',
        HttpStatus.SERVICE_UNAVAILABLE,
      );

      resultService.createScorecardWithIterationMedia.mockRejectedValue(error);

      await expect(
        controller.createScorecardWithIterationMedia(outputVideoId, dto, req),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('createIterationMediaAnnotation', () => {
    it('should successfully create an annotation on iteration media', async () => {
      const iterationMediaId = 123;
      const dto = new CreateAnnotationIterationMediaDto();
      const authorization = 'Bearer some-token';
      const expectedResponse = {
        mediaId: 196150,
        scorecardId: 21002,
      };

      service.createIterationMediaAnnotation = jest
        .fn()
        .mockResolvedValue(expectedResponse);

      const mockReq = { headers: { authorization } };

      const result = await controller.createIterationMediaAnnotation(
        iterationMediaId,
        dto,
        mockReq,
      );

      expect(service.createIterationMediaAnnotation).toHaveBeenCalledWith(
        iterationMediaId,
        dto,
        authorization,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should throw HttpException on service failure', async () => {
      const iterationMediaId = 123;
      const dto = new CreateAnnotationIterationMediaDto();
      const authorization = 'Bearer some-token';
      const error = new HttpException(
        'Service Unavailable',
        HttpStatus.SERVICE_UNAVAILABLE,
      );

      service.createIterationMediaAnnotation = jest
        .fn()
        .mockRejectedValue(error);

      await expect(
        controller.createIterationMediaAnnotation(iterationMediaId, dto, {
          headers: { authorization },
        }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getIterationAnnotations', () => {
    it('should successfully get annotations for a specific iteration', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const paginationOptions = { offset: 0, perPage: 50 };
      const expectedResponse = {
        data: [
          {
            id: 12345,
            text: 'hello',
            visibility: 'PROJECT',
            contextType: 'DRAFT',
            contextSubType: 'OUTPUT_VIDEO',
            unread: false,
            person: {
              id: 12345,
              displayName: 'Editor',
              jobTitle: null,
              photo: null,
              color: '#EBBA81',
            },
            dateCreated: '2024-06-04T19:45:49Z',
            lastUpdated: '2024-06-04T19:45:49Z',
            scope: 'SINGLE',
            annotationSet: null,
            annotationType: null,
            checked: false,
            attachmentIds: [],
            attachments: [],
            mediaClip: {
              id: 1234,
              mediaId: 12345,
              locationType: 'ALL',
            },
          },
        ],
        pagination: {
          offset: 0,
          perPage: 50,
          nextOffset: 10,
          totalSize: 1,
        },
      };

      service.getIterationAnnotations = jest
        .fn()
        .mockResolvedValue(expectedResponse);

      const result = await controller.getIterationAnnotations(
        iterationMediaId,
        req,
        paginationOptions,
      );

      expect(service.getIterationAnnotations).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should throw HttpException on service failure', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const paginationOptions = { offset: 0, perPage: 50 };
      const error = new HttpException(
        'Service Unavailable',
        HttpStatus.SERVICE_UNAVAILABLE,
      );

      service.getIterationAnnotations = jest.fn().mockRejectedValue(error);

      await expect(
        controller.getIterationAnnotations(
          iterationMediaId,
          req,
          paginationOptions,
        ),
      ).rejects.toThrow(HttpException);

      expect(service.getIterationAnnotations).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
    });
  });

  describe('getIterationMentions', () => {
    it('should successfully get mentions for a specific iteration', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const res = { status: jest.fn().mockReturnThis(), json: jest.fn() };
      const paginationOptions = { offset: 0 };
      const expectedResponse = {
        statusCode: 200,
        data: [
          {
            id: 12345,
            displayName: 'Editor',
            jobTitle: null,
            photo: null,
            color: '#93D6AC',
            relationType: 'EDITOR',
          },
        ],
      };

      service.getIterationMentions = jest
        .fn()
        .mockResolvedValue(expectedResponse);

      await controller.getIterationMentions(
        iterationMediaId,
        req,
        res,
        paginationOptions,
      );

      expect(res.status).toHaveBeenCalledWith(expectedResponse.statusCode);
      expect(res.json).toHaveBeenCalledWith(expectedResponse.data);

      expect(service.getIterationMentions).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
    });

    it('should throw HttpException on service failure', async () => {
      const iterationMediaId = 123;
      const req = { headers: { authorization: 'Bearer some-token' } };
      const paginationOptions = { offset: 0, perPage: 50 };
      const error = new HttpException(
        'Service Unavailable',
        HttpStatus.SERVICE_UNAVAILABLE,
      );

      service.getIterationMentions = jest.fn().mockRejectedValue(error);

      await expect(
        controller.getIterationMentions(
          iterationMediaId,
          req,
          res,
          paginationOptions,
        ),
      ).rejects.toThrow(HttpException);

      expect(service.getIterationMentions).toHaveBeenCalledWith(
        iterationMediaId,
        req.headers.authorization,
        paginationOptions,
      );
    });
  });

  describe('getProjectMessageChannel', () => {
    const projectId = 123;
    const authorization = 'Bearer mock-token';
    const mockRequest = {
      headers: {
        authorization: authorization,
      },
    };

    it('should return the project message channel successfully', async () => {
      const mockMessageChannelResponse = [
        {
          id: 1,
          notifyEmail: true,
          notifyPush: true,
          participants: [
            {
              id: 1,
              displayName: 'User 1',
              type: 'PROJECT_OWNER',
              lastReadMessageId: null,
            },
          ],
          projectId: 123,
          readOnly: false,
          type: 'PROJECT_EDITOR',
          unreadMessages: 5,
          messages: [
            {
              id: 1,
              attachmentIds: [1],
              date: '2024-07-01T14:20:43Z',
              fromUser: {
                id: 1,
                displayName: 'User 1',
                jobTitle: 'Tester',
                photo: 'url',
                color: 'red',
              },
              isCurrentUser: false,
              messageChannelId: 1,
              projectId: 123,
              read: false,
              text: 'Message text',
              type: 'USER',
            },
          ],
        },
      ];

      jest
        .spyOn(service, 'getProjectMessageChannel')
        .mockResolvedValue(mockMessageChannelResponse);

      const result = await controller.getProjectMessageChannel(
        projectId,
        mockRequest,
      );

      expect(result).toEqual(mockMessageChannelResponse);
      expect(service.getProjectMessageChannel).toHaveBeenCalledWith(
        projectId,
        authorization,
      );
    });

    it('should throw a ForbiddenException when service fails', async () => {
      jest
        .spyOn(service, 'getProjectMessageChannel')
        .mockRejectedValue(new ForbiddenException());

      await expect(
        controller.getProjectMessageChannel(projectId, mockRequest),
      ).rejects.toThrow(ForbiddenException);
    });
  });
});
