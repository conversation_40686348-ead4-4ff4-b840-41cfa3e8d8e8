import { projectFromParamsHandler } from 'src/auth/decorators/permission.decorator';
import { PermissionAction } from 'src/auth/enums/permission.action.enum';
import { PermissionDomain } from 'src/auth/enums/permission.domain.enum';
import { PermissionSubResource } from 'src/auth/enums/permission.subresource.enum';

export const readProjectInsightProjectLevel = {
  domain: PermissionDomain.PROJECT,
  domainContextHandler: projectFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.PROJECT_INSIGHT,
    },
  ],
};
