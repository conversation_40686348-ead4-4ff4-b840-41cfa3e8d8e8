import { Controller, Get, Param, Post, Request } from '@nestjs/common';
import {
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiSecurity,
} from '@nestjs/swagger';
import { MessageService } from '../services/message.service';

@ApiTags('Plugin Studio Message')
@ApiSecurity('Bearer Token')
@Controller('plugin/studio/message')
export class MessageController {
  constructor(private readonly messageService: MessageService) {}

  @Get('/:messageId/attachment/:attachmentId')
  @ApiOperation({ summary: 'Retrieve a specific message attachment' })
  @ApiResponse({
    status: 200,
    description: 'Attachment retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Attachment not found',
  })
  @ApiParam({
    name: 'messageId',
    type: 'number',
    required: true,
    description: 'The ID of the message',
  })
  @ApiParam({
    name: 'attachmentId',
    type: 'number',
    required: true,
    description: 'The ID of the attachment',
  })
  async getMessageAttachment(
    @Param('messageId') messageId: number,
    @Param('attachmentId') attachmentId: number,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.messageService.getMessageAttachment(
      messageId,
      attachmentId,
      authorization,
    );
  }

  @Get('/:messageId/messageReadBy')
  @ApiOperation({ summary: 'Get users who read a message' })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Message not found',
  })
  @ApiParam({
    name: 'messageId',
    type: 'string',
    required: true,
    description: 'The ID of the message',
  })
  async getUsersWhoReadMessage(
    @Param('messageId') messageId: string,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.messageService.getUsersWhoReadMessage(
      messageId,
      authorization,
    );
  }

  @Post('/:messageId')
  @ApiOperation({ summary: 'Mark message as read' })
  @ApiResponse({
    status: 200,
    description: 'Message marked as read successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Message not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied',
  })
  @ApiParam({
    name: 'messageId',
    type: 'string',
    required: true,
    description: 'The ID of the message',
  })
  async markMessageAsRead(
    @Param('messageId') messageId: string,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.messageService.markMessageAsRead(
      messageId,
      authorization,
    );
  }
}
