import { Test, TestingModule } from '@nestjs/testing';
import { MessageChannelController } from './message-channel.controller';
import { MessageChannelService } from '../services/message-channel.service';
import { CreateMessageDto } from '../dto/create-message.dto';
import { NotFoundException, HttpException } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  ListMentionsResponseDto,
  MentionRelationType,
} from '../dto/list-mention-response.dto';

describe('MessageChannelController', () => {
  let controller: MessageChannelController;
  let service: MessageChannelService;

  const mockMessageChannelService = {
    createMessageInProjectChannel: jest.fn(),
    getMentionsInChannel: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MessageChannelController],
      providers: [
        {
          provide: MessageChannelService,
          useValue: mockMessageChannelService,
        },
      ],
    }).compile();

    controller = module.get<MessageChannelController>(MessageChannelController);
    service = module.get<MessageChannelService>(MessageChannelService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createMessageInProjectChannel', () => {
    const messageChannelId = 1;
    const authorization = 'Bearer token';
    const createMessageDto: CreateMessageDto = {
      filename: 'example_image.png',
      size: 225587,
      text: 'This is a test message with an attachment.',
    };

    it('should create a message with an attachment', async () => {
      mockMessageChannelService.createMessageInProjectChannel.mockResolvedValue(
        {
          id: 1,
          ...createMessageDto,
        },
      );

      const result = await controller.createMessageInProjectChannel(
        messageChannelId,
        createMessageDto,
        { headers: { authorization } },
      );

      expect(result).toEqual({
        id: 1,
        ...createMessageDto,
      });
      expect(
        mockMessageChannelService.createMessageInProjectChannel,
      ).toHaveBeenCalledWith(messageChannelId, createMessageDto, authorization);
    });

    it('should create a message without an attachment', async () => {
      const createMessageDtoWithoutAttachment: CreateMessageDto = {
        text: 'This is a test message without an attachment.',
      };

      mockMessageChannelService.createMessageInProjectChannel.mockResolvedValue(
        {
          id: 2,
          ...createMessageDtoWithoutAttachment,
        },
      );

      const result = await controller.createMessageInProjectChannel(
        messageChannelId,
        createMessageDtoWithoutAttachment,
        { headers: { authorization } },
      );

      expect(result).toEqual({
        id: 2,
        ...createMessageDtoWithoutAttachment,
      });
      expect(
        mockMessageChannelService.createMessageInProjectChannel,
      ).toHaveBeenCalledWith(
        messageChannelId,
        createMessageDtoWithoutAttachment,
        authorization,
      );
    });

    it('should throw NotFoundException if project is not found', async () => {
      mockMessageChannelService.createMessageInProjectChannel.mockRejectedValue(
        new NotFoundException('Project not found'),
      );

      await expect(
        controller.createMessageInProjectChannel(
          messageChannelId,
          createMessageDto,
          { headers: { authorization } },
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw HttpException if attachment data is missing or incorrect', async () => {
      const createMessageDtoWithMissingData: CreateMessageDto = {
        filename: 'example_image.png',
        text: 'This is a test message with an invalid attachment.',
      };

      mockMessageChannelService.createMessageInProjectChannel.mockRejectedValue(
        new HttpException('Error uploading media', 500),
      );

      await expect(
        controller.createMessageInProjectChannel(
          messageChannelId,
          createMessageDtoWithMissingData,
          { headers: { authorization } },
        ),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getMentionsInChannel', () => {
    const messageChannelId = 1;
    const authorization = 'Bearer token';
    const paginationOptions: PaginationOptions = { perPage: 10, offset: 0 };

    it('should return mentions with pagination', async () => {
      const mockResponse: ListMentionsResponseDto = {
        status: 'OK',
        result: [
          {
            id: 12345,
            displayName: 'John Doe',
            jobTitle: null,
            photo: 'https://example.com/default_profile_icon.png',
            color: '#93D6AC',
            relationType: MentionRelationType.PROJECT_OWNER,
          },
          // other mention objects
        ],
        pagination: {
          offset: 0,
          perPage: 10,
          nextOffset: 10,
          totalSize: 20,
          queryId: 'abcd1234-5678-90ef-ghij-klmnopqrstuv',
        },
      };

      mockMessageChannelService.getMentionsInChannel.mockResolvedValueOnce(
        mockResponse,
      );

      const req = { headers: { authorization } };

      const result = await controller.getMentionsInChannel(
        messageChannelId,
        req,
        paginationOptions,
      );

      expect(result).toEqual(mockResponse);
      expect(service.getMentionsInChannel).toHaveBeenCalledWith(
        messageChannelId,
        paginationOptions,
        authorization,
      );
    });

    it('should throw HttpException on error', async () => {
      const mockError = new HttpException('Forbidden', 403);

      mockMessageChannelService.getMentionsInChannel.mockRejectedValueOnce(
        mockError,
      );

      const req = { headers: { authorization } };

      await expect(
        controller.getMentionsInChannel(
          messageChannelId,
          req,
          paginationOptions,
        ),
      ).rejects.toThrow(HttpException);
    });
  });
});
