import { Test, TestingModule } from '@nestjs/testing';
import { MessageController } from './message.controller';
import { MessageService } from '../services/message.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

describe('MessageController', () => {
  let controller: MessageController;
  let service: MessageService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MessageController],
      providers: [
        MessageService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(() => 'http://example.com'),
          },
        },
      ],
    }).compile();

    controller = module.get<MessageController>(MessageController);
    service = module.get<MessageService>(MessageService);
  });

  describe('getMessageAttachment', () => {
    it('should retrieve a message attachment successfully', async () => {
      const mockResponse = {
        status: 'OK',
        result: {
          id: 2588,
          media: {
            thumbnail: 'https://example.com/thumbnail.png',
            url: 'https://example.com/file.png',
            downloadUrl: 'https://example.com/file.png?download=true',
          },
        },
      };
      jest
        .spyOn(service, 'getMessageAttachment')
        .mockResolvedValue(mockResponse);

      const result = await controller.getMessageAttachment(1, 1, {
        headers: { authorization: 'Bearer token' },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should throw an HttpException when an error occurs', async () => {
      const errorMessage = 'Attachment not found';
      jest
        .spyOn(service, 'getMessageAttachment')
        .mockRejectedValue(
          new HttpException(errorMessage, HttpStatus.NOT_FOUND),
        );

      await expect(
        controller.getMessageAttachment(1, 1, {
          headers: { authorization: 'Bearer token' },
        }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getUsersWhoReadMessage', () => {
    it('should retrieve users who read a message successfully', async () => {
      const mockResponse = {
        status: 'OK',
        result: [
          {
            id: 12345,
            displayName: 'John Doe',
            jobTitle: 'Developer',
            photo: 'https://example.com/default_profile_icon.png',
            lastLogin: '2023-07-08T17:08:24Z',
          },
        ],
        pagination: {
          offset: 0,
          perPage: 50,
          nextOffset: 1,
          totalSize: 1,
          queryId: 'abcd1234-5678-90ef-ghij-klmnopqrstuv',
        },
      };
      jest
        .spyOn(service, 'getUsersWhoReadMessage')
        .mockResolvedValue(mockResponse);

      const result = await controller.getUsersWhoReadMessage('1', {
        headers: { authorization: 'Bearer token' },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should throw an HttpException when an error occurs', async () => {
      const errorMessage = 'Message not found';
      jest
        .spyOn(service, 'getUsersWhoReadMessage')
        .mockRejectedValue(
          new HttpException(errorMessage, HttpStatus.NOT_FOUND),
        );

      await expect(
        controller.getUsersWhoReadMessage('1', {
          headers: { authorization: 'Bearer token' },
        }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('markMessageAsRead', () => {
    it('should mark a message as read successfully', async () => {
      const mockResponse = {
        status: 'OK',
        result: {
          id: 9523,
          messageChannelId: 17076,
          projectId: 15521,
          type: 'USER',
          from: {
            id: 10414,
            displayName: 'Display Name from Postman',
            jobTitle: null,
            photo:
              'https://vidmob-storage-dev.s3.amazonaws.com/SSP2Q5SNSK/avatar/avatar4932.jpg',
            color: '#EBBA81',
          },
          text: 'This is the updated Postman Text',
          date: '2019-12-05T21:41:12Z',
          read: true,
          isCurrentUser: true,
          attachmentIds: [],
        },
      };
      jest.spyOn(service, 'markMessageAsRead').mockResolvedValue(mockResponse);

      const result = await controller.markMessageAsRead('1', {
        headers: { authorization: 'Bearer token' },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should throw an HttpException when an error occurs', async () => {
      const errorMessage = 'Message not found';
      jest
        .spyOn(service, 'markMessageAsRead')
        .mockRejectedValue(
          new HttpException(errorMessage, HttpStatus.NOT_FOUND),
        );

      await expect(
        controller.markMessageAsRead('1', {
          headers: { authorization: 'Bearer token' },
        }),
      ).rejects.toThrow(HttpException);
    });
  });
});
