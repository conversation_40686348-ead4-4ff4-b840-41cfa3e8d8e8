import { Body, Controller, Get, Param, Post, Request } from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiSecurity,
} from '@nestjs/swagger';
import { MessageChannelService } from '../services/message-channel.service';
import { CreateMessageDto } from '../dto/create-message.dto';
import { ListMentionsResponseDto } from '../dto/list-mention-response.dto';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';

@ApiTags('Plugin Studio Message Channel')
@ApiSecurity('Bearer Token')
@Controller('plugin/studio/messageChannel')
export class MessageChannelController {
  constructor(private readonly messageService: MessageChannelService) {}

  @Post('/:messageChannelId/message')
  @ApiOperation({ summary: 'Create a message in a channel' })
  @ApiResponse({
    status: 200,
    description: 'Message created successfully',
    type: null,
  })
  @ApiParam({
    name: 'messageChannelId',
    type: 'number',
    required: true,
    description: 'The ID of the message channel',
  })
  @ApiBody({
    type: CreateMessageDto,
    description: 'The message to be created',
  })
  async createMessageInProjectChannel(
    @Param('messageChannelId') messageChannelId: number,
    @Body() body: CreateMessageDto,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    return await this.messageService.createMessageInProjectChannel(
      messageChannelId,
      body,
      authorization,
    );
  }

  @Get('/:messageChannelId/mention')
  @ApiOperation({ summary: 'Get mentions in a message channel' })
  @ApiResponse({
    status: 200,
    description: 'Mentions retrieved successfully',
    type: ListMentionsResponseDto,
  })
  @ApiParam({
    name: 'messageChannelId',
    type: 'number',
    required: true,
    description: 'The ID of the message channel',
  })
  async getMentionsInChannel(
    @Param('messageChannelId') messageChannelId: number,
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const authorization = req.headers.authorization;
    return await this.messageService.getMentionsInChannel(
      messageChannelId,
      paginationOptions,
      authorization,
    );
  }
}
