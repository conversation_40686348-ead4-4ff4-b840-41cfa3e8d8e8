import { Test, TestingModule } from '@nestjs/testing';
import { MessageChannelService } from './message-channel.service';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { CreateMessageDto } from '../dto/create-message.dto';
import {
  CHAT_ATTACHMENT_MEDIA_SOURCE,
  CHAT_ATTACHMENT_MEDIA_TYPE,
} from '../../../../constants/studio.constants';
import { HttpException } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('MessageChannelService', () => {
  let service: MessageChannelService;

  const baseUrlApi = 'https://api-dev.vidmob.com/VidMob';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageChannelService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue(baseUrlApi),
          },
        },
      ],
    }).compile();

    service = module.get<MessageChannelService>(MessageChannelService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createMessageInProjectChannel', () => {
    const messageChannelId = 1;
    const authorization = 'Bearer token';
    const createMessageDto: CreateMessageDto = {
      filename: 'example_image.png',
      size: 225587,
      text: 'This is a test message with an attachment.',
    };

    it('should create a message with an attachment', async () => {
      const mediaResponse = {
        data: {
          status: 'OK',
          result: {
            id: 197843,
            name: 'example_image.png',
          },
        },
      };

      const messageResponse = {
        data: {
          status: 'OK',
          result: {
            id: 14330,
            messageChannelId: 35191,
            text: 'This is a test message with an attachment.',
            attachmentIds: [1234],
            mediaIds: [197843],
          },
        },
      };

      mockedAxios.post
        .mockResolvedValueOnce(mediaResponse)
        .mockResolvedValueOnce(messageResponse);

      const result = await service.createMessageInProjectChannel(
        messageChannelId,
        createMessageDto,
        authorization,
      );

      expect(result).toEqual(messageResponse.data);
      expect(mockedAxios.post).toHaveBeenCalledTimes(2);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${baseUrlApi}/api/v1/user/media`,
        {
          name: createMessageDto.filename,
          size: createMessageDto.size,
          assetIdentifier: `chat-attachment${createMessageDto.filename}`,
          source: CHAT_ATTACHMENT_MEDIA_SOURCE,
          mediaType: CHAT_ATTACHMENT_MEDIA_TYPE,
        },
        {
          headers: { Authorization: authorization },
        },
      );
      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${baseUrlApi}/api/v2/messageChannel/${messageChannelId}/message`,
        {
          text: createMessageDto.text,
          attachmentId: 197843,
        },
        {
          headers: { Authorization: authorization },
        },
      );
    });

    it('should create a message without an attachment', async () => {
      const createMessageDtoWithoutAttachment: CreateMessageDto = {
        text: 'This is a test message without an attachment.',
      };

      const messageResponse = {
        data: {
          status: 'OK',
          result: {
            id: 14331,
            messageChannelId: 35191,
            text: 'This is a test message without an attachment.',
            attachmentIds: [],
            mediaIds: [],
          },
        },
      };

      mockedAxios.post.mockResolvedValueOnce(messageResponse);

      const result = await service.createMessageInProjectChannel(
        messageChannelId,
        createMessageDtoWithoutAttachment,
        authorization,
      );

      const mediaAttachmentId = [];

      expect(result).toEqual(messageResponse.data);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${baseUrlApi}/api/v2/messageChannel/${messageChannelId}/message`,
        {
          text: createMessageDtoWithoutAttachment.text,
          attachmentId: mediaAttachmentId[0],
        },
        {
          headers: { Authorization: authorization },
        },
      );
    });

    it('should throw BadRequestException if only filename is provided without size and text', async () => {
      const createMessageDtoWithFilenameOnly: CreateMessageDto = {
        filename: 'example_image.png',
      };

      await expect(
        service.createMessageInProjectChannel(
          messageChannelId,
          createMessageDtoWithFilenameOnly,
          authorization,
        ),
      ).rejects.toThrow(
        'Either both filename and size must be present, or text must be provided in the message',
      );
    });
  });

  describe('getMentionsInChannel', () => {
    const messageChannelId = 1;
    const authorization = 'Bearer token';
    const paginationOptions: PaginationOptions = { perPage: 10, offset: 0 };
    const mentionEndpoint = `${baseUrlApi}/api/v2/messageChannel/${messageChannelId}/mention`;

    it('should return mentions with pagination', async () => {
      const mockResponse = {
        data: {
          status: 'OK',
          result: [
            {
              id: 12345,
              displayName: 'John Doe',
              jobTitle: null,
              photo: null,
              color: '#EBBA81',
              relationType: 'PROJECT_OWNER',
            },
            // other mention objects
          ],
          pagination: {
            offset: 0,
            perPage: 10,
            nextOffset: 10,
            totalSize: 20,
            queryId: '19ab0ab6-3a60-4997-b10b-329c65443cbe',
          },
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await service.getMentionsInChannel(
        messageChannelId,
        paginationOptions,
        authorization,
      );

      expect(result).toEqual(mockResponse.data);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${mentionEndpoint}?messageChannelId=1&perPage=10&offset=0`,
        { headers: { Authorization: authorization } },
      );
    });

    it('should throw HttpException on error', async () => {
      const mockError = { message: 'Forbidden', response: { status: 403 } };

      mockedAxios.get.mockRejectedValueOnce(mockError);

      await expect(
        service.getMentionsInChannel(
          messageChannelId,
          paginationOptions,
          authorization,
        ),
      ).rejects.toThrow(HttpException);
    });
  });
});
