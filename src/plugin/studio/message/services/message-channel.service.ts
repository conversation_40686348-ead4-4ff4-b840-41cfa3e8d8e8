import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { CreateMessageDto } from '../dto/create-message.dto';
import {
  CHAT_ATTACHMENT_MEDIA_SOURCE,
  CHAT_ATTACHMENT_MEDIA_TYPE,
} from '../../../../constants/studio.constants';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

@Injectable()
export class MessageChannelService {
  baseUrlApi: string;

  constructor(private configService: ConfigService) {
    this.baseUrlApi = this.configService.get<string>('baseVidMobApiUrl', '');
  }

  async createMessageInProjectChannel(
    messageChannelId: number,
    body: CreateMessageDto,
    authorization: string,
  ) {
    if ((!body.filename || !body.size) && !body.text) {
      throw new BadRequestException(
        'Either both filename and size must be present, or text must be provided in the message',
      );
    }

    if (!body.text) {
      body.text = '';
    }

    const mediaAttachmentId = [];

    if (body.filename) {
      const mediaRequest = {
        name: body.filename,
        size: body.size,
        assetIdentifier: `chat-attachment${body.filename}`,
        source: CHAT_ATTACHMENT_MEDIA_SOURCE,
        mediaType: CHAT_ATTACHMENT_MEDIA_TYPE,
      };

      // Validate media attachment creation request
      this.validateAttachmentMediaRequest(mediaRequest);

      try {
        // Upload the media first
        const mediaResponse = await axios.post(
          `${this.baseUrlApi}/api/v1/user/media`,
          mediaRequest,
          {
            headers: { Authorization: authorization },
          },
        );

        const mediaResult = mediaResponse.data.result;
        mediaAttachmentId.push(mediaResult.id);
      } catch (error) {
        throw new HttpException(error.response.data, error.response.status);
      }
    }

    // Create the message
    try {
      const messageResponse = await axios.post(
        `${this.baseUrlApi}/api/v2/messageChannel/${messageChannelId}/message`,
        {
          text: body.text,
          attachmentId: mediaAttachmentId[0],
        },
        {
          headers: { Authorization: authorization },
        },
      );

      const messageData = {
        ...messageResponse.data,
        result: {
          ...messageResponse.data.result,
          fromUser: messageResponse.data.result.from,
          mediaIds: mediaAttachmentId,
        },
      };

      delete messageData.result.from;

      return messageData;
    } catch (error) {
      throw new HttpException(error.response.data, error.response.status);
    }
  }

  private validateAttachmentMediaRequest(mediaRequest: any) {
    const requiredFields = [
      'name',
      'size',
      'assetIdentifier',
      'source',
      'mediaType',
    ];
    const missingFields = requiredFields.filter(
      (field) => !mediaRequest[field],
    );

    if (missingFields.length > 0) {
      throw new BadRequestException(
        `Missing required fields to create attachment: ${missingFields.join(
          ', ',
        )}`,
      );
    }
  }

  async getMentionsInChannel(
    messageChannelId: number,
    paginationOptions: PaginationOptions,
    authorization: string,
  ) {
    const mentionEndpoint = `${this.baseUrlApi}/api/v2/messageChannel/${messageChannelId}/mention`;

    const queryParams = new URLSearchParams({
      messageChannelId: messageChannelId.toString(),
      perPage: paginationOptions.perPage.toString(),
      offset: paginationOptions.offset.toString(),
    });

    try {
      const response = await axios.get(`${mentionEndpoint}?${queryParams}`, {
        headers: { Authorization: authorization },
      });

      return response.data;
    } catch (error) {
      throw new HttpException(error.message, error.response.status);
    }
  }
}
