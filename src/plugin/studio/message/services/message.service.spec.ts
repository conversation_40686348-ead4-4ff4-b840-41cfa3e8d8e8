import { Test, TestingModule } from '@nestjs/testing';
import { MessageService } from './message.service';
import axios from 'axios';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('MessageService', () => {
  let service: MessageService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(() => 'http://example.com'),
          },
        },
      ],
    }).compile();

    service = module.get<MessageService>(MessageService);

    jest.clearAllMocks();
  });

  describe('getMessageAttachment', () => {
    it('should retrieve a message attachment successfully', async () => {
      const mockResponse = {
        data: {
          status: 'OK',
          result: {
            id: 2588,
            media: {
              thumbnail: 'https://example.com/thumbnail.png',
              url: 'https://example.com/file.png',
              downloadUrl: 'https://example.com/file.png?download=true',
            },
          },
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await service.getMessageAttachment(1, 1, 'Bearer token');
      expect(result).toEqual(mockResponse.data);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'http://example.com/api/v2/message/1/attachment/1',
        { headers: { Authorization: 'Bearer token' } },
      );
    });

    it('should throw an HttpException when an attachment is not found', async () => {
      const errorMessage = 'Attachment not found';
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 404,
          data: errorMessage,
        },
      });

      await expect(
        service.getMessageAttachment(1, 1, 'Bearer token'),
      ).rejects.toThrow(new HttpException(errorMessage, HttpStatus.NOT_FOUND));
    });

    it('should throw an HttpException when access is denied', async () => {
      const errorResponse = {
        response: {
          status: 403,
          data: {
            status: 'ERROR',
            error: {
              identifier: 'vidmob.api-bff.accessviolation',
              type: 'accessViolation',
              system: 'project',
              message: 'Access denied.',
              data: {
                item: 'project',
                value: '3344',
              },
            },
          },
        },
      };
      mockedAxios.get.mockRejectedValue(errorResponse);

      await expect(
        service.getMessageAttachment(1, 1, 'Bearer token'),
      ).rejects.toThrow(
        new HttpException(errorResponse.response.data, HttpStatus.FORBIDDEN),
      );
    });
  });

  describe('getMessageReadBy', () => {
    it('should retrieve users who read a message successfully', async () => {
      const mockResponse = {
        data: {
          status: 'OK',
          result: [
            {
              id: 12345,
              displayName: 'John Doe',
              jobTitle: 'Developer',
              photo: 'https://example.com/default_profile_icon.png',
              lastLogin: '2022-02-23T17:08:24Z',
            },
          ],
          pagination: {
            offset: 0,
            perPage: 50,
            nextOffset: 1,
            totalSize: 1,
            queryId: 'abcd1234-5678-90ef-ghij-klmnopqrstuv',
          },
        },
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await service.getUsersWhoReadMessage('1', 'Bearer token');
      expect(result).toEqual(mockResponse.data);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'http://example.com/api/v2/message/1/messageReadBy?messageId=1',
        { headers: { Authorization: 'Bearer token' } },
      );
    });

    it('should throw an HttpException when access is denied', async () => {
      const errorResponse = {
        response: {
          status: 403,
          data: {
            status: 'ERROR',
            error: {
              identifier: 'vidmob.api-bff.accessviolation',
              type: 'accessViolation',
              system: 'message',
              message: 'Access denied.',
              data: {
                item: 'message',
                value: '3344',
              },
            },
          },
        },
      };
      mockedAxios.get.mockRejectedValue(errorResponse);

      await expect(
        service.getUsersWhoReadMessage('1', 'Bearer token'),
      ).rejects.toThrow(
        new HttpException(errorResponse.response.data, HttpStatus.FORBIDDEN),
      );
    });

    it('should throw an HttpException when the message is not found', async () => {
      const errorMessage = 'This message does not exist.';
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 404,
          data: errorMessage,
        },
      });

      await expect(
        service.getUsersWhoReadMessage('1', 'Bearer token'),
      ).rejects.toThrow(new HttpException(errorMessage, HttpStatus.NOT_FOUND));
    });
  });

  describe('markMessageAsRead', () => {
    it('should mark a message as read successfully', async () => {
      const mockResponse = {
        data: {
          status: 'OK',
          result: {
            id: 9523,
            messageChannelId: 17076,
            projectId: 15521,
            type: 'USER',
            from: {
              id: 10414,
              displayName: 'Display Name from Postman',
              jobTitle: null,
              photo:
                'https://vidmob-storage-dev.s3.amazonaws.com/SSP2Q5SNSK/avatar/avatar4932.jpg',
              color: '#EBBA81',
            },
            text: 'This is the updated Postman Text',
            date: '2019-12-05T21:41:12Z',
            read: true,
            isCurrentUser: true,
            attachmentIds: [],
          },
        },
      };
      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await service.markMessageAsRead('1', 'Bearer token');
      expect(result).toEqual(mockResponse.data);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://example.com/api/v2/message/1',
        { isRead: true },
        { headers: { Authorization: 'Bearer token' } },
      );
    });

    it('should throw an HttpException when a message is not found', async () => {
      const errorMessage = 'Message not found';
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 404,
          data: errorMessage,
        },
      });

      await expect(
        service.markMessageAsRead('1', 'Bearer token'),
      ).rejects.toThrow(new HttpException(errorMessage, HttpStatus.NOT_FOUND));
    });

    it('should throw an HttpException when access is denied', async () => {
      const errorResponse = {
        response: {
          status: 403,
          data: {
            status: 'ERROR',
            error: {
              identifier: 'vidmob.api-bff.accessviolation',
              type: 'accessViolation',
              system: 'project',
              message: 'Access denied.',
              data: {
                item: 'project',
                value: '3344',
              },
            },
          },
        },
      };
      mockedAxios.post.mockRejectedValue(errorResponse);

      await expect(
        service.markMessageAsRead('1', 'Bearer token'),
      ).rejects.toThrow(
        new HttpException(errorResponse.response.data, HttpStatus.FORBIDDEN),
      );
    });
  });
});
