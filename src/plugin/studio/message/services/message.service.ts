import { HttpException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class MessageService {
  baseUrlApi: string;

  constructor(private configService: ConfigService) {
    this.baseUrlApi = this.configService.get<string>('baseVidMobApiUrl', '');
  }

  async getMessageAttachment(
    messageId: number,
    attachmentId: number,
    authorization: string,
  ) {
    try {
      const response = await axios.get(
        `${this.baseUrlApi}/api/v2/message/${messageId}/attachment/${attachmentId}`,
        {
          headers: { Authorization: authorization },
        },
      );

      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        throw new HttpException('Attachment not found', 404);
      }
      throw new HttpException(
        error.response ? error.response.data : 'An error occurred',
        error.response ? error.response.status : 500,
      );
    }
  }

  async getUsersWhoReadMessage(messageId: string, authorization: string) {
    try {
      const response = await axios.get(
        `${this.baseUrlApi}/api/v2/message/${messageId}/messageReadBy?messageId=${messageId}`,
        {
          headers: { Authorization: authorization },
        },
      );

      return response.data;
    } catch (error) {
      throw new HttpException(
        error.response ? error.response.data : 'An error occurred',
        error.response ? error.response.status : 500,
      );
    }
  }

  async markMessageAsRead(messageId: string, authorization: string) {
    try {
      const response = await axios.post(
        `${this.baseUrlApi}/api/v2/message/${messageId}`,
        { isRead: true },
        {
          headers: { Authorization: authorization },
        },
      );

      return response.data;
    } catch (error) {
      throw new HttpException(
        error.response ? error.response.data : 'An error occurred',
        error.response ? error.response.status : 500,
      );
    }
  }
}
