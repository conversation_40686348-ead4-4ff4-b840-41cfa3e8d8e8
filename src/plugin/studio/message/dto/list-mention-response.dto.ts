import { ApiProperty } from '@nestjs/swagger';
import { PaginationResponse } from '@vidmob/vidmob-nestjs-common';

export enum MentionRelationType {
  PROJECT_OWNER = 'PROJECT_OWNER',
  COLLABORATOR = 'COLLABORATOR',
  CONTRIBUTOR = 'CONTRIBUTOR',
  EDITOR = 'EDITOR',
  PARTNER_USER = 'PARTNER_USER',
  OTHER = 'OTHER',
}

export class MentionDto {
  @ApiProperty({ example: 12345 })
  id: number;

  @ApiProperty({ example: 'Testing Plugin User' })
  displayName: string;

  @ApiProperty({ example: null, nullable: true })
  jobTitle?: string | null;

  @ApiProperty({ example: null, nullable: true })
  photo?: string | null;

  @ApiProperty({ example: '#93D6AC' })
  color: string;

  @ApiProperty({
    enum: MentionRelationType,
    example: MentionRelationType.EDITOR,
  })
  relationType: MentionRelationType;
}

export class ListMentionsResponseDto {
  @ApiProperty({ example: 'OK' })
  status: string;

  @ApiProperty({ type: [MentionDto] })
  result: MentionDto[];

  @ApiProperty({ type: PaginationResponse })
  pagination: PaginationResponse;
}
