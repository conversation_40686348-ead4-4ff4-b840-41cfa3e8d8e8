import { IsOptional, IsString, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMessageDto {
  @ApiProperty({
    description: 'The name of the file to be uploaded',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  filename?: string;

  @ApiProperty({
    description: 'The size of the file to be uploaded',
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({
    description: 'The text content of the message',
    required: false,
    type: String,
  })
  @IsOptional() // Either filename or text must be present
  @IsString()
  text?: string;
}
