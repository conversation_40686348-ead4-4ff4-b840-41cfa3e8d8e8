import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { OrganizationService } from '../services/organization.service';
import { AuthorizationService } from '@vidmob/vidmob-authorization-service-sdk';
import { OrganizationUserService as OrganizationUserServiceSdk } from '@vidmob/vidmob-organization-service-sdk';
import { WorkspaceService as organizationWorkspaceSDK } from '@vidmob/vidmob-organization-service-sdk';

const mockOrganizationUserServiceSdk = {
  listOrgsWithBrandGovernanceWorkspacesAsPromise: jest.fn(() =>
    Promise.resolve({
      result: mockResponse.result,
      pagination: { totalSize: mockResponse.result.length },
    }),
  ),
};

const mockOrganizationWorkspaceService = {
  findAllUserWorkspaceForAllOrgsAsPromise: jest
    .fn()
    .mockImplementation(() => Promise.resolve()),
};

const mockMapper = {
  map: jest.fn(),
  mapArray: jest.fn(),
};

describe('OrganizationService', () => {
  let service: OrganizationService;
  let mockAuthorizationService: Partial<AuthorizationService>;
  let mockConfigService: Partial<ConfigService>;

  beforeEach(async () => {
    mockAuthorizationService = {
      authorize: jest.fn().mockResolvedValue({
        data: { result: { userId: '123' } },
      }),
    };

    mockConfigService = {
      get: jest.fn().mockReturnValue('http://example.com'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationService,
        {
          provide: AuthorizationService,
          useValue: mockAuthorizationService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: OrganizationUserServiceSdk,
          useValue: mockOrganizationUserServiceSdk,
        },
        {
          provide: organizationWorkspaceSDK,
          useValue: mockOrganizationWorkspaceService,
        },
        {
          provide: 'automapper:nestjs:default',
          useValue: mockMapper,
        },
      ],
    }).compile();

    service = module.get<OrganizationService>(OrganizationService);
  });

  it('should fetch organizations by user id', async () => {
    const mockUserId = 12345;
    const mockSearch = 'Dyna Corp';
    const mockPaginationOptions = {
      offset: 0,
      perPage: 2,
    };

    await service.findAllByUserId(
      mockUserId,
      mockSearch,
      mockPaginationOptions,
    );

    expect(
      mockOrganizationUserServiceSdk.listOrgsWithBrandGovernanceWorkspacesAsPromise,
    ).toBeCalledWith(
      mockUserId,
      mockSearch,
      mockPaginationOptions.offset,
      mockPaginationOptions.perPage,
      undefined,
    );
  });
});

export const mockResponse = {
  status: 'OK',
  result: [
    {
      id: '0c598994-f0db-413a-8089-2c498bfe485e',
      name: 'Dyna Corp',
      associatedWorkspaces: '4',
      redirectWorkspaceId: '30262',
    },
    {
      id: 'bc5330e7-0dfa-4abc-8205-e23a3a733952',
      name: 'Tech Titans',
      associatedWorkspaces: '3',
      redirectWorkspaceId: '1223',
    },
  ],
  pagination: {
    offset: 0,
    perPage: 10,
    nextOffset: 0,
    totalSize: 2,
  },
};
