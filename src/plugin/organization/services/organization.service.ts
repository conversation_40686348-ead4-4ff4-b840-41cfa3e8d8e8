import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  OrganizationUserService,
  ReadOrganizationUserWorkspaceDto,
  WorkspaceService as organizationWorkspaceSDK,
} from '@vidmob/vidmob-organization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { AllOrganizationWorkspaceByUserDto } from '../dto/all-organization-workspace-by-user.dto';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { OrganizationDto } from '../dto/organization.dto';

@Injectable()
export class OrganizationService {
  baseUrlApi: string;

  constructor(
    private readonly organizationUserService: OrganizationUserService,
    private readonly organizationWorkspaceService: organizationWorkspaceSDK,
    private configService: ConfigService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.baseUrlApi = this.configService.get<string>('baseVidMobApiUrl', '');
  }

  async findUserWorkspacesForAllOrgs(
    userId: number,
    organizationId: string | undefined,
  ) {
    const { result } =
      await this.organizationWorkspaceService.findAllUserWorkspaceForAllOrgsAsPromise(
        userId,
        organizationId,
      );

    const organizationWorkspace = await this.aggregateWorkspacesByOrganization(
      result,
    );

    return this.classMapper.mapArray(
      organizationWorkspace,
      OrganizationDto,
      OrganizationDto,
    );
  }

  async aggregateWorkspacesByOrganization(
    responseData: AllOrganizationWorkspaceByUserDto[],
  ): Promise<OrganizationDto[]> {
    const organizationsMap = new Map();

    responseData.forEach((workspace) => {
      let organization = organizationsMap.get(workspace.organizationId);
      if (!organization) {
        organization = {
          id: workspace.organizationId,
          name: workspace.organizationName,
          workspaces: [],
        };
        organizationsMap.set(workspace.organizationId, organization);
      }

      organization.workspaces.push({
        id: workspace.id.toString(),
        name: workspace.name,
        logoUrl: workspace.logoUrl,
      });
    });

    return Array.from(organizationsMap.values());
  }

  async findAllByUserId(
    userId: number,
    search?: string,
    paginationOptions?: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadOrganizationUserWorkspaceDto>> {
    const response =
      await this.organizationUserService.listOrgsWithBrandGovernanceWorkspacesAsPromise(
        userId,
        search || '',
        paginationOptions?.offset,
        paginationOptions?.perPage,
        paginationOptions?.queryId,
      );

    const filteredResults = response.result.filter(
      (organization) => Number(organization.associatedWorkspaces) > 0,
    );

    const adjustedTotalSize =
      response.pagination?.totalSize -
      (response.result.length - filteredResults.length);

    return new PaginatedResultArray(filteredResults, adjustedTotalSize);
  }
}
