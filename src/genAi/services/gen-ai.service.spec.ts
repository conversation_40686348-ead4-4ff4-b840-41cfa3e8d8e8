import { Test, TestingModule } from '@nestjs/testing';
import { GenAiService } from './gen-ai.service';
import { ProjectService } from '@vidmob/vidmob-studio-service-sdk';
import { GenerateGenAiRequestDto } from '../dto/generate-gen-ai-request.dto';
import { GenerateGenAiResponseDto } from '../dto/generate-gen-ai-response.dto';

describe('GenAiService', () => {
  let service: GenAiService;
  let projectService: ProjectService;

  beforeEach(async () => {
    const mockProjectService = {
      projectControllerGenerateGenAiAsPromise: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GenAiService,
        {
          provide: ProjectService,
          useValue: mockProjectService,
        },
      ],
    }).compile();

    service = module.get<GenAiService>(GenAiService);
    projectService = module.get<ProjectService>(ProjectService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return the correct list of tools', () => {
    const expectedTools = [
      {
        name: 'Stable Diffusion',
        endpoint: 'v1/plugin/genai/stableDiffusion',
        responseFormat: 'base64',
      },
      {
        name: 'Dall-E',
        endpoint: 'v1/plugin/genai/dalle',
        responseFormat: 'base64',
      },
    ];

    expect(service.getTools()).toEqual(expectedTools);
  });

  it('should generate GenAI content', async () => {
    const generateGenAiDto: GenerateGenAiRequestDto = {
      outputVideoId: 123,
      workspaceId: 456,
      projectId: 789,
      userPrompt: 'Test prompt',
      outputOptions: {
        textModel: 'default-text-model',
        text: ['Sample text'],
        imageModel: 'default-image-model',
        image: ['Sample image'],
      },
    };

    const mockData: GenerateGenAiResponseDto = {
      text: {
        cta: [
          'Buy now and save 20%',
          'Subscribe for exclusive content',
          'Join now and get early access',
        ],
      },
      prompt: [
        'Generate an image depicting a woman aged 18-24 holding a smartphone in portrait orientation with a calm expression...',
      ],
      image: {
        _final: ['iVBOR...', 'iVBOR...', 'iVBOR...'],
        background: ['iVBOR...', 'iVBOR...', 'iVBOR...'],
        graphic: ['iVBOR...', 'iVBOR...'],
        textPrimary: ['textPrimary1', 'textPrimary2'],
        textSecondary: ['textSecondary1', 'textSecondary2'],
        cta: ['cta1', 'cta2'],
        logo: ['logo1', 'logo2'],
        compliance: ['compliance1', 'compliance2'],
        interactive: ['interactive1', 'interactive2'],
      },
    };

    jest
      .spyOn(projectService, 'projectControllerGenerateGenAiAsPromise')
      .mockResolvedValue(mockData);

    const authorizationHeader = 'Bearer token';
    const result = await service.generateGenAi(
      authorizationHeader,
      generateGenAiDto,
    );

    expect(result).toEqual(mockData);
    expect(
      projectService.projectControllerGenerateGenAiAsPromise,
    ).toHaveBeenCalledWith(authorizationHeader, generateGenAiDto);
  });
});
