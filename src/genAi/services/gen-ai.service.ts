import { Injectable } from '@nestjs/common';
import { ProjectService } from '@vidmob/vidmob-studio-service-sdk';
import {
  EditGenAiDtoV2,
  EditGenAiV2ResponseDto,
  GenerateGenAiDtoV2,
  GenerateGenAiResponseDto,
  GenerateGenAiV2ResponseDto,
  GenerateVideoGenAiDtoV2,
} from '../dto/generate-gen-ai-response.dto';
import { GenerateGenAiRequestDto } from '../dto/generate-gen-ai-request.dto';

@Injectable()
export class GenAiService {
  constructor(private readonly projectService: ProjectService) {}

  getTools() {
    return [
      {
        name: 'Stable Diffusion',
        endpoint: 'v1/plugin/genai/stableDiffusion',
        responseFormat: 'base64',
      },
      {
        name: 'Dall-E',
        endpoint: 'v1/plugin/genai/dalle',
        responseFormat: 'base64',
      },
    ];
  }

  async generateGenAi(
    authorization: string,
    generateGenAiDto: GenerateGenAiRequestDto,
  ): Promise<GenerateGenAiResponseDto> {
    return await this.projectService.projectControllerGenerateGenAiAsPromise(
      authorization,
      generateGenAiDto,
    );
  }

  // V2
  async generateGenAiV2(
    generateGenAiDto: GenerateGenAiDtoV2,
  ): Promise<GenerateGenAiV2ResponseDto> {
    return await this.projectService.projectControllerGenerateGenAiV2AsPromise(
      generateGenAiDto,
    );
  }

  async editGenAiV2(
    editGenAiDto: EditGenAiDtoV2,
  ): Promise<EditGenAiV2ResponseDto> {
    return await this.projectService.projectControllerEditGenAiV2AsPromise(
      editGenAiDto,
    );
  }

  async generateVideoGenAiV2(
    generateVideoGenAiDto: GenerateVideoGenAiDtoV2,
  ): Promise<{ id: string }> {
    return await this.projectService.projectControllerGenerateVideoGenAiV2AsPromise(
      generateVideoGenAiDto,
    );
  }

  async getGenAiV2VideoUrl(id: string): Promise<{ videoUrl: string }> {
    return await this.projectService.projectControllerGetGenAiV2VideoUrlAsPromise(
      id,
    );
  }

  async getGenAiV2VideoStatus(id: string): Promise<{ status: string }> {
    return await this.projectService.projectControllerGetGenAiV2VideoStatusAsPromise(
      id,
    );
  }
}
