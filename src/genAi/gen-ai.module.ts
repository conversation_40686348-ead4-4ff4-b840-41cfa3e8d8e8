import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { StableDiffusionController } from './stableDiffusion/controllers/stable-diffusion.controller';
import { StableDiffusionService } from './stableDiffusion/services/stable-diffusion.service';
import { DalleController } from './dall-e/controllers/dalle.controller';
import { DalleService } from './dall-e/services/dalle.service';
import { GenAiController } from './controllers/gen-ai.controller';
import { GenAiService } from './services/gen-ai.service';

@Module({
  imports: [HttpModule],
  providers: [GenAiService, StableDiffusionService, DalleService],
  controllers: [GenAiController, StableDiffusionController, DalleController],
})
export class GenAiModule {}
