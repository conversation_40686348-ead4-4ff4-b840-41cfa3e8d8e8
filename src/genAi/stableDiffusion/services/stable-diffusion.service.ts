import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as FormData from 'form-data';

@Injectable()
export class StableDiffusionService {
  stableDiffusionApiKey: string;
  stableDiffusionBaseUrl: string;

  constructor(configService: ConfigService) {
    this.stableDiffusionApiKey =
      process.env.STABLE_DIFFUSION_API_KEY ||
      configService.get<string>('stableDiffusionApiKey');

    this.stableDiffusionBaseUrl =
      'https://api.stability.ai/v2beta/stable-image/generate/core';
  }

  async generateImage(prompt: string): Promise<Buffer> {
    const formData = new FormData();
    formData.append('prompt', prompt);

    const response = await axios.post(this.stableDiffusionBaseUrl, formData, {
      headers: {
        ...formData.getHeaders(),
        Authorization: `Bearer ${this.stableDiffusion<PERSON><PERSON>Key}`,
        Accept: 'image/*',
      },
      responseType: 'arraybuffer',
    });

    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error(
        `Failed to generate image: ${response.status} ${response.statusText}`,
      );
    }
  }
}
