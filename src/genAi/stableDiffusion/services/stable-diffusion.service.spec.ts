import { Test, TestingModule } from '@nestjs/testing';
import { StableDiffusionService } from './stable-diffusion.service';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as FormData from 'form-data';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('StableDiffusionService', () => {
  let service: StableDiffusionService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StableDiffusionService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === 'stableDiffusionApiKey') return 'test-api-key';
            }),
          },
        },
      ],
    }).compile();

    service = module.get<StableDiffusionService>(StableDiffusionService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should generate an image and return buffer on successful API call', async () => {
    const prompt = 'a beautiful sunset';
    const expectedBuffer = Buffer.from('fake image data', 'utf-8');
    const mockResponse = {
      status: 200,
      data: expectedBuffer,
    };

    mockedAxios.post.mockResolvedValue(mockResponse);

    const result = await service.generateImage(prompt);

    expect(result).toEqual(expectedBuffer);
    expect(mockedAxios.post).toHaveBeenCalledWith(
      'https://api.stability.ai/v2beta/stable-image/generate/core',
      expect.any(FormData),
      {
        headers: {
          Accept: 'image/*',
          Authorization: 'Bearer test-api-key',
          'content-type': expect.stringContaining(
            'multipart/form-data; boundary=',
          ),
        },
        responseType: 'arraybuffer',
      },
    );
  });
});
