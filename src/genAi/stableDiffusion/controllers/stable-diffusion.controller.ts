import { <PERSON>, Post, Body, Res, HttpStatus } from '@nestjs/common';
import {
  ApiOperation,
  ApiTags,
  ApiResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { Response } from 'express';
import { StableDiffusionService } from '../services/stable-diffusion.service';
import { GenerateImagePromptDto } from '../../dto/generate-image-prompt.dto';

@ApiTags('Plugin Image GenAI')
@ApiSecurity('Bearer Token')
@Controller('plugin/genai/stableDiffusion')
export class StableDiffusionController {
  constructor(private stableDiffusionService: StableDiffusionService) {}

  @Post()
  @ApiOperation({ summary: 'Generate image from text using Stable Diffusion' })
  @ApiResponse({ status: 200, description: 'Image generated successfully.' })
  async generateImageFromText(
    @Body() generateImagePromptDto: GenerateImagePromptDto,
    @Res() res: Response,
  ) {
    try {
      const imageData = await this.stableDiffusionService.generateImage(
        generateImagePromptDto.prompt,
      );

      const base64Image = `data:image/webp;base64,${imageData.toString(
        'base64',
      )}`;
      res.setHeader('Content-Type', 'text/plain');
      res.send(base64Image);
    } catch (error) {
      res
        .status(error.response.status || HttpStatus.INTERNAL_SERVER_ERROR)
        .send({ message: error.message || 'Failed to generate image' });
    }
  }
}
