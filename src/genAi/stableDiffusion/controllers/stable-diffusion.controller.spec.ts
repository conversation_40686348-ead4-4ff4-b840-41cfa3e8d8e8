import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { StableDiffusionController } from './stable-diffusion.controller';
import { StableDiffusionService } from '../services/stable-diffusion.service';
import * as request from 'supertest';

describe('StableDiffusionController', () => {
  let app: INestApplication;
  let stableDiffusionService: StableDiffusionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StableDiffusionController],
      providers: [
        {
          provide: StableDiffusionService,
          useValue: {
            generateImage: jest
              .fn()
              .mockResolvedValue(Buffer.from('base64imageData', 'base64')),
          },
        },
      ],
    }).compile();

    app = module.createNestApplication();
    stableDiffusionService = module.get<StableDiffusionService>(
      StableDiffusionService,
    );
    await app.init();
  });

  it('should return an image as base64 wrapped in a Data URI when POST request is made', async () => {
    const prompt = 'a beautiful landscape';
    const base64Data = 'base64imageData';

    jest
      .spyOn(stableDiffusionService, 'generateImage')
      .mockResolvedValue(Buffer.from(base64Data, 'base64'));

    await request(app.getHttpServer())
      .post('/plugin/genai/stableDiffusion')
      .send({ prompt: prompt })
      .expect(HttpStatus.CREATED)
      .expect('Content-Type', /text\/plain/)
      .expect((res) => {
        expect(res.text).toMatch(/^data:image\/webp;base64,[A-Za-z0-9+/=]+$/);
      });
  });

  afterEach(async () => {
    await app.close();
  });
});
