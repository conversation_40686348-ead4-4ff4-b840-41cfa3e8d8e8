import type { GenerateGenAiDto as IGenerateGenAiRequestDto } from '@vidmob/vidmob-studio-service-sdk';
import {
  IsString,
  IsArray,
  ValidateNested,
  IsNumber,
  IsOptional,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';

export class ImageLayerDto {
  @IsString()
  background: string;

  @IsString()
  graphic: string;

  @IsString()
  textPrimary: string;

  @IsString()
  textSecondary: string;

  @IsString()
  cta: string;

  @IsString()
  logo: string;

  @IsString()
  compliance: string;

  @IsString()
  interactive: string;
}

export class OutputOptionsDto {
  @IsString()
  textModel: string;

  @IsArray()
  @IsString({ each: true })
  text: string[];

  @IsString()
  imageModel: string;

  @IsArray()
  @IsString({ each: true })
  image: string[];
}

export class GenerateGenAiRequestDto implements IGenerateGenAiRequestDto {
  @IsNumber()
  outputVideoId: number;

  @IsNumber()
  workspaceId: number;

  @IsNumber()
  projectId: number;

  @IsOptional()
  @IsString()
  userPrompt?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  inputOptions?: string[];

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => OutputOptionsDto)
  outputOptions?: OutputOptionsDto;
}
