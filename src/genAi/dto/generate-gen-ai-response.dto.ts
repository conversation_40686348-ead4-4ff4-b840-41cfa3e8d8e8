import type { GenerateGenAiResponseDto as IGenerateGenAiResponseDto } from '@vidmob/vidmob-studio-service-sdk';
import {
  IsString,
  IsArray,
  ValidateNested,
  IsNumber,
  IsObject,
  IsOptional,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';

class TextDto {
  @IsArray()
  @IsString({ each: true })
  cta: string[];
}

class ImageDto {
  @IsArray()
  @IsString({ each: true })
  _final: string[];

  @IsArray()
  @IsString({ each: true })
  background: string[];

  @IsArray()
  @IsString({ each: true })
  graphic: string[];

  @IsArray()
  @IsString({ each: true })
  textPrimary: string[];

  @IsArray()
  @IsString({ each: true })
  textSecondary: string[];

  @IsArray()
  @IsString({ each: true })
  cta: string[];

  @IsArray()
  @IsString({ each: true })
  logo: string[];

  @IsArray()
  @IsString({ each: true })
  compliance: string[];

  @IsArray()
  @IsString({ each: true })
  interactive: string[];
}

export class GenerateGenAiResponseDto implements IGenerateGenAiResponseDto {
  @ValidateNested()
  @Type(() => TextDto)
  text: TextDto;

  @ValidateNested()
  @Type(() => ImageDto)
  image: ImageDto;

  @IsArray()
  @IsString({ each: true })
  prompt: string[];
}

export class GenerateGenAiBriefDto {
  @IsString()
  topic: string;

  @IsString()
  value: string;
}

export class GenerateGenAiVidmobExtraDataDto {
  @IsString()
  channel: string;

  @IsArray()
  @IsString({ each: true })
  insights: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GenerateGenAiBriefDto)
  brief: GenerateGenAiBriefDto[];
}

export enum GenerateGenAiImageModelEnum {
  TITAN = 'TITAN',
  STABLE_DIFFUSION = 'STABLE_DIFFUSION',
  FIREFLY = 'FIREFLY',
}

export enum TaskTypeEnum {
  INPAINTING = 'INPAINTING',
  OUTPAINTING = 'OUTPAINTING',
}

export class GenerateGenAiDtoV2 {
  @IsString()
  userPrompt: string;

  @IsNumber()
  numberOfAssets: number;

  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerateGenAiVidmobExtraDataDto)
  vidmobExtraData?: GenerateGenAiVidmobExtraDataDto;

  @IsNumber()
  canvasWidth: number;

  @IsNumber()
  canvasHeight: number;

  @IsEnum(GenerateGenAiImageModelEnum)
  imageModel: GenerateGenAiImageModelEnum;

  @IsNumber()
  creativityBalance: number;
}

export class GenerateVideoGenAiDtoV2 {
  @IsString()
  userPrompt: string;

  @IsNumber()
  durationSeconds: number;

  @IsNumber()
  fps: number;

  @IsString()
  dimension: string;

  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerateGenAiVidmobExtraDataDto)
  vidmobExtraData?: GenerateGenAiVidmobExtraDataDto;
}

export class EditGenAiV2ResponseDto {
  @IsOptional()
  @IsString()
  image?: string;
}

export class EditGenAiDtoV2 {
  @IsString()
  userPrompt: string;

  @IsNumber()
  canvasWidth: number;

  @IsNumber()
  canvasHeight: number;

  @IsEnum(GenerateGenAiImageModelEnum)
  imageModel: GenerateGenAiImageModelEnum;

  @IsNumber()
  creativityBalance: number;

  @IsString()
  image: string;

  @IsString()
  maskImage: string;

  @IsEnum(TaskTypeEnum)
  taskType: TaskTypeEnum;
}

export class GenerateGenAiV2ResponseDto {
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  messages?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  ctas?: string[];

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  primaryColor?: string;

  @IsOptional()
  @IsString()
  secondaryColor?: string;
}
