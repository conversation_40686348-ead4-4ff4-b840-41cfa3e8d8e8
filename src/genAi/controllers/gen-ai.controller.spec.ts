import { Test, TestingModule } from '@nestjs/testing';
import { GenAiController } from './gen-ai.controller';
import { GenAiService } from '../services/gen-ai.service';
import { GenerateGenAiRequestDto } from '../dto/generate-gen-ai-request.dto';
import { GenerateGenAiResponseDto } from '../dto/generate-gen-ai-response.dto';

describe('GenAiController', () => {
  let controller: GenAiController;
  let service: GenAiService;

  const mockData: GenerateGenAiResponseDto = {
    text: {
      cta: [
        'Buy now and save 20%',
        'Subscribe for exclusive content',
        'Join now and get early access',
      ],
    },
    prompt: [
      'Generate an image depicting a woman aged 18-24 holding a smartphone in portrait orientation with a calm expression...',
    ],
    image: {
      _final: ['iVBOR...', 'iVBOR...', 'iVBOR...'],
      background: ['iVBOR...', 'iVBOR...', 'iVBOR...'],
      graphic: ['iVBOR...', 'iVBOR...'],
      textPrimary: ['textPrimary1', 'textPrimary2'],
      textSecondary: ['textSecondary1', 'textSecondary2'],
      cta: ['cta1', 'cta2'],
      logo: ['logo1', 'logo2'],
      compliance: ['compliance1', 'compliance2'],
      interactive: ['interactive1', 'interactive2'],
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GenAiController],
      providers: [
        {
          provide: GenAiService,
          useValue: {
            getTools: jest.fn().mockReturnValue({
              status: 'OK',
              result: [
                {
                  name: 'Stable Diffusion',
                  endpoint: 'v1/plugin/genai/stableDiffusion',
                  responseFormat: 'base64',
                },
                {
                  name: 'Dall-E',
                  endpoint: 'v1/plugin/genai/dalle',
                  responseFormat: 'base64',
                },
              ],
            }),
            generateGenAi: jest.fn().mockResolvedValue(mockData),
          },
        },
      ],
    }).compile();

    controller = module.get<GenAiController>(GenAiController);
    service = module.get<GenAiService>(GenAiService);
  });

  it('should return an array of tools', async () => {
    expect(controller.getTools()).toEqual({
      status: 'OK',
      result: [
        {
          name: 'Stable Diffusion',
          endpoint: 'v1/plugin/genai/stableDiffusion',
          responseFormat: 'base64',
        },
        {
          name: 'Dall-E',
          endpoint: 'v1/plugin/genai/dalle',
          responseFormat: 'base64',
        },
      ],
    });
    expect(service.getTools).toHaveBeenCalled();
  });

  it('should generate GenAI response', async () => {
    const generateGenAiDto: GenerateGenAiRequestDto = {
      outputVideoId: 123,
      workspaceId: 456,
      projectId: 789,
      userPrompt: mockData.prompt[0],
      outputOptions: {
        textModel: 'default-text-model',
        text: mockData.text.cta,
        imageModel: 'default-image-model',
        image: [
          ...mockData.image.background,
          ...mockData.image.graphic,
          ...mockData.image.textPrimary,
          ...mockData.image.textSecondary,
          ...mockData.image.cta,
          ...mockData.image.logo,
          ...mockData.image.compliance,
          ...mockData.image.interactive,
        ],
      },
    };

    const mockReq = { headers: { authorization: 'Bearer token' } };
    expect(await controller.generateGenAi(generateGenAiDto, mockReq)).toEqual(
      mockData,
    );
    expect(service.generateGenAi).toHaveBeenCalledWith(
      'Bearer token',
      generateGenAiDto,
    );
  });
});
