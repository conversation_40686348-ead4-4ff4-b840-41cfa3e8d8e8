import { Controller, Get, Post, Body, Param, Request } from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBody,
  ApiParam,
  ApiSecurity,
} from '@nestjs/swagger';
import { GenAiService } from '../services/gen-ai.service';
import {
  EditGenAiDtoV2,
  EditGenAiV2ResponseDto,
  GenerateGenAiDtoV2,
  GenerateGenAiResponseDto,
  GenerateGenAiV2ResponseDto,
  GenerateVideoGenAiDtoV2,
} from '../dto/generate-gen-ai-response.dto';
import { GenerateGenAiRequestDto } from '../dto/generate-gen-ai-request.dto';

@ApiTags('Plugin Project GenAI')
@ApiSecurity('Bearer Token')
@Controller('plugin/genai')
export class GenAiController {
  constructor(private readonly genAiService: GenAiService) {}

  @ApiTags('Plugin Image GenAI')
  @Get('tools')
  @ApiOperation({ summary: 'Get available Image GenAI models' })
  @ApiResponse({ status: 200, description: 'Tools fetched successfully.' })
  getTools() {
    return this.genAiService.getTools();
  }

  @Post('/generate')
  @ApiBody({
    type: GenerateGenAiRequestDto,
  })
  @ApiResponse({
    status: 200,
    type: GenerateGenAiResponseDto,
    description: 'GenAI plugin response',
  })
  async generateGenAi(
    @Body() generateGenAiDto: GenerateGenAiRequestDto,
    @Request() req: any,
  ): Promise<GenerateGenAiResponseDto> {
    const authorization = req.headers.authorization;
    return await this.genAiService.generateGenAi(
      authorization,
      generateGenAiDto,
    );
  }

  @Post('/generate/v2')
  @ApiBody({
    type: GenerateGenAiDtoV2,
  })
  @ApiResponse({
    status: 200,
    type: GenerateGenAiV2ResponseDto,
    description: 'GenAI plugin response',
  })
  async generateGenAiV2(
    @Body() body: GenerateGenAiDtoV2,
  ): Promise<GenerateGenAiV2ResponseDto> {
    return await this.genAiService.generateGenAiV2(body);
  }

  @Post('/edit/v2')
  @ApiBody({
    type: EditGenAiDtoV2,
  })
  @ApiResponse({
    status: 200,
    type: EditGenAiV2ResponseDto,
    description: 'GenAI plugin response',
  })
  async editGenAiV2(
    @Body() body: EditGenAiDtoV2,
  ): Promise<EditGenAiV2ResponseDto> {
    return await this.genAiService.editGenAiV2(body);
  }

  @Post('/generate/video/V2')
  @ApiBody({
    type: GenerateVideoGenAiDtoV2,
  })
  @ApiResponse({
    status: 200,
    description: 'GenAI plugin response',
  })
  async generateVideoGenAiV2(
    @Body() body: GenerateVideoGenAiDtoV2,
  ): Promise<{ id: string }> {
    return await this.genAiService.generateVideoGenAiV2(body);
  }

  @Get('/videoUrl/V2/:videoId')
  @ApiParam({
    name: 'videoId',
    type: 'string',
    description: 'ID of the video',
  })
  @ApiResponse({
    status: 200,
    description: 'GenAI plugin response',
  })
  async getGenAiV2VideoUrl(
    @Param('videoId') videoId: string,
  ): Promise<{ videoUrl: string }> {
    return await this.genAiService.getGenAiV2VideoUrl(videoId);
  }

  @Get('/videoStatus/V2/:videoId')
  @ApiParam({
    name: 'videoId',
    type: 'string',
    description: 'ID of the video',
  })
  @ApiResponse({
    status: 200,
    description: 'GenAI plugin response',
  })
  async getGenAiV2VideoStatus(
    @Param('videoId') videoId: string,
  ): Promise<{ status: string }> {
    return await this.genAiService.getGenAiV2VideoStatus(videoId);
  }
}
