import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus, INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { <PERSON><PERSON>Controller } from './dalle.controller';
import { DalleService } from '../services/dalle.service';

describe('DalleController', () => {
  let app: INestApplication;
  let dalleService: DalleService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [DalleController],
      providers: [
        {
          provide: DalleService,
          useValue: {
            generateImage: jest
              .fn()
              .mockResolvedValue(
                Buffer.from('realisticImageData').toString('base64'),
              ),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    dalleService = moduleFixture.get<DalleService>(DalleService);
    await app.init();
  });

  it('should generate an image and return it as base64 wrapped in a data URI when POST request is made', async () => {
    const prompt = 'a beautiful sunset';
    const base64EncodedData =
      Buffer.from('realisticImageData').toString('base64');
    const expectedDataUri = `data:image/png;base64,${base64EncodedData}`;

    await request(app.getHttpServer())
      .post('/plugin/genai/dalle')
      .send({ prompt })
      .expect('Content-Type', /text\/plain/)
      .expect(HttpStatus.CREATED)
      .expect(expectedDataUri)
      .then(() => {
        expect(dalleService.generateImage).toHaveBeenCalledWith(prompt);
      });
  });

  afterEach(async () => {
    await app.close();
  });
});
