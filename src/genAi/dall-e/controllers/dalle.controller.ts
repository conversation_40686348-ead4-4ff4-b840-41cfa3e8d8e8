import { <PERSON>, Post, Body, Res } from '@nestjs/common';
import { Response } from 'express';
import { DalleService } from '../services/dalle.service';
import {
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { GenerateImagePromptDto } from './../../dto/generate-image-prompt.dto';

@ApiTags('Plugin Image GenAI')
@ApiSecurity('Bearer Token')
@Controller('plugin/genai/dalle')
export class DalleController {
  constructor(private readonly dalleService: DalleService) {}

  @Post()
  @ApiOperation({ summary: 'Generate image from text using DALL-E' })
  @ApiResponse({
    status: 201,
    description:
      'Returns a Base64 encoded image as a response body wrapped in a Data URI scheme, suitable for direct use in HTML or CSS.',
  })
  async generateFromText(
    @Body() generateImagePromptDto: GenerateImagePromptDto,
    @Res() res: Response,
  ) {
    try {
      const base64Image = await this.dalleService.generateImage(
        generateImagePromptDto.prompt,
      );
      const imageDataUri = `data:image/png;base64,${base64Image}`;
      res.setHeader('Content-Type', 'text/plain');
      res.status(201).send(imageDataUri);
    } catch (error) {
      res
        .status(error.status || 500)
        .json({ message: error.message || 'Internal Server Error' });
    }
  }
}
