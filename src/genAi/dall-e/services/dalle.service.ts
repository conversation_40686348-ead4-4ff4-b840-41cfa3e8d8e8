import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class DalleService {
  private dalleApiKey: string;
  private dalleBaseURL: string;

  constructor(configService: ConfigService) {
    this.dalleApiKey =
      process.env.DALLE_API_KEY || configService.get<string>('dalle<PERSON>pi<PERSON>ey');

    this.dalleBaseURL = 'https://api.openai.com/v1/images/generations';
  }

  async generateImage(prompt: string): Promise<string> {
    const headers = {
      Authorization: `Bearer ${this.dalleApiKey}`,
      'Content-Type': 'application/json',
    };

    const data = {
      model: 'dall-e-3',
      prompt: prompt,
      n: 1,
      size: '1024x1024',
      response_format: 'b64_json',
    };

    try {
      const response = await axios.post(this.dalleBaseURL, data, { headers });

      const imageData = response.data;

      return imageData.data[0].b64_json;
    } catch (error) {
      throw new HttpException(
        `DALL-E API Error: ${
          error.response?.data?.error?.message || 'No error message provided'
        }`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
