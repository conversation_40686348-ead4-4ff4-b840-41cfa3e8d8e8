import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { DalleService } from './dalle.service';
import axios from 'axios';
import { HttpException, HttpStatus } from '@nestjs/common';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('DalleService', () => {
  let service: DalleService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DalleService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === 'dalleApiKey') return 'test-dalle-api-key';
            }),
          },
        },
      ],
    }).compile();

    service = module.get<DalleService>(DalleService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should successfully call the DALL-E API and return image data', async () => {
    const prompt = 'beautiful landscape';
    const mockResponse = {
      data: {
        data: [
          {
            b64_json: 'base64imageData',
          },
        ],
      },
    };

    mockedAxios.post.mockResolvedValue(mockResponse);

    const result = await service.generateImage(prompt);

    expect(result).toEqual('base64imageData');
    expect(mockedAxios.post).toHaveBeenCalledWith(
      'https://api.openai.com/v1/images/generations',
      {
        model: 'dall-e-3',
        prompt,
        n: 1,
        size: '1024x1024',
        response_format: 'b64_json',
      },
      {
        headers: {
          Authorization: 'Bearer test-dalle-api-key',
          'Content-Type': 'application/json',
        },
      },
    );
  });

  it('should throw an HttpException if the DALL-E API call fails', async () => {
    const prompt = 'beautiful landscape';
    const error = {
      response: {
        status: 500,
        data: {
          error: {
            message: 'Internal server error',
          },
        },
      },
    };

    mockedAxios.post.mockRejectedValue(error);

    await expect(service.generateImage(prompt)).rejects.toThrow(HttpException);
    await expect(service.generateImage(prompt)).rejects.toThrow(
      'DALL-E API Error: Internal server error',
    );
  });
});
