export const PLUGIN_MEDIA = 'PLUGIN_MEDIA';
export const DEFAULT_PER_PAGE_FOR_DUP_NAMES = 50;
export const DEFAULT_OFFSET_FOR_DUP_NAMES = 0;
export const DEFAULT_PER_PAGE_MEDIA_DETAILS = 1;
export const DEFAULT_OFFSET_MEDIA_DETAILS = 0;
export const DEFATUL_SORT_ORDER_FOR_DUP_NAMES = 'DESC';
export const DEFATUL_SORT_BY_FOR_DUP_NAMES = 'name';
export const MAX_SULFIX = 1;
export const SUBMITTED_STATUS = 'SUBMITTED';
export const DEFAULT_BRIEF_PER_PAGE = '50';
export const DEFAULT_MESSAGE_PER_PAGE = 20;
export const DEFAULT_MESSAGE_OFFSET = 0;
export const VIDEO_TYPE = 'VIDEO';

export const platformIdentifierMapping = {
  AMAZON: 'AMAZON',
  FACEBOOK: 'FACEBOOK',
  INSTAGRAM: 'FACEBOOK',
  GOOGLE: 'ADWORDS',
  ADWORDS: 'ADWORDS',
  LINKEDIN: 'LINKEDIN',
  PINTEREST: 'PINTEREST',
  REDDIT: 'REDDIT',
  SNAPCHAT: 'SNAPCHAT',
  TIKTOK: 'TIKTOK',
  TINDER: 'TINDER',
  TWITTER: 'TWITTER',
  YOUTUBE: 'DV360',
  DV360: 'DV360',
  ALL_PLATFORMS: 'ALL_PLATFORMS',
};

export const ITERATION_MEDIA_TYPE = 'ITERATION';
export const PARTNER_ASSET_MEDIA_TYPE = 'PARTNER_ASSET';
export const DEFAULT_GET_SCORECARDS_PAGINATION_PARAMS = {
  perPage: 1000,
  offset: 0,
};
