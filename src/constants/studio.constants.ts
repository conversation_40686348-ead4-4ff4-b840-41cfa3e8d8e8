export const REVIEW_STATUS_ACCEPTED = 'ACCEPTED';

export const errorText = `
  identifier
  message
  system
  type
`;

export const eventsText = `
  FAVORITED
  LINKED_TO_PROJECT
  UNLINKED_FROM_PROJECT
  VIEW
  VIEW_DETAILS
`;

// recommendationInsightResultText is the same as fullInsightResultText without lastUpdatedTime
export const recommendationInsightResultText = `
    adAccountIds
    audiences
    campaigns
    campaignsInfo {
        id
        name
    }
    createdBy {
        id
        name
    }
    createdTime
    detail
    detailedInsightBuckets
    dimensionGroups
    dimensionValues
    endDate
    events {
        ${eventsText}
    }
    formats
    highLevelInsightBuckets
    industryId
    insightCreativeExamples {
        examplePlatformMediaIds
        exampleType
        tagType
        tagValue
    }
    insightCreativePlatformMediaIds
    insightId
    insightReportUrl
    kpis
    kpisInfo {
        id
        name
    }
    mediaTypes
    normativeMetaData {
        accountCounts
        elementAdCount
        elementAdVideoCount
        elementImpressions
        elementKpiValue
        elementPercentLift
        elementTagType
        elementValue
        kpi {
            id
            name
        }
        level
        levelAdCount
        levelAdVideoCount
        levelImpressions
        levelKpiValue
        levelName
        time
    }
    objectives {
        id
        name
    }
    placements {
        id
        name
    }
    platform
    publishDate
    recommendation
    source
    startDate
    status
    title
    type
`;

export const fullInsightResultText = `
    adAccountIds
    audiences
    campaigns
    campaignsInfo {
        id
        name
    }
    createdBy {
        id
        name
    }
    createdTime
    detail
    detailedInsightBuckets
    dimensionGroups
    dimensionValues
    endDate
    events {
        ${eventsText}
    }
    formats
    highLevelInsightBuckets
    industryId
    insightCreativeExamples {
        examplePlatformMediaIds
        exampleType
        tagType
        tagValue
    }
    insightCreativePlatformMediaIds
    insightId
    insightReportUrl
    kpis
    kpisInfo {
        id
        name
    }
    lastUpdatedTime
    mediaTypes
    normativeMetaData {
        accountCounts
        elementAdCount
        elementAdVideoCount
        elementImpressions
        elementKpiValue
        elementPercentLift
        elementTagType
        elementValue
        kpi {
            id
            name
        }
        level
        levelAdCount
        levelAdVideoCount
        levelImpressions
        levelKpiValue
        levelName
        time
    }
    objectives {
        id
        name
    }
    placements {
        id
        name
    }
    platform
    publishDate
    recommendation
    source
    savedAnalyticsReportId
    startDate
    status
    title
    type
`;

export const CHAT_ATTACHMENT_MEDIA_TYPE = 'CHAT_ATTACHMENT';
export const CHAT_ATTACHMENT_MEDIA_SOURCE = 7;
