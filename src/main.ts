// Must go at top of file before any other imports
import '@vidmob/vidmob-nestjs-common/dist/tracing';

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {
  DEFAULT_PORT,
  ErrorResponse,
  SuccessResponse,
  VidmobCommonModule,
} from '@vidmob/vidmob-nestjs-common';
import {
  CONFIG_CORS_ORIGIN,
  CONFIG_ENV_LOCAL,
  CONFIG_ENV_SDK,
  CONFIG_PORT,
} from './constants/configuration.constants';

import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  API_DESCRIPTION,
  API_TITLE,
  API_VERSION,
} from './constants/api.constants';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as bodyParser from 'body-parser';
import { Request, Response, NextFunction } from 'express';

const SWAGGER_URI = 'docs';
const CORS_ALLOW_ORIGIN_ALL = '*';

const ROUTS_WITH_INCREASE_LIMIT = [
  '/v1/plugin/genai/edit/V2',
  '/v1/media/aperture',
];

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  VidmobCommonModule.setupLogger(app); // Setup logger (uses Pino Logger)
  app.enableShutdownHooks();

  app.use((req: Request, res: Response, next: NextFunction) => {
    // Increase the payload size limit
    // We need to increase the limit for some routes like /v1/plugin/genai/edit/V2
    if (ROUTS_WITH_INCREASE_LIMIT.includes(req.path)) {
      bodyParser.json({ limit: '10mb' })(req, res, (err) => {
        if (err) {
          return res.status(413).json({ error: 'Payload too large' });
        }
        next();
      });
    } else {
      next();
    }
  });

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
    }),
  );

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  const configService = app.get(ConfigService);
  const environment = configService.get('NODE_ENV');

  if (environment === CONFIG_ENV_SDK || environment === CONFIG_ENV_LOCAL) {
    const port = configService.get<number>(CONFIG_PORT, DEFAULT_PORT);
    const config = new DocumentBuilder()
      .setTitle(API_TITLE)
      .setDescription(API_DESCRIPTION)
      .setVersion(API_VERSION)
      .addServer('https://api-bff-dev.vidmob.com', 'Dev')
      .addServer('https://api-bff-stage.vidmob.com', 'Stage')
      .addServer('https://api-bff.vidmob.com', 'Prod')
      .addServer(`http://localhost:${port}`, 'Local')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'Bearer Token',
          name: 'Authorization',
          description: 'Enter your Bearer Token or API Key (without "Bearer " prefix)',
          in: 'header',
        },
        'Bearer Token', // This is the key that will be used to reference this auth method
      )
      .build();
    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [SuccessResponse, ErrorResponse],
    });
    SwaggerModule.setup(SWAGGER_URI, app, document);
  }

  const port = configService.get<number>(CONFIG_PORT, DEFAULT_PORT);
  console.log('Listening on port: ' + port);
  const allowList = configService.get<string[]>(CONFIG_CORS_ORIGIN, [
    CORS_ALLOW_ORIGIN_ALL,
  ]);

  /*
   * This is a custom CORS implementation that allows us to use a whitelist
   */
  app.enableCors((req: any, callback: any) => {
    let corsOptions;
    const originHeader = req.header('Origin');

    if (allowList.indexOf(CORS_ALLOW_ORIGIN_ALL) !== -1) {
      return callback(null, {
        origin: CORS_ALLOW_ORIGIN_ALL,
        exposedHeaders: ['Location'],
      });
    }

    if (allowList.indexOf(originHeader) !== -1) {
      corsOptions = { origin: originHeader, exposedHeaders: ['Location'] };
    } else {
      corsOptions = { origin: null };
    }
    callback(null, corsOptions);
  });

  await app.listen(port);
}
bootstrap();
