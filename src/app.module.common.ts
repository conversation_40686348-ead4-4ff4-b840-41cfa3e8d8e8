import {
  ThrottlerGenerateKeyFunction,
  ThrottlerModuleOptions,
} from '@nestjs/throttler/dist/throttler-module-options.interface';
import { ExecutionContext } from '@nestjs/common/interfaces';
import { ConfigService } from '@nestjs/config';
import { ThrottlerStorageRedisService } from 'nestjs-throttler-storage-redis';
import { CacheConfig } from './api.module.types';
import { ThrottlerStorage } from '@nestjs/throttler/dist/throttler-storage.interface';
import { Cluster } from 'ioredis';

/**
 * Generates Api Throttle Key for any endpoint using the throttle guard.
 * The endpoint will need to be authorized using the @ApiKeyScopes decorator
 * @param context
 */
const generateApiThrottleKey: ThrottlerGenerateKeyFunction = (
  context: ExecutionContext,
) => {
  const request: any = context.switchToHttp().getRequest();
  return request?.organizationId;
};

/**
 * Get throttle storage for env. Uses in memory storage if there is no
 * Redis configuration stated
 *
 * @param configService
 */
const getThrottleStorage: (
  configService: ConfigService,
) => ThrottlerStorage | undefined = (configService) => {
  const cacheConfig: CacheConfig = configService.get<CacheConfig>('cache');
  if (!cacheConfig) {
    return undefined;
  }
  const redisCluster: Cluster = new Cluster(
    [
      {
        host: cacheConfig.redisHost,
        port: 6379,
      },
    ],
    {
      dnsLookup: (address, callback) => callback(null, address),
      redisOptions: {
        tls: cacheConfig.tls ? {} : undefined,
      },
    },
  );
  return new ThrottlerStorageRedisService(redisCluster);
};

/**
 * Builds ThrottlerModule options for each environment. If
 * the environment is in local, it skips creating the cache storage
 * and uses in-memory storage by default
 *
 * @param configService
 */
export const createApiThrottlerModuleOptions: (
  configService: ConfigService,
) => Promise<ThrottlerModuleOptions> = async (configService) => {
  return {
    generateKey: generateApiThrottleKey,
    storage: getThrottleStorage(configService),
    throttlers: [
      {
        ttl: configService.get<number>('THROTTLE_TTL_MILLISECONDS'),
        limit: configService.get<number>('THROTTLE_LIMIT'),
      },
    ],
  };
};
