import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Request,
} from '@nestjs/common';
import { UploadService } from '../services/upload.service';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { UseInterceptors, UploadedFile } from '@nestjs/common';
import {
  ApiHeader,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiSecurity,
} from '@nestjs/swagger';

@ApiTags('Upload Files')
@ApiSecurity('Bearer Token')
@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post(':mediaId')
  @ApiOperation({ summary: 'Create a file upload session' })
  @ApiResponse({
    status: 200,
    description: 'File upload session created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Error response',
  })
  @ApiHeader({
    name: 'upload-length',
    description: 'Total length of the file being uploaded',
    required: true,
  })
  @ApiHeader({
    name: 'upload-metadata',
    description:
      'Metadata related to the file being uploaded, encoded as a base64 string',
    required: true,
  })
  @ApiParam({
    name: 'mediaId',
    type: 'string',
    required: true,
    description: 'The ID of the media',
  })
  async createFileUpload(
    @Param('mediaId') mediaId: string,
    @Headers('upload-length') uploadLength: string,
    @Headers('upload-metadata') uploadMetadata: string,
    @Res() res: Response,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    const response = await this.uploadService.createFileUpload(
      mediaId,
      uploadLength,
      uploadMetadata,
      authorization,
    );

    Object.keys(response.headers).forEach((header) => {
      res.setHeader(header, response.headers[header]);
    });

    res.status(response.status).send(response.data);
  }

  @Patch(':location')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a chunk of a file' })
  @ApiResponse({
    status: 200,
    description: 'Chunk uploaded successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Error response',
  })
  @ApiHeader({
    name: 'upload-offset',
    description: 'The byte offset of the chunk being uploaded',
    required: true,
  })
  @ApiParam({
    name: 'location',
    type: 'string',
    required: true,
    description: 'The location identifier for the chunk upload',
  })
  async uploadChunk(
    @Param('location') location: string,
    @UploadedFile() file: Express.Multer.File,
    @Headers('upload-offset') uploadOffset: string,
    @Res() res: Response,
    @Request() req: any,
  ) {
    const authorization = req.headers.authorization;
    const response = await this.uploadService.uploadChunk(
      location,
      file,
      uploadOffset,
      authorization,
    );

    Object.keys(response.headers).forEach((header) => {
      res.setHeader(header, response.headers[header]);
    });

    res.status(response.status).send(response.data);
  }
}
