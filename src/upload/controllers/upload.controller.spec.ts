import { Test, TestingModule } from '@nestjs/testing';
import { UploadController } from './upload.controller';
import { UploadService } from '../services/upload.service';
import { Response } from 'express';

describe('UploadController', () => {
  let controller: UploadController;
  let service: UploadService;
  let mockResponse: Partial<Response>;

  beforeEach(async () => {
    mockResponse = {
      setHeader: jest.fn(),
      status: jest.fn().mockReturnThis(), // Chainable
      send: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UploadController],
      providers: [
        {
          provide: UploadService,
          useValue: {
            createFileUpload: jest.fn(),
            uploadChunk: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UploadController>(UploadController);
    service = module.get<UploadService>(UploadService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createFileUpload', () => {
    it('should create a file upload session', async () => {
      const mediaId = 'testMediaId';
      const uploadLength = '1000';
      const uploadMetadata = 'testMetadata';
      const authorization = 'Bearer testToken';
      const mockServiceResponse = {
        headers: { location: 'test/location' },
        status: 200,
        data: 'Session Created',
      };

      jest
        .spyOn(service, 'createFileUpload')
        .mockResolvedValue(mockServiceResponse);

      const mockReq = { headers: { authorization } };

      await controller.createFileUpload(
        mediaId,
        uploadLength,
        uploadMetadata,
        mockResponse as Response,
        mockReq as any,
      );

      expect(service.createFileUpload).toHaveBeenCalledWith(
        mediaId,
        uploadLength,
        uploadMetadata,
        authorization,
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'location',
        'test/location',
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalledWith('Session Created');
    });
  });

  describe('uploadChunk', () => {
    it('should upload a chunk of a file', async () => {
      const location = 'testLocation';
      const file = {
        originalname: 'testFile.txt',
        buffer: Buffer.from('test'),
      } as Express.Multer.File;
      const uploadOffset = '0';
      const authorization = 'Bearer testToken';
      const mockServiceResponse = {
        headers: { 'upload-offset': '1000' },
        status: 200,
        data: 'Chunk Uploaded',
      };

      jest.spyOn(service, 'uploadChunk').mockResolvedValue(mockServiceResponse);

      const mockReq = { headers: { authorization } };

      await controller.uploadChunk(
        location,
        file,
        uploadOffset,
        mockResponse as Response,
        mockReq as any,
      );

      expect(service.uploadChunk).toHaveBeenCalledWith(
        location,
        file,
        uploadOffset,
        authorization,
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'upload-offset',
        '1000',
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalledWith('Chunk Uploaded');
    });
  });
});
