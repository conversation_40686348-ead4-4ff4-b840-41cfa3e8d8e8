import { Test, TestingModule } from '@nestjs/testing';
import { UploadService } from './upload.service';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('MediaService', () => {
  let service: UploadService;
  let mockConfigService: Partial<ConfigService>;

  beforeEach(async () => {
    mockedAxios.post.mockReset();
    mockedAxios.patch.mockReset();

    mockConfigService = {
      get: jest.fn().mockReturnValue('http://example.com'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UploadService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<UploadService>(UploadService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createFileUpload', () => {
    it('should successfully create a file upload session', async () => {
      const mockResponse = { data: {}, status: 200, headers: {} };
      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await service.createFileUpload(
        'mediaId',
        '1000',
        'metadata',
        'Bearer token',
      );

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://example.com/api/v1/tus/mediaId',
        {},
        {
          headers: {
            'Upload-Length': '1000',
            'Upload-Metadata': 'metadata',
            Authorization: 'Bearer token',
          },
        },
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle an error response', async () => {
      const mockError = {
        response: { data: 'Error', status: 400, headers: {} },
      };
      mockedAxios.post.mockRejectedValue(mockError);

      const result = await service.createFileUpload(
        'mediaId',
        '1000',
        'metadata',
        'Bearer token',
      );

      expect(result).toEqual({
        data: mockError.response.data,
        status: mockError.response.status,
        headers: mockError.response.headers,
      });
    });
  });

  describe('uploadChunk', () => {
    it('should successfully upload a chunk', async () => {
      const mockFile = {
        buffer: Buffer.from('file data'),
        originalname: 'test.txt',
      };
      const mockResponse = { data: {}, status: 200, headers: {} };
      mockedAxios.patch.mockResolvedValue(mockResponse);

      const result = await service.uploadChunk(
        'http://example.com/upload/location',
        mockFile as Express.Multer.File,
        '0',
        'Bearer token',
      );

      expect(mockedAxios.patch).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should handle an error response', async () => {
      const mockFile = {
        buffer: Buffer.from('file data'),
        originalname: 'test.txt',
      };
      const mockError = {
        response: { data: 'Error', status: 400, headers: {} },
      };
      mockedAxios.patch.mockRejectedValue(mockError);

      const result = await service.uploadChunk(
        'http://example.com/upload/location',
        mockFile as Express.Multer.File,
        '0',
        'Bearer token',
      );

      expect(result).toEqual({
        data: mockError.response.data,
        status: mockError.response.status,
        headers: mockError.response.headers,
      });
    });
  });
});
