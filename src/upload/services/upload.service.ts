import { Injectable, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as FormData from 'form-data';

@Injectable()
export class UploadService {
  private readonly baseUrlApi: string;

  constructor(private readonly configService: ConfigService) {
    this.baseUrlApi = this.configService.get<string>('baseVidMobApiUrl', '');
  }

  async createFileUpload(
    mediaId: string,
    uploadLength: string,
    uploadMetadata: string,
    authorization: string,
  ): Promise<any> {
    const headers = {
      'Upload-Length': uploadLength,
      'Upload-Metadata': uploadMetadata,
      Authorization: authorization,
    };

    try {
      const response = await axios.post(
        `${this.baseUrlApi}/api/v1/tus/${mediaId}`,
        {},
        { headers },
      );
      return {
        data: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      if (error.response) {
        return {
          data: error.response.data,
          headers: error.response.headers,
          status: error.response.status,
        };
      }

      return {
        data: 'Network Error',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async uploadChunk(
    location: string,
    file: Express.Multer.File,
    uploadOffset: string,
    authorization: string,
  ): Promise<any> {
    const formData = new FormData();
    formData.append('file', file.buffer, file.originalname);

    const formHeaders = formData.getHeaders();
    const headers = {
      ...formHeaders,
      'Upload-Offset': uploadOffset,
      Authorization: authorization,
    };

    try {
      const response = await axios.patch(location, formData, { headers });
      return {
        data: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      if (error.response) {
        return {
          data: error.response.data,
          headers: error.response.headers,
          status: error.response.status,
        };
      }
      return {
        data: 'Network Error',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
}
