import { HttpStatus, Injectable } from '@nestjs/common';
import axios from 'axios';
import { getFormUrlEncodedHeaders } from '../api.utils';
import { ConfigService } from '@nestjs/config';
import { RefreshTokenRequestDto } from '../dto/refresh-token-request.dto';
import { InjectMapper } from '@automapper/nestjs';
import {
  ResponseFromApiDto,
  SuccessResponseDto,
} from '../dto/login-success-response.dto';
import { Mapper } from '@automapper/core';

@Injectable()
export class LoginService {
  clientId: string;
  clientSecret: string;
  baseUrlLoginService: string;

  constructor(
    private configService: ConfigService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.clientId =
      process.env.clientId || this.configService.get<string>('clientId', '');
    this.clientSecret =
      process.env.clientSecret ||
      this.configService.get<string>('clientSecret', '');
    this.baseUrlLoginService = this.configService.get<string>(
      'baseVidMobApiUrl',
      '',
    );
  }

  async login(
    username: string,
    password: string,
    twoFactorAuthPassword = null,
  ) {
    const loginEndpointUrl = `${this.baseUrlLoginService}/api/noauth/v1/applicationLogin`;

    try {
      const response = await axios.post(loginEndpointUrl, {
        username,
        password,
        twoFactorAuthPassword,
        clientId: this.clientId,
        clientSecret: this.clientSecret,
      });
      return {
        data: this.classMapper.map(
          response.data.result,
          ResponseFromApiDto,
          SuccessResponseDto,
        ),
        statusCode: response.status,
      };
    } catch (error) {
      const errorResponse = {
        error: error.response?.data.error || 'Unknown Error',
      };
      return {
        data: errorResponse,
        statusCode: error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async refreshToken(refreshTokenRequestDto: RefreshTokenRequestDto) {
    const refreshTokenUrl = `${this.baseUrlLoginService}/oauth2/access_token`;

    const clientId = process.env.clientId || this.clientId;
    const clientSecret = process.env.clientSecret || this.clientSecret;

    const data =
      `grant_type=${encodeURIComponent('refresh_token')}&` +
      `code=${encodeURIComponent(refreshTokenRequestDto.refreshToken)}&` +
      `client_id=${encodeURIComponent(clientId)}&` +
      `client_secret=${encodeURIComponent(clientSecret)}`;

    try {
      const response = await axios.post(refreshTokenUrl, data, {
        headers: getFormUrlEncodedHeaders(),
      });
      return {
        data: this.classMapper.map(
          response.data,
          ResponseFromApiDto,
          SuccessResponseDto,
        ),
        statusCode: response.status,
      };
    } catch (error) {
      const errorResponse = {
        error: error.response?.data.error || 'Unknown Error',
        errorDescription:
          error.response?.data.error_description ||
          'An unexpected error occurred',
      };
      return {
        data: errorResponse,
        statusCode: error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
}
