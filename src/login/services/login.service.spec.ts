import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import { LoginService } from './login.service';

jest.mock('axios');

const username = 'testuser';
const password = 'testpassword';

const testResponse = {
  tokenType: 'Bearer',
  refreshToken: 'new-refresh-token',
  accessToken: 'new-access-token',
  expiresIn: 3600,
};

describe('LoginService', () => {
  let loginService: LoginService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: LoginService,
          useValue: {
            login: jest.fn().mockResolvedValue(testResponse),
          },
        },
      ],
    }).compile();

    loginService = module.get<LoginService>(LoginService);
  });

  it('should be defined', () => {
    expect(loginService).toBeDefined();
  });

  it('should successfully login', async () => {
    const username = 'testuser';
    const password = 'testpassword';
    jest.spyOn(axios, 'post').mockResolvedValue({
      data: testResponse,
    });
    const response = await loginService.login(username, password);
    expect(response).toEqual(testResponse);
  });

  it('should handle login failure', async () => {
    jest.spyOn(axios, 'post').mockRejectedValue(new Error('Network Error'));

    loginService.login = jest
      .fn()
      .mockRejectedValue(new Error('Network Error'));

    try {
      await loginService.login(username, password);
      expect(true).toBe(false);
    } catch (error) {
      expect(error.message).toBe('Network Error');
    }
  });
});
