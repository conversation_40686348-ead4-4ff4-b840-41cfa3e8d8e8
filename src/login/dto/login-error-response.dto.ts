import { ApiProperty } from '@nestjs/swagger';

class ErrorDetailsDto {
  @ApiProperty({ example: 'vidmob.login.invalid' })
  code: string;

  @ApiProperty({ example: 'invalid' })
  type: string;

  @ApiProperty({ example: 'login' })
  system: string;

  @ApiProperty({
    example: 'You entered invalid login information, please try again.',
  })
  message: string;
}

export class ErrorLoginResponseDto {
  @ApiProperty({ type: ErrorDetailsDto })
  error: ErrorDetailsDto;
}
