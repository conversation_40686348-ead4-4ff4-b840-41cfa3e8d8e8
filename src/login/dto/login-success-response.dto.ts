import { ApiProperty } from '@nestjs/swagger';

export class SuccessResponseDto {
  @ApiProperty({ example: 'Bearer' })
  tokenType: string;

  @ApiProperty({ example: '...' }) // Example token
  refreshToken: string;

  @ApiProperty({ example: '...' }) // Example token
  accessToken: string;

  @ApiProperty({ example: 3600 })
  expiresIn: number;
}

export class ResponseFromApiDto {
  @ApiProperty({ example: 'Bearer' })
  token_type: string;

  @ApiProperty({ example: '...' }) // Example token
  refresh_token: string;

  @ApiProperty({ example: '...' }) // Example token
  access_token: string;

  @ApiProperty({ example: 3600 })
  expires_in: number;
}
