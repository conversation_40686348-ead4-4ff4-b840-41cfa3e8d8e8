import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';

export class LoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the user',
  })
  @IsEmail()
  username: string;

  @ApiProperty({
    example: 'Passw0rd!',
    description:
      'The password of the user. Must be between 8 and 64 characters and must contain at least 1 letter and 1 non-letter.',
  })
  @IsString()
  @Length(8, 64)
  @Matches(/^.*(?=.*[a-zA-Z])(?=.*[^a-zA-Z\s]).*$/, {
    message:
      'Password must be between 8 and 64 characters and must contain at least 1 letter and 1 non-letter.',
  })
  password: string;

  @ApiProperty({
    example: '123456',
    description: 'The two-factor authentication password of the user',
  })
  @IsOptional()
  @IsNumber()
  twoFactorAuthPassword?: number;
}
