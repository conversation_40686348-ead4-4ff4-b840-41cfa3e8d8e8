import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus, Response } from '@nestjs/common';
import { LoginController } from './login.controller';
import { LoginService } from '../services/login.service';
import { LoginDto } from '../dto/login-request.dto';
import { RefreshTokenRequestDto } from '../dto/refresh-token-request.dto';

describe('LoginController', () => {
  let loginController: LoginController;
  let loginService: LoginService;

  const mockResponse = (): Partial<Response> => {
    const res: Partial<Response> = {
      status: jest.fn().mockReturnThis() as unknown as Response['status'],
      json: jest.fn().mockReturnThis(),
    };
    return res as Response;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LoginController],
      providers: [
        {
          provide: LoginService,
          useValue: {
            login: jest.fn(),
            refreshToken: jest.fn(),
          },
        },
      ],
    }).compile();

    loginController = module.get<LoginController>(LoginController);
    loginService = module.get<LoginService>(LoginService);
  });

  it('should be defined', () => {
    expect(loginController).toBeDefined();
  });

  it('should successfully login', async () => {
    const loginDto: LoginDto = {
      username: 'testuser',
      password: 'testpassword',
      twoFactorAuthPassword: null,
    };
    const successResponse = {
      data: {
        tokenType: 'Bearer',
        accessToken: 'token',
        refreshToken: 'refreshToken',
        expiresIn: 3600,
      },
      statusCode: HttpStatus.CREATED,
    };
    jest.spyOn(loginService, 'login').mockResolvedValue(successResponse);

    const res = mockResponse();
    await loginController.login(loginDto, res as any);

    expect(loginService.login).toHaveBeenCalledWith(
      loginDto.username,
      loginDto.password,
      null,
    );
    expect(res.status).toHaveBeenCalledWith(successResponse.statusCode);
    expect(res.json).toHaveBeenCalledWith(successResponse.data);
  });

  it('should return error for invalid login credentials', async () => {
    const loginDto: LoginDto = {
      username: 'wronguser',
      password: 'wrongpassword',
      twoFactorAuthPassword: null,
    };
    const errorResponse = {
      data: {
        error: {
          code: 'vidmob.login.invalid',
          type: 'invalid',
          system: 'login',
          message: 'You entered invalid login information, please try again.',
        },
      },
      statusCode: HttpStatus.UNAUTHORIZED,
    };

    jest.spyOn(loginService, 'login').mockResolvedValue(errorResponse);

    const res = mockResponse();
    await loginController.login(loginDto, res as any);

    expect(loginService.login).toHaveBeenCalledWith(
      loginDto.username,
      loginDto.password,
      null,
    );
    expect(res.status).toHaveBeenCalledWith(errorResponse.statusCode);
    expect(res.json).toHaveBeenCalledWith(errorResponse.data);
  });

  it('should successfully refresh token', async () => {
    const refreshTokenRequestDto: RefreshTokenRequestDto = {
      refreshToken: 'valid-refresh-token-code',
    };
    const successResponse = {
      data: {
        tokenType: 'Bearer',
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 3600,
      },
      statusCode: HttpStatus.OK,
    };
    jest.spyOn(loginService, 'refreshToken').mockResolvedValue(successResponse);

    const res = mockResponse();
    await loginController.refreshToken(refreshTokenRequestDto, res as any);

    expect(loginService.refreshToken).toHaveBeenCalledWith(
      refreshTokenRequestDto,
    );
    expect(res.status).toHaveBeenCalledWith(successResponse.statusCode);
    expect(res.json).toHaveBeenCalledWith(successResponse.data);
  });

  it('should return error for invalid refresh token', async () => {
    const refreshTokenRequestDto: RefreshTokenRequestDto = {
      refreshToken: 'invalid-refresh-token-code',
    };
    const errorResponse = {
      data: {
        error: 'invalid_grant',
        errorDescription: 'Refresh token invalid',
      },
      statusCode: HttpStatus.UNAUTHORIZED,
    };

    jest.spyOn(loginService, 'refreshToken').mockResolvedValue(errorResponse);

    const res = mockResponse();
    await loginController.refreshToken(refreshTokenRequestDto, res as any);

    expect(loginService.refreshToken).toHaveBeenCalledWith(
      refreshTokenRequestDto,
    );
    expect(res.status).toHaveBeenCalledWith(errorResponse.statusCode);
    expect(res.json).toHaveBeenCalledWith(errorResponse.data);
  });

  it('should successfully login with a valid twoFactorAuthPassword', async () => {
    const loginDto: LoginDto = {
      username: 'testuser',
      password: 'testpassword',
      twoFactorAuthPassword: 123456,
    };
    const successResponse = {
      data: {
        tokenType: 'Bearer',
        accessToken: 'token',
        refreshToken: 'refreshToken',
        expiresIn: 3600,
      },
      statusCode: HttpStatus.CREATED,
    };

    jest.spyOn(loginService, 'login').mockResolvedValue(successResponse);

    const res = mockResponse();
    await loginController.login(loginDto, res as any);

    expect(loginService.login).toHaveBeenCalledWith(
      loginDto.username,
      loginDto.password,
      loginDto.twoFactorAuthPassword,
    );
    expect(res.status).toHaveBeenCalledWith(successResponse.statusCode);
    expect(res.json).toHaveBeenCalledWith(successResponse.data);
  });

  it('should return a validation error for invalid twoFactorAuthPassword', async () => {
    const invalidTwoFactorAuthPassword = 0;
    const loginDto: LoginDto = {
      username: 'testuser',
      password: 'testpassword',
      twoFactorAuthPassword: invalidTwoFactorAuthPassword,
    };
    const errorResponse = {
      error: {
        code: 'vidmob.common.validation',
        type: 'validation',
        system: 'common',
        message: `${invalidTwoFactorAuthPassword} is not a valid value for twoFactorAuthPassword`,
        data: {
          field: 'twoFactorAuthPassword',
          rejectedValue: invalidTwoFactorAuthPassword,
        },
      },
    };
    const expectedStatusCode = HttpStatus.BAD_REQUEST;

    jest.spyOn(loginService, 'login').mockResolvedValue({
      data: errorResponse,
      statusCode: expectedStatusCode,
    });

    const res = mockResponse();
    await loginController.login(loginDto, res as any);

    expect(loginService.login).toHaveBeenCalledWith(
      loginDto.username,
      loginDto.password,
      invalidTwoFactorAuthPassword,
    );
    expect(res.status).toHaveBeenCalledWith(expectedStatusCode);
    expect(res.json).toHaveBeenCalledWith(errorResponse);
  });
});
