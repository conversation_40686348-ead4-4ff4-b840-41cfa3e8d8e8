import {
  Body,
  Controller,
  Post,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { LoginService } from '../services/login.service';
import { LoginDto } from '../dto/login-request.dto';
import { SuccessResponseDto } from '../dto/login-success-response.dto';
import { ErrorLoginResponseDto } from '../dto/login-error-response.dto';
import { RefreshTokenRequestDto } from '../dto/refresh-token-request.dto';
import { RefreshTokenErrorResponseDto } from '../dto/refresh-token-error-response.dto';

@ApiTags('Login')
@Controller('login')
export class LoginController {
  constructor(private loginService: LoginService) {}

  @Post()
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({
    status: 201,
    description: 'Login successful',
    type: SuccessResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorLoginResponseDto,
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async login(@Body() loginDto: LoginDto, @Res() res: Response) {
    const { username, password, twoFactorAuthPassword } = loginDto;

    const response = await this.loginService.login(
      username,
      password,
      twoFactorAuthPassword,
    );

    return res.status(response.statusCode).json(response.data);
  }

  @Post('refresh-token')
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({
    status: 200,
    description: 'Token refreshed successfully',
    type: SuccessResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid refresh token',
    type: RefreshTokenErrorResponseDto,
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async refreshToken(
    @Body() refreshTokenRequestDto: RefreshTokenRequestDto,
    @Res() res: Response,
  ) {
    const response = await this.loginService.refreshToken(
      refreshTokenRequestDto,
    );

    return res.status(response.statusCode).json(response.data);
  }
}
