import { Module } from '@nestjs/common';
import { LoginController } from './controllers/login.controller';
import { LoginService } from './services/login.service';
import { ConfigModule } from '@nestjs/config';
import { LoginProfile } from '../login/mapper/login.profile';

@Module({
  imports: [ConfigModule],
  providers: [LoginService, LoginProfile],
  controllers: [LoginController],
})
export class LoginModule {}
