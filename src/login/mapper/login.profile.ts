import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { Injectable } from '@nestjs/common';
import {
  ResponseFromApiDto,
  SuccessResponseDto,
} from '../dto/login-success-response.dto';

@Injectable()
export class LoginProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        ResponseFromApiDto,
        SuccessResponseDto,
        forMember(
          (dest) => dest.tokenType,
          mapFrom((src) => src.token_type),
        ),
        forMember(
          (dest) => dest.refreshToken,
          mapFrom((src) => src.refresh_token),
        ),
        forMember(
          (dest) => dest.accessToken,
          mapFrom((src) => src.access_token),
        ),
        forMember(
          (dest) => dest.expiresIn,
          mapFrom((src) => src.expires_in),
        ),
      );
    };
  }
}
