import {
  Controller,
  Get,
  Logger,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AppService } from './app.service';
import { ApiKeyScopes } from './auth/decorators/api-key.permission.decorator';
import {
  OrganizationService,
  ReadOrganizationDto,
  ReadWorkspaceDto,
} from '@vidmob/vidmob-organization-service-sdk';
import { Create201Response } from '@vidmob/vidmob-organization-service-sdk/dist/model/create201Response';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import {
  OrganizationApiKeyResponseDto,
  WorkspaceApiKeyResponseDto,
} from './app.dto';
import { ThrottlerGuard } from '@nestjs/throttler';
import { KinesisInterceptor } from './interceptor/kinesis/kinesis.interceptor';
import { FindWorkspacesByOrganization200Response } from '@vidmob/vidmob-organization-service-sdk/dist/model/findWorkspacesByOrganization200Response';
import { SearchParamsDto } from './plugin/workspace/dto/search-params.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { RuntimeException } from '@nestjs/core/errors/exceptions';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Public API')
@UseInterceptors(KinesisInterceptor)
@Controller()
export class AppController {
  private logger: Logger = new Logger(AppController.name);
  constructor(
    private readonly appService: AppService,
    private readonly organizationService: OrganizationService,
  ) {}

  @VmApiOkResponse({
    type: OrganizationApiKeyResponseDto,
    description: 'Returns the organization id and name',
    headers: {
      authorization: {
        description: 'The authorization token. Use "Bearer " before the token',
      },
    },
  })
  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({ scopePermissions: [] })
  @Get('organization')
  async getMe(@Request() req: any) {
    const organizationId = req.organizationId;
    try {
      const response: Create201Response =
        await this.organizationService.findOneAsPromise(organizationId);
      const organizationDto: ReadOrganizationDto = response.result;
      return {
        id: organizationDto.id,
        name: organizationDto.name,
      };
    } catch (error) {
      this.logger.error(
        `Error Occurred in getOrganization: ${JSON.stringify(error)}`,
      );
      return new RuntimeException('Error processing request');
    }
  }

  @VmApiOkResponse({
    type: WorkspaceApiKeyResponseDto,
    description: 'Returns all workspaces associated to the organization ',
    headers: {
      authorization: {
        description: 'The authorization token. Use "Bearer " before the token',
      },
    },
  })
  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({ scopePermissions: [] })
  @Get('workspaces')
  async getWorkspaces(
    @Request() req: any,
    @Query() searchParams: SearchParamsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const organizationId = req.organizationId;
    try {
      const workspaceResponse: FindWorkspacesByOrganization200Response =
        await this.organizationService.findWorkspacesByOrganizationAsPromise(
          organizationId,
          searchParams.search,
          searchParams.market,
          searchParams.brand,
          paginationOptions.offset,
          paginationOptions.perPage,
          paginationOptions.queryId,
        );
      const workspaces: ReadWorkspaceDto[] = workspaceResponse.result;
      const workspaceInfo: WorkspaceApiKeyResponseDto[] = workspaces.map(
        (w) => {
          return { id: w.id, name: w.name };
        },
      );
      return new PaginatedResultArray<WorkspaceApiKeyResponseDto>(
        workspaceInfo,
        workspaceResponse.pagination.totalSize,
        workspaceResponse.pagination.queryId,
      );
    } catch (error) {
      this.logger.error(
        `Error Occurred in getWorkspaces: ${JSON.stringify(error)}`,
      );
      return new RuntimeException('Error processing request');
    }
  }
}
