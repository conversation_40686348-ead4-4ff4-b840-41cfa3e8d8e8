import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class GetApiMediaStatusQueryParamsDto {
  @ApiProperty({
    description: 'Specifies the media source',
    type: String,
    required: false,
    example: 'dam-name',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({
    description: 'Specifies the media version',
    type: String,
    required: false,
    example: '2',
  })
  @IsOptional()
  @IsString()
  version?: string;
}
