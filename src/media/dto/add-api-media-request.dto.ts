import { AutoMap } from '@automapper/classes';
import { <PERSON><PERSON><PERSON>y, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class AddApiMediaRequestDto {
  @IsString()
  id: string;

  @IsString()
  @IsOptional()
  version?: string;

  @IsString()
  @IsOptional()
  source?: string;

  @IsString()
  url: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  brands?: string[];

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  markets?: string[];

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  channels?: string[];

  @IsNumber()
  @IsOptional()
  workspaceId?: number;
}
