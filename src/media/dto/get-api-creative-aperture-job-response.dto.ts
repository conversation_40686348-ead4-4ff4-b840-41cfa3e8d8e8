import { AutoMap } from '@automapper/classes';
import { IsOptional } from 'class-validator';

export class GetApiCreativeApertureJobResponse {
  @AutoMap()
  jobId: string;

  @AutoMap()
  status: string;

  @AutoMap()
  dateCreated: string;

  @AutoMap()
  @IsOptional()
  dateUpdated?: string;

  @AutoMap()
  @IsOptional()
  message?: string;

  @AutoMap()
  @IsOptional()
  downloadUrlJSON?: string;

  @AutoMap()
  @IsOptional()
  downloadUrlCSV?: string;

  @AutoMap()
  @IsOptional()
  errors?: Record<string, string>;

  @AutoMap()
  creativeIdentifiers: string[];

  @AutoMap()
  clientTags: Record<string, string>;
}
