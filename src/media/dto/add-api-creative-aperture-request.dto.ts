import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsObject,
  ValidateNested,
} from 'class-validator';
import {
  CREATIVE_APERTURE_MAX_CREATIVES,
  CREATIVE_APERTURE_MIN_CREATIVES,
} from 'src/constants/platform.constants';
import { CreativeApertureCreativeDto } from './creative-aperture-creative-dto';

export class AddApiCreativeApertureRequestDto {
  @AutoMap()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(CREATIVE_APERTURE_MIN_CREATIVES)
  @ArrayMaxSize(CREATIVE_APERTURE_MAX_CREATIVES)
  @Type(() => CreativeApertureCreativeDto)
  @ApiProperty({ type: [CreativeApertureCreativeDto] })
  creatives: CreativeApertureCreativeDto[];

  @AutoMap()
  @IsObject()
  @ApiProperty({
    description: 'Client tags as key value pairs',
    example: { brand: 'vidmob', channels: 'meta,tiktok' },
  })
  clientTags: Record<string, string>;
}
