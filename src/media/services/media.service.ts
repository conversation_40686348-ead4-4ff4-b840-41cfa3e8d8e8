import { Injectable, Logger } from '@nestjs/common';

import {
  APIMediaService,
  AddMediaRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { DefaultService as CreativeApertureService } from '@vidmob/vidmob-soa-media-annotation-service-sdk';
import { AddApiMediaRequestDto } from '../dto/add-api-media-request.dto';
import { GetApiMediaStatusQueryParamsDto } from '../dto/get-api-media-status-query-params.dto';
import { GetApiMediaStatusResponseDto } from '../dto/get-api-media-status-response.dto';
import { AddApiCreativeApertureRequestDto } from '../dto/add-api-creative-aperture-request.dto';
import { AddApiCreativeApertureResponseDto } from '../dto/add-api-creative-aperture-response.dto';
import { GetApiCreativeApertureJobResponse } from '../dto/get-api-creative-aperture-job-response.dto';

@Injectable()
export class MediaService {
  private readonly logger = new Logger(MediaService.name);

  constructor(
    private readonly mediaService: APIMediaService,
    private readonly creativeApertureService: CreativeApertureService,
  ) {}

  public async addApiMedia(
    orgId: string,
    addMediaRequest: AddApiMediaRequestDto,
  ) {
    const addMediaRequestDto: AddMediaRequestDto = {
      organizationId: orgId,
      workspaceId: addMediaRequest.workspaceId,
      personId: undefined, //default to the scoring bot user
      extMediaId: addMediaRequest.id,
      extMediaVersion: addMediaRequest.version,
      source: addMediaRequest.source,
      extMediaUrl: addMediaRequest.url,
      name: addMediaRequest.name,
      description: addMediaRequest.description,
      brands: addMediaRequest.brands,
      markets: addMediaRequest.markets,
      channels: addMediaRequest.channels,
    };

    const response = await this.mediaService.addMediaAsPromise(
      addMediaRequestDto,
    );
    return {
      uniqueId: response.result.uniqueId,
    };
  }

  public async getMediaStatus(
    organizationId: string,
    mediaId: string,
    queryParams: GetApiMediaStatusQueryParamsDto,
  ): Promise<GetApiMediaStatusResponseDto> {
    const response = await this.mediaService.getMediaStatusAsPromise(
      organizationId,
      mediaId,
      queryParams.source,
      queryParams.version,
    );
    const { status, source, extId, extVersion } = response.result;
    return {
      uniqueId: response.result.id,
      source: source,
      id: extId,
      version: extVersion,
      status,
    };
  }

  public addCreativeApertureMock(
    addMediaAperture: AddApiCreativeApertureRequestDto,
  ): AddApiCreativeApertureResponseDto {
    const creativeIdentifiers = addMediaAperture.creatives.map(
      (creative) => creative.id,
    );

    return this.mockAddCreativeApertureSuccess(creativeIdentifiers);
  }

  public getCreativeApertureJobMock(
    jobId: string,
  ): GetApiCreativeApertureJobResponse {
    return this.mockGetCreativeApertureJob(jobId);
  }

  public async getCreativeApertureJobFromMediaAnnotation(
    jobId: string,
    organizationId: string,
  ): Promise<GetApiCreativeApertureJobResponse> {
    return await this.creativeApertureService.getCreativeApertureJobAsPromise(
      jobId,
      organizationId,
    );
  }

  public async addCreativeAperture(
    addMediaAperture: AddApiCreativeApertureRequestDto,
    organizationId: string,
    organizationApiKeyId: string,
  ): Promise<GetApiCreativeApertureJobResponse> {
    return await this.creativeApertureService.createCreativeApertureAsPromise({
      organizationId,
      organizationApiKeyId,
      ...addMediaAperture,
    });
  }

  private mockAddCreativeApertureSuccess(
    creativeIdentifiers: string[],
  ): AddApiCreativeApertureResponseDto {
    return {
      jobId: '07fd81fc-41fc-41f6-90ea-72bf141996dc',
      status: 'QUEUED',
      dateCreated: new Date(Date.now()).toISOString(),
      creativeIdentifiers,
      clientTags: {
        brand: 'VidMob',
        market: 'US',
        channels: 'meta, dv360',
      },
    };
  }

  private mockGetCreativeApertureJob(jobId: string) {
    return {
      jobId,
      status: 'COMPLETED',
      dateCreated: new Date(Date.now()).toISOString(),
      dateUpdated: new Date(Date.now()).toISOString(),
      creativeIdentifiers: [
        '049PM4U2MO',
        '3ZE8H08159',
        '5MGSGQBCNY',
        '8IFGLWI3LC',
        'AHHNZJ7ZR3',
        'DWB7UM9F4C',
        'EK9O7XSVC1',
        'G16MVZKY9A',
        'GRNL09KCZS',
        'JBNHOQD0F8',
        'JTT815M9XB',
        'KBWIIP5JX7',
        'MVRE2ZD3R9',
        'MZHAB5YQ9U',
        'O78UGBTTGY',
        'QU0D40EET3',
        'SCPHSQUXMZ',
        'TXBCB789WF',
        'U2MKMP2NLR',
        'YLP8A9HZLR',
      ],
      downloadUrlCSV:
        'https://downloads.vidmob.com/creative-aperture/sample.csv',
      clientTags: {
        brand: 'VidMob',
        market: 'US',
        channels: 'meta, dv360',
      },
    };
  }
}
