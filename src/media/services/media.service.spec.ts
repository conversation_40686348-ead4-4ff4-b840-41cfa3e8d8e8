import { Test, TestingModule } from '@nestjs/testing';
import { MediaService } from './media.service';
import { APIMediaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { DefaultService as CreativeApertureService } from '@vidmob/vidmob-soa-media-annotation-service-sdk';

describe('MediaService', () => {
  let service: MediaService;
  const mockAddMediaAsPromise = jest.fn();
  const mockGetMediaStatusAsPromise = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaService,
        {
          provide: APIMediaService,
          useValue: {
            addMediaAsPromise: mockAddMediaAsPromise,
            getMediaStatusAsPromise: mockGetMediaStatusAsPromise,
          },
        },
        {
          provide: CreativeApertureService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<MediaService>(MediaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('add media should call the sdk', async () => {
    const media = {
      source: 'source',
      id: 'id',
      version: 'version',
      url: 'url',
    };
    const result = {
      uniqueId: 'uniqueId',
    };
    mockAddMediaAsPromise.mockResolvedValue({ result });
    const response = await service.addApiMedia('organizationId', media);
    expect(mockAddMediaAsPromise).toHaveBeenCalledWith(
      expect.objectContaining({
        organizationId: 'organizationId',
        workspaceId: undefined,
        personId: undefined,
        extMediaId: media.id,
        extMediaVersion: media.version,
        source: media.source,
        extMediaUrl: media.url,
      }),
    );
    expect(response).toEqual({ uniqueId: result.uniqueId });
  });

  it('get status should reformat the response', async () => {
    const statusResponse = {
      source: 'source',
      id: 'id',
      extId: 'extId',
      extVersion: 'version',
      status: 'PROCESSING',
    };
    mockGetMediaStatusAsPromise.mockResolvedValue({ result: statusResponse });
    const result = await service.getMediaStatus('organizationId', 'mediaId', {
      source: 'source',
      version: 'version',
    });
    expect(result).toEqual({
      uniqueId: expect.any(String),
      source: statusResponse.source,
      id: statusResponse.extId,
      version: statusResponse.extVersion,
      status: statusResponse.status,
    });
  });
});
