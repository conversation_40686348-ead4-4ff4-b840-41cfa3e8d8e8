import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Logger,
  Param,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { MediaService } from '../services/media.service';

import { ApiKeyScopes } from '../../auth/decorators/api-key.permission.decorator';
import { ThrottlerGuard } from '@nestjs/throttler';
import { AddApiMediaRequestDto } from '../dto/add-api-media-request.dto';
import { AddApiMediaResponseDto } from '../dto/add-api-media-response.dto';
import { GetApiMediaStatusResponseDto } from '../dto/get-api-media-status-response.dto';
import { GetApiMediaStatusQueryParamsDto } from '../dto/get-api-media-status-query-params.dto';
import {
  VmApiCreatedResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { AddApiCreativeApertureRequestDto } from '../dto/add-api-creative-aperture-request.dto';
import { AddApiCreativeApertureResponseDto } from '../dto/add-api-creative-aperture-response.dto';
import { GetApiCreativeApertureJobResponse } from '../dto/get-api-creative-aperture-job-response.dto';
import { CREATIVE_APERTURE_MAX_CLIENT_TAGS } from '../../constants/platform.constants';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Public API')
@Controller('media')
export class MediaController {
  private readonly logger = new Logger(MediaController.name);

  constructor(private readonly mediaService: MediaService) {}

  /**
   * Add media to the system. This queues the media to be downloaded, sent through content recognition and scored.
   * Brand dependent scores use the brands submitted as part of the request as identifiers.
   * Scores are generated using the global criteria for the organization.
   * @param addMediaRequest - The media metadata and download url.
   * @param req - used to get the organizationId from the request
   */
  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: 'scoring',
        permission: 'read_write',
      },
    ],
  })
  @VmApiCreatedResponse({
    description: 'Returns the uniqueId assigned to the media',
  })
  @Post()
  async addMedia(
    @Body() addMediaRequest: AddApiMediaRequestDto,
    @Request() req: any,
  ): Promise<AddApiMediaResponseDto> {
    const organizationId = req.organizationId;
    const { id, version } = addMediaRequest;
    this.logger.log(
      `Request to add api media (${id}, ${version}) to ${organizationId}`,
    );
    return await this.mediaService.addApiMedia(organizationId, addMediaRequest);
  }

  /**
   * Get the status of a media item by id. This method support querying by both the external and the internal (UUID) id.
   * @param mediaId - The media id to query (could be external or internal)
   * @param getApiMediaStatusQueryParamsDto - Allow source and version to be specified if using external id
   * @param req - used to get the organizationId from the request
   */
  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: 'scoring',
        permission: 'read',
      },
    ],
  })
  @VmApiOkResponse({
    description: 'Returns the status of the media',
  })
  @Get(':mediaId/status')
  async getMediaStatusByExternalId(
    @Param('mediaId') mediaId: string,
    @Query() getApiMediaStatusQueryParamsDto: GetApiMediaStatusQueryParamsDto,
    @Request() req: any,
  ): Promise<GetApiMediaStatusResponseDto> {
    const organizationId = req.organizationId;
    const { source, version } = getApiMediaStatusQueryParamsDto;
    this.logger.log(
      `Requesting status of api media for organizationId=${organizationId}, mediaId=${mediaId}, source=${source}, version=${version}`,
    );
    return await this.mediaService.getMediaStatus(
      organizationId,
      mediaId,
      getApiMediaStatusQueryParamsDto,
    );
  }

  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: 'aperture',
        permission: 'read_write',
      },
    ],
  })
  @Post('/aperture')
  @VmApiCreatedResponse({
    description:
      'It creates a creative aperture job for the creatives in the request',
  })
  async addCreativeAperture(
    @Body() addMediaAperture: AddApiCreativeApertureRequestDto,
    @Request() req: any,
  ): Promise<AddApiCreativeApertureResponseDto> {
    if (
      addMediaAperture.clientTags &&
      Object.keys(addMediaAperture.clientTags).length >
        CREATIVE_APERTURE_MAX_CLIENT_TAGS
    ) {
      throw new BadRequestException(
        `Client tags cannot exceed maximum of ${CREATIVE_APERTURE_MAX_CLIENT_TAGS} key value pairs.`,
      );
    }

    const { organizationId, organizationApiKeyId } = req;
    return await this.mediaService.addCreativeAperture(
      addMediaAperture,
      organizationId,
      organizationApiKeyId,
    );
  }

  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: 'aperture',
        permission: 'read',
      },
    ],
  })
  @Get('/aperture/:jobId')
  @VmApiCreatedResponse({
    description:
      'Returns the job status information using media annotation service sdk',
  })
  async getCreativeApertureJobFromMediaAnnotation(
    @Param('jobId') jobId: string,
    @Request() req: any,
  ): Promise<GetApiCreativeApertureJobResponse> {
    const { organizationId } = req;
    return await this.mediaService.getCreativeApertureJobFromMediaAnnotation(
      jobId,
      organizationId,
    );
  }
}
