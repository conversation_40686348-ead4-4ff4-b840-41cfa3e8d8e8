import { Test, TestingModule } from '@nestjs/testing';
import { MediaController } from './media.controller';
import { ThrottlerModule } from '@nestjs/throttler';
import { MediaService } from '../services/media.service';

describe('MediaController', () => {
  let controller: MediaController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ThrottlerModule.forRoot({
          throttlers: [{ ttl: 60, limit: 10 }],
        }),
      ],
      providers: [
        {
          provide: MediaService,
          useValue: {},
        },
      ],
      controllers: [MediaController],
    }).compile();

    controller = module.get<MediaController>(MediaController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
