import { ExecutionContext } from '@nestjs/common';
import { ApiKeyScopes } from '../../auth/decorators/api-key.permission.decorator';

class KinesesStreamTestController {
  @ApiKeyScopes({
    scopePermissions: [],
  })
  get() {
    return 'Success!';
  }
}

export const generateContext = (
  method: string,
  url: string,
): ExecutionContext => {
  const request =
    url === '/v1/noOrganization'
      ? {}
      : { headers: { authorization: 'test' }, organizationId: 'test-org' };
  const controller = new KinesesStreamTestController();

  // get the function on controller from the method string
  const handler = Reflect.get(controller, method);
  return {
    getClass: () => KinesesStreamTestController,
    getHandler: () => handler,
    switchToHttp: () => ({ getRequest: () => request }),
  } as ExecutionContext;
};
