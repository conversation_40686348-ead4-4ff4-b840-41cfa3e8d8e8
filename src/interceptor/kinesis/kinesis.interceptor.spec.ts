import { Test, TestingModule } from '@nestjs/testing';
import { KinesisInterceptor } from './kinesis.interceptor';
import { KinesisService } from '@vidmob/vidmob-nestjs-common';
import { generateContext } from '../common/kinesis.interceptor.spec.common';
import { firstValueFrom, of } from 'rxjs';
import { CallHandler } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

const mockConfig: Record<string, any> = {
  KINESIS_ENABLED: true,
  kinesis: {
    streamName: 'test-stream-name',
    streamArn: 'test-stream-arn',
  },
};

describe('KinesisInterceptor', () => {
  let interceptor: KinesisInterceptor;
  let kinesisService: KinesisService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KinesisInterceptor,
        {
          provide: ConfigService,
          useValue: {
            get: (key: string) =>
              key in mockConfig ? mockConfig[key] : undefined,
          },
        },
        {
          provide: KinesisService,
          useValue: {
            putRecord: jest.fn(),
          },
        },
      ],
    }).compile();
    interceptor = module.get<KinesisInterceptor>(KinesisInterceptor);
    kinesisService = module.get<KinesisService>(KinesisService);
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });
  describe('intercept', () => {
    it('should call kinesisService.putRecord', async () => {
      const context = generateContext('get', '/v1/withOrganizationId');
      const data = { test: 'data' };
      const next: CallHandler = { handle: () => of(data) };
      const interception = await interceptor.intercept(context, next);
      await firstValueFrom(interception);
      expect(kinesisService.putRecord).toHaveBeenCalled();
    });
    it('should not call kinesisService.putRecord if organizationId is not present in the request', async () => {
      const context = generateContext('get', '/v1/noOrganization');
      const next: CallHandler = { handle: () => of({}) };
      const interception = await interceptor.intercept(context, next);
      await firstValueFrom(interception);
      expect(kinesisService.putRecord).not.toHaveBeenCalled();
    });
  });
});
