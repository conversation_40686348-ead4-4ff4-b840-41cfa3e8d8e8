import {
  CallHand<PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, tap } from 'rxjs';
import { PutRecordInput } from '@aws-sdk/client-kinesis';
import { KinesisService } from '@vidmob/vidmob-nestjs-common';
import { ConfigService } from '@nestjs/config';
import { KinesisEndpointStreamData } from './kinesis.interceptor.dto';
import { Request } from 'express';
import { Reflector } from '@nestjs/core';
import { isApiKeyEndpoint, isPublicEndpoint } from '../../utils/reflectorUtils';

export type KinesisConfig = {
  streamName: string;
  streamArn: string;
};

@Injectable()
export class KinesisInterceptor implements NestInterceptor {
  private readonly logger: Logger = new Logger(KinesisInterceptor.name);
  private readonly kinesisConfig: KinesisConfig;
  private readonly isKinesisOn: boolean;

  constructor(
    private readonly reflector: Reflector,
    private readonly kinesis: KinesisService,
    private readonly configService: ConfigService,
  ) {
    this.kinesisConfig = configService.get<KinesisConfig>('kinesis');
    this.isKinesisOn = configService.get<boolean>('KINESIS_ENABLED');
  }

  /**
   * Intercepts the requests/response and puts messages to a Kinesis Stream
   * @param context
   * @param next
   */
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    return next.handle().pipe(tap(() => this.handleKinesisEvent(context)));
  }

  private async handleKinesisEvent(context: ExecutionContext) {
    if (!this.shouldStreamData(context)) {
      return;
    }
    if (!this.kinesisConfig || !this.isKinesisOn) {
      this.logger.warn(
        `Kinesis is disabled or not configured. Please look at the config.yaml!`,
      );
      return;
    }
    await this.handleDataToKinesisEvent(context);
  }

  private shouldStreamData(context: ExecutionContext): boolean {
    const isApiKey: boolean = isApiKeyEndpoint(this.reflector, context);
    const isPublic: boolean = isPublicEndpoint(this.reflector, context);
    return !isPublic && isApiKey;
  }

  private async handleDataToKinesisEvent(context: ExecutionContext) {
    try {
      const organizationId: string | undefined =
        KinesisInterceptor.extractOrganizationIdFromContext(context);
      if (!organizationId) {
        this.logger.error(
          `Organization ID is not apart of the request and is required by the kinesis request logging`,
        );
        return;
      }
      const kinesisRecord: PutRecordInput = this.createKinesisRecord(context);
      await this.kinesis.putRecord(kinesisRecord);
    } catch (e) {
      this.logger.error(
        `Error when putting data to kinesis stream: ${JSON.stringify(e)}`,
      );
    }
  }

  private static extractOrganizationIdFromContext(
    context: ExecutionContext,
  ): string {
    const request: any = context.switchToHttp().getRequest();
    return request?.organizationId;
  }

  private static buildKinesisEndpointStreamData(
    context: ExecutionContext,
  ): KinesisEndpointStreamData {
    const request: Request = context.switchToHttp().getRequest<Request>();
    const headers: Record<string, any> = request.headers ?? {};
    KinesisInterceptor.removeAuthInfoFromHeaders(headers);
    return {
      request: {
        headers,
        method: request.method,
        path: request.url,
        queryParameters: request.query ?? {},
      },
      timestamp: new Date(),
    };
  }

  private static removeAuthInfoFromHeaders(headers: Record<string, any>) {
    if ('authorization' in headers || 'Authorization' in headers) {
      const authKey: string =
        'authorization' in headers ? 'authorization' : 'Authorization';
      delete headers[authKey];
    }
  }

  private createKinesisRecord(context: ExecutionContext): PutRecordInput {
    const partitionKey: string =
      KinesisInterceptor.extractOrganizationIdFromContext(context);
    const data: KinesisEndpointStreamData =
      KinesisInterceptor.buildKinesisEndpointStreamData(context);
    const streamName: string = this.kinesisConfig.streamName;
    const streamArn: string = this.kinesisConfig.streamArn;

    const encoder = new TextEncoder();
    const encodedData = encoder.encode(JSON.stringify(data));
    return {
      Data: encodedData,
      PartitionKey: partitionKey,
      StreamName: streamName,
      StreamARN: streamArn,
    };
  }
}
