import { Module } from '@nestjs/common';
import { KinesisModule } from '@vidmob/vidmob-nestjs-common';
import { ConfigModule } from '@nestjs/config';
import { KinesisInterceptor } from './kinesis/kinesis.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';

@Module({
  imports: [KinesisModule, ConfigModule],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: KinesisInterceptor,
    },
  ],
})
export class InterceptorModule {}
