import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LoginModule } from './login/login.module';
import {
  configuration,
  databaseProvider,
  HealthModule,
  KinesisModule,
  VidmobCommonModule,
  VidmobResponseInterceptorFactory,
} from '@vidmob/vidmob-nestjs-common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { ConfigService } from '@nestjs/config';
import {
  ApiModule as AuthorizationApiModule,
  Configuration as AuthorizationConfiguration,
  ConfigurationParameters as AuthorizationConfigurationParameters,
} from '@vidmob/vidmob-authorization-service-sdk';
import {
  ApiModule as OrganizationApiModule,
  Configuration as OrganizationConfiguration,
  ConfigurationParameters as OrganizationConfigurationParameters,
} from '@vidmob/vidmob-organization-service-sdk';
import {
  ApiModule as ScoringApiModule,
  Configuration as ScoringConfiguration,
  ConfigurationParameters as ScoringConfigurationParameters,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import {
  ApiModule as AnalyticsApiModule,
  Configuration as AnalyticsConfiguration,
  ConfigurationParameters as AnalyticsConfigurationParameters,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  ApiModule as MediaConversionModule,
  Configuration as MediaConversionConfiguration,
  ConfigurationParameters as MediaConversionConfigurationParameters,
} from '@vidmob/vidmob-media-conversion-service-sdk';
import {
  ApiModule as StudioModule,
  Configuration as StudioConfiguration,
  ConfigurationParameters as StudioConfigurationParameters,
} from '@vidmob/vidmob-studio-service-sdk';
import {
  ApiModule as MediaAnnotationApiModule,
  Configuration as MediaAnnotationConfiguration,
  ConfigurationParameters as MediaAnnotationConfigurationParameters,
} from '@vidmob/vidmob-soa-media-annotation-service-sdk';
import {
  API_TAG_NAME,
  DEFAULT_RESPONSE_DESCRIPTION,
  DEFAULT_RESPONSE_ERROR_TYPE,
} from './constants/api.constants';
import { UploadModule } from './upload/upload.module';
import { AuthModule } from './auth/auth.module';
import { PluginModule } from './plugin/plugin.module';
import { GenAiModule } from './genAi/gen-ai.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ThrottlerModule } from '@nestjs/throttler';
import { createApiThrottlerModuleOptions } from './app.module.common';
import { InterceptorModule } from './interceptor/interceptor.module';
import { ScoringModule } from './scoring/scoring.module';
import { MediaModule } from './media/media.module';

@Module({
  imports: [
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) =>
        createApiThrottlerModuleOptions(config),
    }),
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    AutomapperModule.forRoot({ strategyInitializer: classes() }),
    VidmobCommonModule,
    HealthModule,
    KinesisModule,
    InterceptorModule,
    LoginModule,
    PluginModule,
    AuthModule,
    UploadModule,
    GenAiModule,
    TypeOrmModule.forRootAsync(databaseProvider()),
    AuthorizationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<AuthorizationConfigurationParameters>(
            'authorizationService',
          );
        return new AuthorizationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    ScoringApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<ScoringConfigurationParameters>('scoringService');
        return new ScoringConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    AnalyticsApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<AnalyticsConfigurationParameters>(
            'analyticsService',
          );
        return new AnalyticsConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    OrganizationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<OrganizationConfigurationParameters>(
            'organizationService',
          );
        return new OrganizationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    MediaConversionModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<MediaConversionConfigurationParameters>(
            'mediaConversionService',
          );
        return new MediaConversionConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    StudioModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<StudioConfigurationParameters>('studioService');
        return new StudioConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    ScoringModule,
    MediaModule,
    MediaAnnotationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<MediaAnnotationConfigurationParameters>(
            'mediaAnnotationService',
          );
        return new MediaAnnotationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useValue: new VidmobResponseInterceptorFactory().create({
        errorConfiguration: {
          identifierPrefix: API_TAG_NAME,
          serviceSystem: API_TAG_NAME,
          defaultErrorMessage: DEFAULT_RESPONSE_DESCRIPTION,
          defaultErrorType: DEFAULT_RESPONSE_ERROR_TYPE,
        },
      }),
    },
  ],
})
export class AppModule {}
