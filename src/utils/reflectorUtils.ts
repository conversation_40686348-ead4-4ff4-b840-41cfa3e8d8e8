import { ExecutionContext } from '@nestjs/common';
import {
  API_KEY_PARAM_KEY,
  ApiScopeSpecification,
} from '../auth/decorators/api-key.permission.decorator';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../auth/decorators/public.decorator';

export const isApiKeyEndpoint = (
  reflector: Reflector,
  context: ExecutionContext,
): boolean => {
  const scope: ApiScopeSpecification =
    reflector.getAllAndOverride<ApiScopeSpecification>(API_KEY_PARAM_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
  return scope !== null && scope !== undefined;
};

export const isPublicEndpoint = (
  reflector: Reflector,
  context: ExecutionContext,
): boolean => {
  const request: any = context.switchToHttp().getRequest();
  const isPublic: boolean = reflector.getAllAndOverride<boolean>(
    IS_PUBLIC_KEY,
    [context.getHandler(), context.getClass()],
  );
  const isHealth: boolean = /^\/health$/.test(request.url);
  return isPublic || isHealth;
};
