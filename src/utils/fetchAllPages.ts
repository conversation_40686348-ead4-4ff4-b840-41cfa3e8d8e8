import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';

export async function fetchAllPages<T>(
  fetchFunction: (
    paginationOptions: PaginationOptions,
  ) => Promise<PaginatedResultArray<T>>,
  paginationOptions: PaginationOptions,
): Promise<T[]> {
  const allItems: T[] = [];
  let currentOffset = 0;
  let totalSize = 0;

  do {
    const currentPaginationOptions = {
      ...paginationOptions,
      offset: currentOffset,
    };

    const response = await fetchFunction(currentPaginationOptions);

    allItems.push(...response.items);
    totalSize = response.totalCount;
    currentOffset += paginationOptions.perPage;
  } while (currentOffset < totalSize);

  return allItems;
}
