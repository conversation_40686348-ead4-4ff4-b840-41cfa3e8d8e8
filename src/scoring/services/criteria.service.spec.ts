import { Test, TestingModule } from '@nestjs/testing';
import { ScoringCriteriaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { CriteriaService } from './criteria.service';

describe('Non-plugin Criteria Service', () => {
  let service: CriteriaService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CriteriaService,
        { provide: ScoringCriteriaService, useValue: jest.fn() },
      ],
    }).compile();

    service = module.get<CriteriaService>(CriteriaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
