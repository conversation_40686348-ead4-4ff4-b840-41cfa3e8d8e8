import { Test, TestingModule } from '@nestjs/testing';
import { ScoringService } from './scoring.service';
import {
  APIMediaService,
  ScorecardService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';
import { Create201Response1 } from '@vidmob/vidmob-organization-service-sdk/dist/model/create201Response1';
import { GetScorecards200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getScorecards200Response';
import { GetScorecardsResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getScorecardsResponseDto';
import { ScorecardsResponseDto } from '../dto/scorecards-response.dto';

describe('ScoringService', () => {
  let service: ScoringService;

  const workspaceId = 1234;
  const orgId = 'my-org-uuid';
  const workspaceResponse: Create201Response1 = {
    status: 'OK',
    result: {
      id: workspaceId,
      name: 'My test workspace',
      logoUrl: 'https://example.com/logo.png',
      isPrimary: true,
      organizationId: orgId,
    },
  };

  const getApiMediaScoreResponse = {
    status: 'OK',
    result: {
      media: {
        id: 'test-api-media-id',
        extId: 'test-ext-media-id',
        extVersion: '1.0',
        name: 'My Ad Media',
        dateUploaded: '2024-09-03T20:22:03Z',
        type: 'VIDEO',
      },
      summary: {
        FACEBOOK: {
          adherencePercent: 0.5,
          passCount: 1,
          failCount: 0,
          applicableCount: 2,
          notApplicableCount: 0,
          notAvailableCount: 2,
        },
      },
    },
  };

  //FYI - TypeORM is converts BigInt to string by default and we're not setting it back to number
  //in the response. I'm not totally against that as using string means we should be able to switch to UUID
  const internalScorecard = {
    id: '20598',
    name: 'BEROZ20240226A',
    batchType: 'PRE_FLIGHT',
    status: 'COMPLETE',
    criteriaSetId: '3',
    partnerId: '23142',
    partnerAssetFolderId: '23712',
    reasonOutdated: 'CRITERIA_CHANGE',
    dateCreated: '2024-02-27T01:33:25.000Z',
    lastUpdated: '2024-06-07T06:18:56.000Z',
    startDate: null,
    endDate: null,
    score: 48,
    isOutdated: 1,
    isInternal: 0,
    platformAdAccount: null,
    person: {
      id: 50911,
      firstName: 'Sam',
      lastName: 'Beroz',
      email: '<EMAIL>',
    },
    markets: [],
    platforms: [],
    baseReportId: null,
    totalMediaCount: 2,
    brands: [],
    objectives: [],
  } as unknown as GetScorecardsResponseDto;

  const externalScorecard = {
    id: internalScorecard.id,
    name: internalScorecard.name,
    type: internalScorecard.batchType,
    status: internalScorecard.status,
    criteriaSetId: `${internalScorecard.criteriaSetId}`,
    workspaceId: `${internalScorecard.partnerId}`,
    // partnerAssetFolderId: "23712",
    // reasonOutdated: "CRITERIA_CHANGE",
    dateCreated: internalScorecard.dateCreated,
    lastUpdated: internalScorecard.lastUpdated,
    startDate: internalScorecard.startDate,
    endDate: internalScorecard.endDate,
    score: internalScorecard.score,
    // isOutdated: 1,
    // isInternal: 0,
    adAccount: internalScorecard.platformAdAccount,
    createdBy: {
      id: `${internalScorecard.person.id}`,
      firstName: internalScorecard.person.firstName,
      lastName: internalScorecard.person.lastName,
      email: internalScorecard.person.email,
    },
    markets: internalScorecard.markets,
    channels: internalScorecard.platforms,
    // baseReportId: null,
    mediaCount: internalScorecard.totalMediaCount,
    brands: internalScorecard.brands,
    // objectives: [],
  } as unknown as ScorecardsResponseDto;

  const internalResponse: GetScorecards200Response = {
    status: 'OK',
    result: [internalScorecard],
    pagination: {
      offset: 0,
      perPage: 10,
      nextOffset: 0,
      totalSize: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScoringService,
        {
          provide: ScorecardService,
          useValue: {
            getAdAccountFilteredViewAsPromise: () =>
              Promise.resolve(internalResponse),
          },
        },
        {
          provide: WorkspaceService,
          useValue: {
            findOneAsPromise: () => Promise.resolve(workspaceResponse),
          },
        },
        {
          provide: APIMediaService,
          useValue: {
            getMediaScoreDetailsExternalIdAsPromise: () =>
              Promise.resolve(getApiMediaScoreResponse),
          },
        },
      ],
    }).compile();

    service = module.get<ScoringService>(ScoringService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  const basicQueryParamDto = {
    types: 'PRE_FLIGHT',
    startDate: '2024-01-01',
    endDate: '2024-04-01',
  };

  it('get scorecard view happy path test', async () => {
    const result = await service.getScorecardDateFilteredView(
      orgId,
      workspaceId,
      basicQueryParamDto,
      {},
    );
    expect(result.status).toBe('OK');
    expect(result.result).toHaveLength(1);
    expect(result.result[0]).toEqual(externalScorecard);
    expect(result.pagination).toEqual(internalResponse.pagination);
  });

  it('error if workspace is not a match for organization', async () => {
    const differentOrgId = 'A different org UUID';
    await expect(
      service.getScorecardDateFilteredView(
        differentOrgId,
        workspaceId,
        basicQueryParamDto,
        {},
      ),
    ).rejects.toThrow('Invalid credentials for action');
  });

  it('error if requested page too big', async () => {
    await expect(
      service.getScorecardDateFilteredView(
        orgId,
        workspaceId,
        basicQueryParamDto,
        { perPage: 25 },
      ),
    ).rejects.toThrow('Maximum page size (20) exceeded');
  });

  it('getApiMediaScore converts internal response to external', async () => {
    const { media } = getApiMediaScoreResponse.result;
    const expectedMediaResponse = {
      id: media.extId,
      uniqueId: media.id,
      version: media.extVersion,
      name: media.name,
      type: media.type,
    };
    const response = await service.getApiMediaScore(
      orgId,
      'test-ext-media-id',
      {},
    );

    expect(response).toBeDefined();
    expect(response.result).toBeDefined();
    expect(response.result.media).toBeDefined();
    expect(response.result.media).toEqual(expectedMediaResponse);
  });
});
