import { Injectable, Logger } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ScoringCriteriaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ObjectLiteral } from 'typeorm';
import { GetOrgWorkspaceGlobalCriteriaRequestDto } from '../dto/get-org-workspace-global-criteria-request.dto';
import { CriteriaUtils } from '../utils/criteria-utils';

@Injectable()
export class CriteriaService {
  private readonly logger = new Logger(CriteriaService.name);

  constructor(
    private readonly scoringCriteriaService: ScoringCriteriaService,
  ) {}

  public async getAllOrgWorkspaceAndGlobalCriteria(
    organizationId: string,
    getOrgWorkspaceAndGlobalCriteriaRequestDto: GetOrgWorkspaceGlobalCriteriaRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    const { offset, perPage } = paginationOptions;

    const soaResponse =
      await this.scoringCriteriaService.getAllOrgWorkspaceAndGlobalCriteriaAsPromise(
        organizationId,
        CriteriaUtils.mapApiCriteriaRequestDtoToSoaCriteriaRequestDto(
          getOrgWorkspaceAndGlobalCriteriaRequestDto,
        ),
        offset,
        perPage,
      );

    const criteria = soaResponse.result as unknown as ObjectLiteral[];
    soaResponse.result =
      CriteriaUtils.mapSoaCriteriaResponseDtoToApiCriteriaResponseDto(criteria);

    return soaResponse;
  }
}
