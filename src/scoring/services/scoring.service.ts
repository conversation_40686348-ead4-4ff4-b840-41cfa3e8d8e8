import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';
import {
  APIMediaService,
  GetMediaScoreDetailsExternalId200Response,
  GetScorecardsRequestDto,
  GetScorecardsResponseDto,
  PlatformAdAccountDTO,
  ScorecardService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { GetScorecards200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getScorecards200Response';
import { GetScorecardsResponseDtoPerson } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getScorecardsResponseDtoPerson';
import PlatformEnum = PlatformAdAccountDTO.PlatformEnum;
import SortByEnum = GetScorecardsRequestDto.SortByEnum;
import SortOrderEnum = GetScorecardsRequestDto.SortOrderEnum;
import StatusesEnum = GetScorecardsRequestDto.StatusesEnum;
import TypesEnum = GetScorecardsRequestDto.TypesEnum;
import { GetApiMediaScoreQueryParamsDto } from '../dto/get-api-media-score-query-params.dto';
import { GetApiMediaScoreResponseDto } from '../dto/get-api-media-score-response.dto';
import {
  ChannelAdAccountDTO,
  CreatorDTO,
  ScorecardsResponseDto,
} from '../dto/scorecards-response.dto';
import { ScorecardsQueryparamsDto } from '../dto/scorecards-queryparams.dto';

@Injectable()
export class ScoringService {
  private readonly logger = new Logger(ScoringService.name);
  private FILTERED_VIEW_MAX_PAGE_SIZE = 20;

  constructor(
    private readonly apiMediaService: APIMediaService,
    private readonly scorecardService: ScorecardService,
    private readonly workspaceService: WorkspaceService,
  ) {}

  private asStringArray(value: string | undefined): string[] | undefined {
    if (value === undefined) {
      return undefined;
    }
    return value.split(',');
  }

  private convertToScorecardRequestDto(
    queryParamDto: ScorecardsQueryparamsDto,
    paginationOptions: PaginationOptions,
  ): GetScorecardsRequestDto {
    //TODO understand why the generated type is so messed up (fix the scoring SDK)
    //Note sortBy operates on the original scorecard and not the date filtered view
    //The totalMediaCount and score both operate on the original (all time) ad-account scorecard
    const platformInput = queryParamDto.channels as unknown as string;
    return {
      brands: this.asStringArray(queryParamDto.brands),
      creators: this.asStringArray(queryParamDto.creators),
      endDate: queryParamDto.endDate,
      markets: this.asStringArray(queryParamDto.markets),
      platforms: this.asStringArray(platformInput) as unknown as PlatformEnum,
      sortBy: queryParamDto.sortBy
        ? (queryParamDto.sortBy as SortByEnum)
        : undefined,
      sortOrder: queryParamDto.sortOrder
        ? (queryParamDto.sortOrder as SortOrderEnum)
        : undefined,
      startDate: queryParamDto.startDate,
      statuses: this.asStringArray(
        queryParamDto.statuses,
      ) as unknown as StatusesEnum,
      types: this.asStringArray(queryParamDto.types) as unknown as TypesEnum,
      ...paginationOptions,
    };
  }

  public async getScorecardDateFilteredView(
    orgId: string,
    workspaceId: number,
    queryParamDto: ScorecardsQueryparamsDto,
    paginationOptions: PaginationOptions,
    includeScoreDetail = false,
  ) {
    const workspace = await this.workspaceService.findOneAsPromise(workspaceId);
    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }
    if (workspace.result.organizationId !== orgId) {
      throw new UnauthorizedException('Invalid credentials for action');
    }

    if (paginationOptions.perPage > this.FILTERED_VIEW_MAX_PAGE_SIZE) {
      throw new BadRequestException(
        `Maximum page size (${this.FILTERED_VIEW_MAX_PAGE_SIZE}) exceeded`,
      );
    }

    const postRequestDto: GetScorecardsRequestDto =
      this.convertToScorecardRequestDto(queryParamDto, paginationOptions);
    //TODO change the name of the scoring method to getScorecardDateFilteredView
    const internalResponse =
      await this.scorecardService.getAdAccountFilteredViewAsPromise(
        includeScoreDetail,
        workspaceId,
        postRequestDto,
      );
    return this.externalResponseFor(internalResponse);
  }

  async getApiMediaScore(
    organizationId: string,
    mediaId: string,
    getApiMediaScoreQueryParamsDto: GetApiMediaScoreQueryParamsDto,
  ): Promise<GetApiMediaScoreResponseDto> {
    const { channel, source, version, consideration, format } =
      getApiMediaScoreQueryParamsDto;

    const internalResponse =
      await this.apiMediaService.getMediaScoreDetailsExternalIdAsPromise(
        organizationId,
        mediaId,
        channel,
        format,
        source,
        version,
        consideration,
      );

    return this.externalResponseForApiMediaScore(internalResponse);
  }

  private externalResponseForApiMediaScore(
    internalResponse: GetMediaScoreDetailsExternalId200Response,
  ) {
    const { result, status } = internalResponse;
    const media = result.media as {
      extId: string;
      id: string;
      extVersion?: string;
      name?: string;
      type: string;
    };

    return {
      status,
      result: {
        ...result,
        media: {
          id: media.extId,
          uniqueId: media.id,
          version: media?.extVersion,
          name: media?.name,
          type: media?.type,
        },
      },
    };
  }

  private externalResponseFor(internalResponse: GetScorecards200Response) {
    const result = internalResponse.result.map((scorecard) => {
      return this.transformScorecard(scorecard);
    });
    return {
      status: internalResponse.status,
      result,
      pagination: internalResponse.pagination,
    };
  }

  private transformScorecard(
    scorecard: GetScorecardsResponseDto,
  ): ScorecardsResponseDto {
    return {
      id: scorecard.id,
      workspaceId: scorecard.partnerId,
      name: scorecard.name,
      type: scorecard.batchType,
      createdBy: scorecard.isInternal
        ? undefined
        : this.asCreator(scorecard.person),
      criteriaSetId: `${scorecard.criteriaSetId}`,
      status: scorecard.status,
      channels: scorecard.platforms,
      startDate: scorecard.startDate,
      endDate: scorecard.endDate,
      markets: scorecard.markets,
      brands: scorecard.brands,
      adAccount: this.asAdAccount(scorecard.platformAdAccount),
      score: scorecard.score,
      // @ts-ignore
      scoreDetail: scorecard.scoreDetail,
      mediaCount: scorecard.totalMediaCount,
      dateCreated: scorecard.dateCreated,
      lastUpdated: scorecard.lastUpdated,
    };
  }

  private asAdAccount(adAccount: PlatformAdAccountDTO): ChannelAdAccountDTO {
    if (!adAccount) {
      return null;
    }
    return {
      id: `${adAccount.id}`,
      channel: adAccount.platform,
      accountId: adAccount.platformAccountId,
      accountName: adAccount.platformAccountName,
    };
  }

  private asCreator(person: GetScorecardsResponseDtoPerson): CreatorDTO {
    return {
      id: `${person.id}`,
      firstName: person.firstName,
      lastName: person.lastName,
      email: person.email,
    };
  }
}
