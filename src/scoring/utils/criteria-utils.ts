import { ObjectLiteral } from 'typeorm';
import { GetAllOrgWorkspaceGlobalCriteriaRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getAllOrgWorkspaceGlobalCriteriaRequestDto';
import { GetOrgWorkspaceGlobalCriteriaRequestDto } from '../dto/get-org-workspace-global-criteria-request.dto';
import { GetOrgWorkspaceGlobalCriteriaResponseDto } from '../dto/get-org-workspace-global-criteria-response.dto';
import ConsiderationEnum = GetAllOrgWorkspaceGlobalCriteriaRequestDto.ConsiderationEnum;
import { SOA_TO_API_PLATFORM_MAP } from '../constants';

export class CriteriaUtils {
  public static mapApiCriteriaRequestDtoToSoaCriteriaRequestDto(
    getOrgWorkspaceAndGlobalCriteriaRequestDto: GetOrgWorkspaceGlobalCriteriaRequestDto,
  ): GetAllOrgWorkspaceGlobalCriteriaRequestDto {
    const { channels, criteriaGroups, consideration, globalOnly, workspaces } =
      getOrgWorkspaceAndGlobalCriteriaRequestDto;
    return {
      workspaceIds: workspaces,
      criteriaGroupIds: criteriaGroups,
      consideration: consideration as unknown as ConsiderationEnum,
      channels,
      globalOnly,
    };
  }

  public static mapSoaCriteriaResponseDtoToApiCriteriaResponseDto(
    criteria: ObjectLiteral[],
  ): GetOrgWorkspaceGlobalCriteriaResponseDto[] {
    return criteria.map((criterion) => {
      return {
        id: parseInt(criterion.id),
        name: criterion.name,
        rule: criterion.rule,
        bestPractice: !!criterion.isBestPractice,
        category: criterion.criteriaTemplate.category,
        channel: this.mapSoaToApiPlatform(criterion.platformIdentifier),
        consideration: criterion.isOptional
          ? ConsiderationEnum.Optional
          : ConsiderationEnum.Mandatory,
        criteriaGroups: criterion.criteriaGroupCriteriaMap?.map((cgcm) => ({
          id: cgcm.criteriaGroupId,
          name: cgcm.criteriaGroup.name,
        })),
        creativeTypes: criterion.applicabilityMediaTypes,
        dateCreated: criterion.dateCreated,
        organizationCriteria: !!criterion.isGlobal,
        person: criterion.person
          ? {
              email: criterion.person.email,
              firstName: criterion.person.firstName,
              lastName: criterion.person.lastName,
            }
          : null,
        workspace: criterion.criteriaSet.workspace
          ? {
              id: criterion.criteriaSet.workspace.id,
              name: criterion.criteriaSet.workspace.name,
            }
          : null,
      };
    });
  }

  private static mapSoaToApiPlatform(platformIdentifier: string): string {
    const mappedPlatformIdentifier =
      SOA_TO_API_PLATFORM_MAP[platformIdentifier];

    return mappedPlatformIdentifier ?? platformIdentifier;
  }
}
