import {
  Body,
  Controller,
  DefaultV<PERSON>ue<PERSON>ipe,
  Get,
  Logger,
  Param,
  ParseBoolPipe,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { CriteriaService } from '../services/criteria.service';
import { ScoringService } from '../services/scoring.service';
import { ApiKeyScopes } from '../../auth/decorators/api-key.permission.decorator';
import { ScorecardsQueryparamsDto } from '../dto/scorecards-queryparams.dto';
import { GetApiMediaScoreQueryParamsDto } from '../dto/get-api-media-score-query-params.dto';
import { GetApiMediaScoreResponseDto } from '../dto/get-api-media-score-response.dto';
import { GetOrgWorkspaceGlobalCriteriaRequestDto } from '../dto/get-org-workspace-global-criteria-request.dto';
import { CriteriaRequestValidationPipe } from '../pipes/criteria-request-validation.pipe';

@ApiTags('Public API')
@Controller('scoring')
export class ScoringController {
  private readonly logger = new Logger(ScoringController.name);

  constructor(
    private readonly scoringService: ScoringService,
    private readonly criteriaService: CriteriaService,
  ) {}

  /**
   * This endpoint gets a list of scorecards for a given workspace. It supports a series of filters through query params.
   */
  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: 'scoring',
        permission: 'read',
      },
    ],
  })
  @Get('/workspace/:workspaceId/scorecards')
  async getScorecardDateFilteredView(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('workspaceId') workspaceId: number,
    @Query() getScorecardsQueryParamDto: ScorecardsQueryparamsDto,
    @Query('scoreDetail', new DefaultValuePipe(false), ParseBoolPipe)
    scoreDetail: boolean,
    @Request() req: any,
  ) {
    this.logger.debug('Input - getScorecards: ', {
      workspaceId,
      getScorecardsQueryParamDto,
      paginationOptions,
    });

    const organizationId = req.organizationId;
    return await this.scoringService.getScorecardDateFilteredView(
      organizationId,
      workspaceId,
      getScorecardsQueryParamDto,
      paginationOptions,
      scoreDetail,
    );
  }

  /**
   * Get API media scores. API media is external media e.g. media that has been ingested from a DAM.
   * @param mediaId - External (client provided) media ID
   * @param getApiMediaScoreQueryParamsDto - Query parameters for the API media score
   * @param req - Request object
   * @returns API media score
   */
  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: 'scoring',
        permission: 'read',
      },
    ],
  })
  @ApiParam({
    name: 'mediaId',
    type: 'string',
    description: 'The media ID',
  })
  @ApiQuery({
    name: 'channel',
    type: 'string',
    required: false,
    description: 'List of platforms for which to get the media score',
  })
  @ApiQuery({
    name: 'version',
    type: 'string',
    required: false,
    description: 'Specifies the media version',
  })
  @ApiQuery({
    name: 'consideration',
    type: 'string',
    required: false,
    description: 'Specifies the criteria consideration',
  })
  @Get('/media/:mediaId/scores')
  async getApiMediaScore(
    @Param('mediaId') mediaId: string,
    @Query() getApiMediaScoreQueryParamsDto: GetApiMediaScoreQueryParamsDto,
    @Request() req: any,
  ): Promise<GetApiMediaScoreResponseDto> {
    this.logger.debug(
      `scoring/media/:mediaId/scores called with mediaId: ${mediaId}`,
    );

    const organizationId = req.organizationId;
    return await this.scoringService.getApiMediaScore(
      organizationId,
      mediaId,
      getApiMediaScoreQueryParamsDto,
    );
  }

  /**
   * Get criteria from all workspaces in the organization AND the global criteria for that organization.
   * @param getOrgWorkspaceAndGlobalCriteriaRequestDto - DTO object to provide filtering options
   * @param paginationOptions - offset and perPage values for paginated response
   * @param req - Request object
   */
  @UseGuards(ThrottlerGuard)
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: 'scoring',
        permission: 'read',
      },
    ],
  })
  @Post('/criteria/metadata')
  async getAllOrgWorkspaceAndGlobalCriteria(
    @Body(new CriteriaRequestValidationPipe())
    getOrgWorkspaceAndGlobalCriteriaRequestDto: GetOrgWorkspaceGlobalCriteriaRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
  ) {
    this.logger.debug(
      `scoring/criteria/metadata called with payload: ${JSON.stringify(
        getOrgWorkspaceAndGlobalCriteriaRequestDto,
      )}`,
    );

    const organizationId = req.organizationId;
    return await this.criteriaService.getAllOrgWorkspaceAndGlobalCriteria(
      organizationId,
      getOrgWorkspaceAndGlobalCriteriaRequestDto,
      paginationOptions,
    );
  }
}
