import { Test, TestingModule } from '@nestjs/testing';
import { ScoringController } from './scoring.controller';
import { ScoringService } from '../services/scoring.service';
import { ThrottlerModule } from '@nestjs/throttler';
import { CriteriaService } from '../services/criteria.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { GetOrgWorkspaceGlobalCriteriaRequestDto } from '../dto/get-org-workspace-global-criteria-request.dto';

describe('ScoringController', () => {
  let controller: ScoringController;
  let criteriaService: CriteriaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ThrottlerModule.forRoot({
          throttlers: [{ ttl: 60, limit: 10 }],
        }),
      ],
      controllers: [ScoringController],
      providers: [
        {
          provide: ScoringService,
          useValue: {},
        },
        {
          provide: CriteriaService,
          useValue: {
            getAllOrgWorkspaceAndGlobalCriteria: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ScoringController>(ScoringController);
    criteriaService = module.get<CriteriaService>(CriteriaService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return org-wide criteria', async () => {
    const mockOrgId = 'mock-organization-id';
    const getOrgWorkspaceGlobalCriteriaRequestDto: GetOrgWorkspaceGlobalCriteriaRequestDto =
      {
        channels: ['X', 'META'],
        workspaces: [1, 2, 3],
      };
    const paginationOptions: PaginationOptions = { offset: 0, perPage: 1 };
    const expectedResponse = {
      status: 'OK',
      result: [
        {
          id: 1,
          name: 'test name 1',
          rule: 'test rule 1',
          bestPractice: false,
          category: 'Formatting',
          channel: 'DV360',
          consideration: 'MANDATORY',
          criteriaGroups: [
            {
              id: '9af107f0-5921-4253-80b9-298cc0b68d64',
              name: 'Example Group Name',
            },
          ],
          creativeTypes: ['VIDEO', 'ANIMATED_IMAGE'],
          dateCreated: new Date(),
          organizationCriteria: true,
          person: {
            email: '<EMAIL>',
            firstName: 'Criteria',
            lastName: 'Cait',
          },
          workspace: null,
        },
        {
          id: 1,
          name: 'test name 1',
          rule: 'test rule 1',
          bestPractice: false,
          category: 'Formatting',
          channel: 'DV360',
          consideration: 'MANDATORY',
          criteriaGroups: [
            {
              id: '9af107f0-5921-4253-80b9-298cc0b68d64',
              name: 'Example Group Name',
            },
          ],
          creativeTypes: ['VIDEO', 'ANIMATED_IMAGE'],
          dateCreated: new Date(),
          organizationCriteria: true,
          person: {
            email: '<EMAIL>',
            firstName: 'Criteria',
            lastName: 'Cait',
          },
          workspace: null,
        },
      ],
      pagination: {
        offset: 0,
        perPage: 3,
        nextOffset: 3,
        totalSize: 7,
      },
    };

    jest
      .spyOn(criteriaService, 'getAllOrgWorkspaceAndGlobalCriteria')
      .mockResolvedValue(expectedResponse);

    const result = await controller.getAllOrgWorkspaceAndGlobalCriteria(
      getOrgWorkspaceGlobalCriteriaRequestDto,
      paginationOptions,
      { organizationId: mockOrgId },
    );

    expect(
      criteriaService.getAllOrgWorkspaceAndGlobalCriteria,
    ).toHaveBeenCalledWith(
      mockOrgId,
      getOrgWorkspaceGlobalCriteriaRequestDto,
      paginationOptions,
    );

    expect(result).toEqual(expectedResponse);
  });
});
