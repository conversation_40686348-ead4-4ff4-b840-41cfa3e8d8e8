import { ScoreData } from './scorecards-response.dto';

export type MediaDetail = {
  /** UUID of the media.
   * @example 'd7392a8d-3f73-44b8-aad6-10ca5c3f4aa8'
   */
  uniqueId: string;
  /** Media ID.
   * @example '196e0c4f5b2344f0a29eb1d5014f271a'
   */
  id: string;
  /** Media version.
   * @example '1.0'
   */
  version: string | null;
  /** Media name.
   * @example 'My Ad Media'
   */
  name?: string | null;
  /** Date media was uploaded.
   * @example '2024-09-03T20:22:03Z'
   */
  dateUploaded?: Date;
  /** Type of media.
   * @example 'VIDEO'
   */
  type?: string;
};

export type MediaScoreDetailResponse = {
  media?: MediaDetail;
  summary?: {
    [key: string]: ScoreData;
  };
};

export class GetApiMediaScoreResponseDto {
  status: string;
  result: MediaScoreDetailResponse;
}
