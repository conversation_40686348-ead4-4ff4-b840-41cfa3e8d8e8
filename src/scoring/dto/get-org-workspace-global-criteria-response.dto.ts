import { ApiProperty } from '@nestjs/swagger';
import { IsArray } from 'class-validator';

export class GetOrgWorkspaceGlobalCriteriaResponseDto {
  @ApiProperty({
    description: 'Unique criteria ID',
    type: Number,
  })
  id: number;

  @ApiProperty({
    description: 'Criteria name',
    type: String,
  })
  name: string;

  @ApiProperty({
    description: 'Criteria rule',
    type: String,
  })
  rule: string;

  @ApiProperty({
    description: 'Criteria category',
    type: String,
  })
  category: string;

  @ApiProperty({
    description: 'Criteria channel',
    type: String,
    example: 'DV360',
  })
  channel: string;

  @ApiProperty({
    description: 'Criteria consideration',
    type: String,
    example: 'MANDATORY',
  })
  consideration: string;

  @ApiProperty({
    description: 'List of criteria groups this criteria belongs to',
    isArray: true,
  })
  @IsArray()
  criteriaGroups: { id: string; name: string }[];

  @ApiProperty({
    description: 'Types of creative supported by criteria',
    type: String,
    isArray: true,
    example: '["VIDEO", "IMAGE"]',
  })
  @IsArray()
  creativeTypes: string[];

  @ApiProperty({
    description: 'Date when criteria was created',
    type: Date,
  })
  dateCreated: Date;

  @ApiProperty({
    description: 'Indicates if criteria is best practice',
    type: Boolean,
  })
  bestPractice: boolean;

  @ApiProperty({
    description: 'Indicates if criteria is organization-wide global criteria',
    type: Boolean,
  })
  organizationCriteria: boolean;

  @ApiProperty({
    description: 'Criteria added by',
  })
  person: { email: string; firstName: string; lastName: string } | null;

  @ApiProperty({
    description: 'Workspace associated with non-global criteria',
  })
  workspace: { id: number; name: string } | null;
}
