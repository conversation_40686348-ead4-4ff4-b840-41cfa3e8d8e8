import { IsDateString, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ScorecardsQueryparamsDto {
  @IsOptional()
  @IsString()
  sortOrder? = 'DESC';

  @IsOptional()
  @IsString()
  sortBy?: string;

  @IsString()
  types?: string;

  @IsOptional()
  @IsString()
  channels?: string;

  @ApiProperty({
    description: 'Filter scorecards that have a start date greater than this',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'Filter scorecards that have an end date less than this',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description: 'Comma separated list of markets to filter scorecards by',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  markets?: string;

  @ApiProperty({
    description: 'Comma separated list of user ids that created the scorecards',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  creators?: string;

  @IsOptional()
  @IsString()
  statuses?: string;

  @ApiProperty({
    description: 'Search by scorecard name',
    type: String,
    required: false,
  })
  @IsOptional()
  searchText?: string;

  @ApiProperty({
    description: 'Brand IDs to filter on',
    type: String,
    required: false,
  })
  @IsOptional()
  brands?: string;

  @ApiProperty({
    description: 'Ad accounts to filter on',
    type: String,
    required: false,
  })
  @IsOptional()
  channelAdAccounts?: string;
}
