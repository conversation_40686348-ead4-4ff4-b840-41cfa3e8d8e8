import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional } from 'class-validator';

export class GetOrgWorkspaceGlobalCriteriaRequestDto {
  @ApiProperty({
    description: 'Only criteria belonging to list of channels will be included',
    type: String,
    isArray: true,
    required: false,
    example: 'DV360',
  })
  @IsArray()
  @IsOptional()
  channels?: string[];

  @ApiProperty({
    description:
      'Only workspace criteria from provided list of IDs will be included',
    type: Number,
    isArray: true,
    required: false,
  })
  @IsArray()
  @IsOptional()
  workspaces?: number[];

  @ApiProperty({
    description:
      'Only criteria from provided list of group IDs will be included',
    type: String,
    isArray: true,
    required: false,
    example: '2ba3e8f2-44da-4efa-8f59-3bb287ab8f3d',
  })
  @IsArray()
  @IsOptional()
  criteriaGroups?: string[];

  @ApiProperty({
    description: 'Provides a way to filter criteria by consideration',
    type: String,
    required: false,
    example: 'OPTIONAL',
  })
  @IsOptional()
  consideration?: string;

  @ApiProperty({
    description: 'Flag to only include global criteria in response',
    type: Boolean,
    required: false,
    example: false,
  })
  @IsOptional()
  globalOnly?: boolean;
}
