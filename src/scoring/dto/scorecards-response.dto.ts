export class LocationDTO {
  /** ISO code for the country of the location.
   * @example 'usa'
   */
  isoCode: string;

  /** Name of the country of the location.
   * @example 'United States' */
  name: string;
}

export class BrandDTO {
  /** UUID of the brand.
   * @example 'd7392a8d-3f73-44b8-aad6-10ca5c3f4aa8'
   */
  id: string;

  /** Name of the brand.
   * @example 'My Brand' */
  name: string;
}

// Platform Ad Account Data Transfer Object
export class ChannelAdAccountDTO {
  /** System assigned Id of the platform ad account.
   * @example 12
   * */
  id: string;
  /** Platform of the ad account.
   * @example 'FACEBOOK'
   * */
  channel: string; //the renamed platform
  /** Account ID of the platform ad account.
   * @example '*********'
   * */
  accountId: string;
  /** Name of the platform ad account.
   * @example 'Ad Account 1'
   * */
  accountName: string;
}

// Person Data Transfer Object
export class CreatorDTO {
  /** Unique identifier for the person.
   * @example 21541
   * */
  id: string;

  /** First name of the person.
   * @example 'John'
   * */
  firstName: string;

  /** Last name of the person.
   * @example 'Doe'
   * */
  lastName: string;

  /** Email address of the person.
   * @example '<EMAIL>'
   * */
  email: string;
}

export class ScoreData {
  adherencePercent: number;
  passCount: number;
  failCount: number;
  applicableCount: number;
  notApplicableCount: number;
  notAvailableCount: number;
}

class CriteriaSummary {
  identifier: string;
  parameters: object;
}

class Criteria {
  criteriaSummary: CriteriaSummary;
  scoreData: ScoreData;
}

class Channel {
  channel: string;
  scoreData: ScoreData;
  criteria: Criteria[];
}

export class ScoreDetailDTO {
  scoreData: ScoreData;
  channels: Channel[];
}

export class ScorecardsResponseDto {
  /** System assigned identifier for the scorecard.
   * @example '1251'
   */
  id: string;

  /** Name of the scorecard.
   * @example 'Scorecard 1'
   * */
  name: string;

  /** Type of the scorecard.
   * @example 'IN_FLIGHT'
   */
  type: string; //renamed batchType

  /** Status of the scorecard.
   *  @example 'PROCESSING'
   */
  status: string;

  /** Unique identifier for the criteria set.
   * @example '789'
   * */
  criteriaSetId: string;

  /**
   * Workspace id of the scorecard.
   */
  workspaceId: number;

  /** Platforms associated with the scorecard.
   * @example ['FACEBOOK','TWITTER']
   * */
  channels: string[]; //renamed platforms

  /** Reason for the scorecard being outdated.
   * @example 'Criteria Change'
   * */
  // reasonOutdated: string; //This field deliberately omitted from external API

  /** Date when the scorecard was created.
   * @example '2023-10-05T08:00:00Z'
   * */
  dateCreated: string;

  /** Date when the scorecard was last updated.
   * @example '2023-10-05T08:30:00Z'
   * */
  lastUpdated: string;

  /** Start date of the scorecard.
   * @example '2023-09-01T00:00:00Z'
   * */
  startDate?: string;

  /** End date of the scorecard.
   * @example '2023-09-30T23:59:59Z'
   * */
  endDate?: string;
  /** Score associated with the scorecard.
   * @example 85
   * */
  score: number;

  /** An object representing the raw score details.
   * */
  scoreDetail?: ScoreDetailDTO;

  /** Markets where scorecards is applicable.
   * */
  markets: LocationDTO[];

  /**
   * Platform ad account associated with the scorecard.
   * */
  adAccount?: ChannelAdAccountDTO; //renamed platformAdAccount

  /** Person associated with the scorecard, as a PersonDTO object. */
  createdBy?: CreatorDTO; //renamed person

  /** Indicates whether the scorecard is outdated.
   * @example 1
   * */
  // isOutdated: boolean; //This field deliberately omitted from external API

  /** Brands tied to scorecard.
   * @example ["d739278d-3673-44a8-aa26-10cadc3f4aa8", "0bddbd01-792a-462f-b5d8-5502ce220abc"]
   * */
  brands: BrandDTO[];

  mediaCount: number; //renamed totalMediaCount
}
