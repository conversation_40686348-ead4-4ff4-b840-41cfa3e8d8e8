import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class GetApiMediaScoreQueryParamsDto {
  @ApiProperty({
    description: 'List of platforms for which to get the media score',
    type: String,
    required: false,
    example: 'FACEBOOK,DV360',
  })
  @IsOptional()
  @IsString()
  channel?: string;

  @ApiProperty({
    description: 'Specifies the media source',
    type: String,
    required: false,
    example: 'client-dam-name',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({
    description: 'Specifies the media version',
    type: String,
    required: false,
    example: '2',
  })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiProperty({
    description: 'Specifies the criteria consideration',
    type: String,
    required: false,
    example: 'MANDATORY,OPTIONAL',
  })
  @IsOptional()
  @IsString()
  consideration?: string;

  @ApiProperty({
    description: 'Specifies response format',
    type: String,
    required: false,
    example: 'summary,detail',
  })
  @IsOptional()
  @IsString()
  format?: string;
}
