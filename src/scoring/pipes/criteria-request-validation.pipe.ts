import { PipeTransform } from '@nestjs/common';
import { GetOrgWorkspaceGlobalCriteriaRequestDto } from '../dto/get-org-workspace-global-criteria-request.dto';
import { API_TO_SOA_PLATFORM_MAP } from '../constants';

export class CriteriaRequestValidationPipe implements PipeTransform {
  transform(
    getOrgWorkspaceAndGlobalCriteriaRequestDto: GetOrgWorkspaceGlobalCriteriaRequestDto,
  ): any {
    const { channels } = getOrgWorkspaceAndGlobalCriteriaRequestDto;

    if (channels) {
      const internalChannels = channels.map(
        (channel) => API_TO_SOA_PLATFORM_MAP[channel] ?? channel,
      );

      return {
        ...getOrgWorkspaceAndGlobalCriteriaRequestDto,
        channels: internalChannels,
      };
    }

    return getOrgWorkspaceAndGlobalCriteriaRequestDto;
  }
}
