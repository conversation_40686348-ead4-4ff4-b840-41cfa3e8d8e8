import { ApiKeyScopes } from '../decorators/api-key.permission.decorator';
import { Public } from '../decorators/public.decorator';
import { ExecutionContext } from '@nestjs/common';
import { ApiTokenScopeDto } from '@vidmob/vidmob-authorization-service-sdk/dist/model/apiTokenScopeDto';
import { createHash } from 'crypto';
import { DigestionEncodings, HashAlgorithms } from './api-key.guard.common';

class TestController {
  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: ApiTokenScopeDto.ScopeEnum.Platform,
        permission: ApiTokenScopeDto.PermissionEnum.ReadWrite,
      },
    ],
  })
  getApiKeyPlatformEndpoint() {
    return 'Api Key Endpoint!';
  }

  @ApiKeyScopes({
    scopePermissions: [
      {
        scope: ApiTokenScopeDto.ScopeEnum.Scoring,
        permission: ApiTokenScopeDto.PermissionEnum.ReadWrite,
      },
    ],
  })
  getApiKeyScoringEndpoint() {
    return 'Api Key Endpoint!';
  }

  @ApiKeyScopes({
    scopePermissions: [],
  })
  getNoScopeApiKeyEndpoint() {
    return 'Api Key Endpoint!';
  }

  @ApiKeyScopes({
    scopePermissions: [],
  })
  getErrorApiKeyEndpoint() {
    return 'Api Key Endpoint!';
  }

  @Public()
  getPublic() {
    return 'Public Endpoint!';
  }
}

export const generateContext = (
  method: string,
  url: string,
): ExecutionContext => {
  const request =
    url === '/v1/me/cached'
      ? {
          headers: {
            authorization: 'Bearer test-api-token-2',
          },
          url,
        }
      : {
          headers: {
            authorization: 'Bearer test-api-token',
          },
          url,
        };
  const controller = new TestController();

  // get the function on controller from the method string
  const handler = Reflect.get(controller, method);
  return {
    getClass: () => TestController,
    getHandler: () => handler,
    switchToHttp: () => ({ getRequest: () => request }),
  } as ExecutionContext;
};

const encryptFunction = (key: string) =>
  createHash(HashAlgorithms.SHA256).update(key).digest(DigestionEncodings.HEX);

export const MOCK_CACHE = {
  [encryptFunction(
    'bff_api_key_id_test-api-token-2_scopes_scoring:read_write',
  )]: {
    organizationId: 'test-organization-id-1',
    authorized: true,
  },
};
