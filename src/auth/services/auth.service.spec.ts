import { Test } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { AuthorizationService } from '@vidmob/vidmob-authorization-service-sdk';

class AuthorizationServiceMock {}

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: AuthorizationService, useClass: AuthorizationServiceMock },
      ],
    }).compile();

    authService = moduleRef.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(authService).toBeDefined();
  });
});
