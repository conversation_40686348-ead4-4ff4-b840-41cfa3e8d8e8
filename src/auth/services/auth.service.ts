// authorization.service.ts
import { Injectable } from '@nestjs/common';
import { AxiosResponse } from 'axios';
import { Observable } from 'rxjs';
import { firstValueFrom } from 'rxjs';
import {
  AuthorizationService,
  CanAccess200Response,
  StatementPermissionDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { PermissionDomain } from '../enums/permission.domain.enum';

@Injectable()
export class AuthService {
  constructor(private readonly authorizationService: AuthorizationService) {}

  async isResourcePermissionValid(
    domain: PermissionDomain,
    domainId: number | string,
    authorization: string,
    checkAccess: StatementPermissionDto[],
  ): Promise<boolean> {
    try {
      const responseObservable: Observable<
        AxiosResponse<CanAccess200Response>
      > = this.authorizationService.canAccess({
        domain,
        domainId,
        authorization,
        checkAccess,
      });

      const response: AxiosResponse = await firstValue<PERSON>rom(responseObservable);
      return response?.data?.result?.canAccess;
    } catch (e) {
      return false;
    }
  }
}
