import { Modu<PERSON> } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { AuthPermissionsGuard } from './guards/auth.permission.guard';
import { AuthTokenGuard } from './guards/auth.token.guard';
import { AuthorityGuard } from './guards/auth.authority.guard';
import { ApiKeyGuard } from './guards/api-key.guard';
import { VidmobCacheModule } from '@vidmob/vidmob-nestjs-common';

@Module({
  imports: [VidmobCacheModule],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthTokenGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthPermissionsGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthorityGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ApiKeyGuard,
    },
  ],
})
export class AuthModule {}
