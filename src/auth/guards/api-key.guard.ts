import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Reflector } from '@nestjs/core';
import {
  ApiKeyService,
  ApiTokenAuthorizationRequestDto,
  ApiTokenAuthorizationResponseDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { ApiTokenScopeDto } from '@vidmob/vidmob-authorization-service-sdk/dist/model/apiTokenScopeDto';
import {
  API_KEY_PARAM_KEY,
  ApiScopeSpecification,
  RequiredApiScopePermissions,
} from '../decorators/api-key.permission.decorator';
import { Authorize201Response1 } from '@vidmob/vidmob-authorization-service-sdk/dist/model/authorize201Response1';
import {
  DigestionEncodings,
  HashAlgorithms,
  HTTP_4XX_ERROR_STATUS_CODES,
} from '../commons/api-key.guard.common';
import { tracer } from 'dd-trace';
import { Cache } from 'cache-manager';
import { createHash } from 'crypto';
import { ApiKeyTtsEnum } from '../enums/api-key.tts.enum';
import { isApiKeyEndpoint, isPublicEndpoint } from '../../utils/reflectorUtils';
import {
  Create201Response,
  OrganizationService,
} from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyGuard.name);
  private readonly API_KEY_GUARD_PERMISSION_TTL: number =
    ApiKeyTtsEnum.TEN_MINUTES_IN_MS;
  private readonly CACHE_PREFIX = 'bff_api_key_id';

  constructor(
    private readonly reflector: Reflector,
    private readonly apiKeyService: ApiKeyService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Check if the request from the user can access APIKey endpoints.
   * If the request is public, it will return true. Otherwise, it will
   * attempt to authorize the request, and if successful, attach the organization id
   * to the request and trace.
   *
   * @param context
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic: boolean = isPublicEndpoint(this.reflector, context);
    const isApiKey: boolean = isApiKeyEndpoint(this.reflector, context);
    if (isPublic || !isApiKey) {
      return true;
    }
    return this.authorizeRequest(context);
  }

  /**
   * Calls the Api Key Authorization Service to authorize a users request.
   * If the service response with a successful 200 message, the organization
   * id will be attached to the request. Otherwise, it checks if the error
   * response was a 4xx error or not.
   *
   * @param context
   */
  async authorizeRequest(context: ExecutionContext): Promise<boolean> {
    try {
      this.logger.debug('Authorizing request.');
      return await this.authorizeRequestFromContext(
        context,
        this.organizationService,
      );
    } catch (err) {
      if (err instanceof HttpException && ApiKeyGuard.is4XXResponse(err)) {
        const response = err.getResponse();
        this.logger.warn(
          `400 Error occurred when authorizing: ${JSON.stringify(response)}`,
        );
        ApiKeyGuard.handle4XXErrors(err);
      }
      this.logger.warn(
        `Error occurred when authorizing: ${JSON.stringify(err)}`,
      );
      throw err;
    }
  }

  /**
   * Authorizes a request from the cache or the authorization service.
   * Uses the context to get the request and response objects.
   * If the response is not in the cache, it will fetch it from the
   * authorization service and save it to the cache.
   *
   * @param context
   * @private
   */
  private async authorizeRequestFromContext(
    context: ExecutionContext,
    organizationService: OrganizationService,
  ) {
    const cacheKey = this.buildCacheKey(context);
    const response: ApiTokenAuthorizationResponseDto =
      await this.cacheManager.wrap<ApiTokenAuthorizationResponseDto>(
        cacheKey,
        async () => await this.fetchAuthResFromAuthService(context),
        this.API_KEY_GUARD_PERMISSION_TTL,
      );
    return ApiKeyGuard.handleCachedAuthorization(
      context,
      response,
      organizationService,
    );
  }

  /**
   * Handle cached authorization response by attaching the organization id
   * to the request and the trace info. Only use this method if the response
   * is cached.
   *
   * @param context
   * @param response
   * @private
   */
  private static async handleCachedAuthorization(
    context: ExecutionContext,
    response: ApiTokenAuthorizationResponseDto,
    organizationService: OrganizationService,
  ): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    ApiKeyGuard.attachOrganizationIdToRequest(request, response);
    ApiKeyGuard.attachTraceInfo(response, organizationService);
    return response.authorized;
  }

  /**
   * Attaches the organization id to the user's request.
   *
   * @param request
   * @param result
   * @private
   */
  private static attachOrganizationIdToRequest(
    request: any,
    result: ApiTokenAuthorizationResponseDto,
  ) {
    request['organizationId'] = result.organizationId;
    request['organizationApiKeyId'] = result.organizationApiKeyId;
  }

  /**
   * Extracts the API Key scopes from the clients request.
   * If no scopes are found, it will return an empty array.
   *
   * @param context
   * @private
   */

  private extractApiKeyScopesFromEndpointHandler(
    context: ExecutionContext,
  ): ApiTokenScopeDto[] {
    const apiScopeSpecification = this.reflector.get<ApiScopeSpecification>(
      API_KEY_PARAM_KEY,
      context.getHandler(),
    );
    const requestedScopePermissions: RequiredApiScopePermissions[] =
      apiScopeSpecification?.scopePermissions;
    if (requestedScopePermissions) {
      return apiScopeSpecification.scopePermissions.map((s) =>
        ApiKeyGuard.buildApiTokenAuthRequestFromScopePermission(s),
      );
    }
    return [];
  }

  /**
   * Builds an ApiTokenScopeDto from the RequiredApiScopePermissions.
   *
   * @param context
   * @private
   */
  createApiTokenAuthorizationRequestDto(
    context: ExecutionContext,
  ): ApiTokenAuthorizationRequestDto {
    const request = context.switchToHttp().getRequest();
    const apiKey: string = ApiKeyGuard.extractApiKeyFromRequest(request);
    const handlerScopes: ApiTokenScopeDto[] =
      this.extractApiKeyScopesFromEndpointHandler(context);
    return { apiKey, scopes: handlerScopes };
  }

  /**
   * Extracts the API Key from the request.
   *
   * @param request
   * @private
   */
  private static extractApiKeyFromRequest(request: any): string {
    const bearerApiKey: string = request.headers?.authorization;
    if (bearerApiKey === undefined) {
      throw new UnauthorizedException(
        'Api token attached to header is not correctly formatted. Check if the key in the header is "authorization"',
      );
    }
    const bearerApiKeyParse: string[] = bearerApiKey.split('Bearer ');
    if (bearerApiKeyParse.length > 1 && bearerApiKeyParse[1].length > 0) {
      return bearerApiKeyParse[1];
    }
    throw new UnauthorizedException(
      'Api token attached to header is not correctly formatted. Use "Bearer " in the beginning',
    );
  }

  /**
   * Checks if an error response is a client side error.
   * Used when checking auth response from the authorization service.
   *
   * @param err
   * @private
   */
  private static is4XXResponse(err: HttpException) {
    const response: string | object = err.getResponse();
    const status: number = err.getStatus();
    return response && HTTP_4XX_ERROR_STATUS_CODES.includes(status);
  }
  /**
   * Handles 4XXErrors by throwing the appropriate exception.
   * @param err
   * @private
   */
  private static handle4XXErrors(err: HttpException) {
    if (err.getStatus() === HttpStatus.UNAUTHORIZED) {
      throw new UnauthorizedException('Request is Unauthorized');
    } else if (err.getStatus() === HttpStatus.FORBIDDEN) {
      throw new ForbiddenException('Request is Forbidden');
    }
    throw err;
  }
  /**
   * Builds Api Token Auth Request Dto from a user request scopes and permissions.
   * It checks the request scopes and permissions from the Enums associated to the
   * ApiTokenScopeDto namespace.
   *
   * @param scopePermission
   * @private
   */
  private static buildApiTokenAuthRequestFromScopePermission(
    scopePermission: RequiredApiScopePermissions,
  ) {
    try {
      const scope: ApiTokenScopeDto.ScopeEnum = scopePermission.scope;
      const permission: ApiTokenScopeDto.PermissionEnum =
        scopePermission.permission;
      return { scope, permission };
    } catch (e) {
      throw Error(`Scope Permissions from user request is invalid.`);
    }
  }

  /**
   * Attaches trace info to the response for Datadog.
   *
   * @param result
   * @private
   */
  private static async attachTraceInfo(
    result: ApiTokenAuthorizationResponseDto,
    organizationService: OrganizationService,
  ) {
    try {
      const span = tracer.scope()?.active();
      if (!span) {
        console.log('No active span found for DD');
        return;
      }
      const rootSpan = (span.context() as any)._trace.started[0];
      if (span && rootSpan) {
        const response: Create201Response =
          await organizationService.findOneAsPromise(result.organizationId);
        rootSpan.setTag('organization.id', result.organizationId);
        rootSpan.setTag('organization.name', response?.result?.name || '');
      }
    } catch (error: Error | unknown) {
      if (error instanceof Error)
        console.error(`Error attaching trace info: ${error.message}`);
    }
  }

  /**
   * Fetches the authorization response from the authorization service.
   * If the response is a 4XX error, it will throw an UnauthorizedException.
   * @param context
   * @private
   */
  private async fetchAuthResFromAuthService(
    context: ExecutionContext,
  ): Promise<ApiTokenAuthorizationResponseDto> {
    const apiTokenAuthorizationRequestDto: ApiTokenAuthorizationRequestDto =
      this.createApiTokenAuthorizationRequestDto(context);
    const authorizationResponse: Authorize201Response1 =
      await this.apiKeyService.authorizeAsPromise(
        apiTokenAuthorizationRequestDto,
      );
    return authorizationResponse.result;
  }

  /**
   * Builds Cache Key for an authorization response.
   * This will be the key to the cache response.
   * @param context
   * @private
   */
  private buildCacheKey(context: ExecutionContext): string {
    const request = context.switchToHttp().getRequest();
    const apiKey: string = ApiKeyGuard.extractApiKeyFromRequest(request);
    const handlerScopes: ApiTokenScopeDto[] =
      this.extractApiKeyScopesFromEndpointHandler(context);
    const unencryptedKey = this.createUnencryptedApiCacheKey(
      apiKey,
      handlerScopes,
    );
    return createHash(HashAlgorithms.SHA256)
      .update(unencryptedKey)
      .digest(DigestionEncodings.HEX);
  }

  private createUnencryptedApiCacheKey(
    apiKey: string,
    apiScopesPermissions: ApiTokenScopeDto[],
  ): string {
    const scopes: string[] = apiScopesPermissions.map(
      (s) => `${s.scope}:${s.permission}`,
    );
    const scopeString: string = scopes.join('_');
    return `${this.CACHE_PREFIX}_${apiKey}_scopes_${scopeString}`;
  }
}
