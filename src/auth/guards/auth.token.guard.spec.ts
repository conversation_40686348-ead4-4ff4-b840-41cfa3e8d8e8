import { Test, TestingModule } from '@nestjs/testing';
import { AuthorizationService } from '@vidmob/vidmob-authorization-service-sdk';
import { Public } from '../decorators/public.decorator';
import { AuthTokenGuard } from './auth.token.guard';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

class TestController {
  getPrivate() {
    return 'Private Endpoint!';
  }
  @Public()
  getPublic() {
    return 'Public Endpoint!';
  }
}

const generateContext = (method: string, url: string) => {
  const request = {
    headers: {},
    url,
  };
  const controller = new TestController();

  // get the function on controller from the method string
  const handler = Reflect.get(controller, method);

  return {
    getClass: () => TestController,
    getHandler: () => handler,
    switchToHttp: () => ({ getRequest: () => request }),
  } as any;
};

describe('auth.guard', () => {
  let service: AuthTokenGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthTokenGuard,
        { provide: CACHE_MANAGER, useValue: {} },
        {
          provide: AuthorizationService,
          useValue: {
            authorizationService: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthTokenGuard>(AuthTokenGuard);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it.each([
    ['/', false],
    ['/v1/hello', false],
    ['/v1/healthy', false],
    ['/v1/campaign/health', false],
    ['/health', true],
    ['/v32/health', false],
  ])('should detect health check', (url, expected) => {
    expect(service.isHealthRequest({ url })).toBe(expected);
  });

  it('should treat health check as public', () => {
    expect(
      service.isPublic(generateContext('getPrivate', '/v1/health')),
    ).toBeFalsy();
  });

  it('should treat non-health check as private', () => {
    expect(
      service.isPublic(generateContext('getPrivate', '/v1/hello')),
    ).toBeFalsy();
  });

  it('should treat public decorated method as public', () => {
    expect(
      service.isPublic(generateContext('getPublic', '/v1/hello')),
    ).toBeTruthy();
  });
});
