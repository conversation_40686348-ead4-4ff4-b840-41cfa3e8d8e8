import {
  CanActivate,
  ExecutionContext,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { DECORATOR_METADATA_AUTHORITIES } from '../../constants/configuration.constants';

@Injectable()
export class AuthorityGuard implements CanActivate {
  constructor(private reflector: Reflector) {}
  private readonly logger = new Logger(AuthorityGuard.name);

  canActivate(context: ExecutionContext): boolean {
    let allowedAuthorities;
    const userAuthorities = context.switchToHttp().getRequest().authorities;
    const userName = context.switchToHttp().getRequest().username;

    try {
      allowedAuthorities = this.reflector.get<string[]>(
        DECORATOR_METADATA_AUTHORITIES,
        context.getHandler(),
      );
      if (!allowedAuthorities || allowedAuthorities.length === 0) {
        return true;
      }

      if (!userAuthorities || userAuthorities.length === 0) {
        this.logger.debug(`No authorities found for user - ${userName}`);
        return false;
      }
    } catch (e) {
      const errorMessage =
        'Error while checking authorities for user : ' + e.message;
      this.logger.error(errorMessage);
      throw new InternalServerErrorException(errorMessage);
    }

    return this.isUserAuthorized(userAuthorities, allowedAuthorities);
  }

  private isUserAuthorized(
    userAuthorities: string[],
    allowedAuthorities: string[],
  ): boolean {
    return allowedAuthorities.some((authority) =>
      userAuthorities.includes(authority),
    );
  }
}
