import { Test, TestingModule } from '@nestjs/testing';
import { ApiKeyGuard } from './api-key.guard';
import {
  ApiKeyService,
  ApiTokenAuthorizationRequestDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import {
  generateContext,
  MOCK_CACHE,
} from '../commons/api-key.guard.spec.common';
import { ApiTokenAuthorizationResponseDto } from '@vidmob/vidmob-authorization-service-sdk/dist/model/apiTokenAuthorizationResponseDto';

import { ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { OrganizationService } from '@vidmob/vidmob-organization-service-sdk';

// TESTS
describe('PublicApiGuardsService', () => {
  let guard: ApiKeyGuard;
  let apiKeyService: ApiKeyService;
  let cacheManager: Cache;
  let organizationService: OrganizationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApiKeyGuard,
        {
          provide: CACHE_MANAGER,
          useValue: {
            wrap: jest.fn().mockImplementation((key: string) =>
              Promise.resolve(
                MOCK_CACHE[key]
                  ? MOCK_CACHE[key]
                  : {
                      organizationId: 'test-organization-id',
                      authorized: true,
                    },
              ),
            ),
          },
        },
        {
          provide: ApiKeyService,
          useValue: {
            authorizeAsPromise: jest.fn(),
          },
        },
        {
          provide: OrganizationService,
          useValue: {
            findOneAsPromise: jest.fn().mockResolvedValue({
              result: { id: 'test-organization-id', name: 'Test Org' },
            }),
          },
        },
      ],
    }).compile();

    guard = module.get<ApiKeyGuard>(ApiKeyGuard);
    apiKeyService = module.get<ApiKeyService>(ApiKeyService);
    cacheManager = module.get<Cache>(CACHE_MANAGER);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('createApiTokenAuthorizationRequestDto', () => {
    it.each([
      [
        'getApiKeyScoringEndpoint',
        '/v1/me/',
        {
          apiKey: 'test-api-token',
          scopes: [
            {
              scope: 'scoring',
              permission: 'read_write',
            },
          ],
        } as ApiTokenAuthorizationRequestDto,
      ],
      [
        'getNoScopeApiKeyEndpoint',
        '/v1/me/',
        {
          apiKey: 'test-api-token',
          scopes: [],
        } as ApiTokenAuthorizationRequestDto,
      ],
    ])(
      'should create a ApiTokenAuthorizationRequestDto for user context',
      (
        method: string,
        url: string,
        expected: ApiTokenAuthorizationRequestDto,
      ) => {
        const mockContext = generateContext(method, url);
        const authorizationRequestDto: ApiTokenAuthorizationRequestDto =
          guard.createApiTokenAuthorizationRequestDto(mockContext);
        expect(authorizationRequestDto).toBeDefined();
        expect(authorizationRequestDto).toEqual(expected);
      },
    );
  });

  describe('canActivate', () => {
    it.each([
      [
        'getApiKeyPlatformEndpoint',
        '/v1/me/',
        {
          organizationId: 'test-organization-id',
          authorized: true,
        },
      ],
      [
        'getApiKeyScoringEndpoint',
        '/v1/me/cached',
        {
          organizationId: 'test-organization-id-1',
          authorized: true,
        },
      ],
    ])(
      'should handle successful authorization',
      async (
        method: string,
        url: string,
        result: ApiTokenAuthorizationResponseDto,
      ) => {
        jest
          .spyOn(apiKeyService, 'authorizeAsPromise')
          .mockResolvedValueOnce({ status: 'OK', result });
        const mockContext = generateContext(method, url);
        const canActivate: boolean = (await guard.canActivate(
          mockContext,
        )) as boolean;
        expect(canActivate).toBe(result.authorized);
        const request = mockContext.switchToHttp().getRequest();
        expect(request.organizationId).toBe(result.organizationId);
        expect(cacheManager.wrap).toHaveBeenCalled();
      },
    );
    it.each([
      [
        'getErrorApiKeyEndpoint',
        '/v1/me/',
        new ForbiddenException('Request is Forbidden'),
      ],
      [
        'getErrorApiKeyEndpoint',
        '/v1/me/',
        new UnauthorizedException('Request is Unauthorized'),
      ],
    ])(
      'should handle 4XX errors',
      async (
        method: string,
        url: string,
        error: ForbiddenException | UnauthorizedException,
      ) => {
        jest.spyOn(cacheManager, 'wrap').mockRejectedValueOnce(error);
        const mockContext = generateContext(method, url);
        await expect(guard.canActivate(mockContext)).rejects.toThrowError(
          error,
        );
      },
    );
  });
});
