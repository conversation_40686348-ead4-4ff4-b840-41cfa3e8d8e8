import { SetMetadata, applyDecorators } from '@nestjs/common';
import { ApiSecurity } from '@nestjs/swagger';
import { ApiTokenScopeDto } from '@vidmob/vidmob-authorization-service-sdk/dist/model/apiTokenScopeDto';

export const API_KEY_PARAM_KEY = 'ApiKeyDecoratorKey';

/**
 * Decorator to specify the required permission for an endpoint.
 */
export class RequiredApiScopePermissions {
  scope: ApiTokenScopeDto.ScopeEnum;
  permission: ApiTokenScopeDto.PermissionEnum;
}

export type ApiScopeSpecification = {
  scopePermissions: RequiredApiScopePermissions[];
};

export const ApiKeyScopes = (scopes: ApiScopeSpecification) =>
  applyDecorators(
    SetMetadata(API_KEY_PARAM_KEY, scopes),
    ApiSecurity('Bearer Token'),
  );
