import {
  BadRequestException,
  ExecutionContext,
  SetMetadata,
} from '@nestjs/common';
import { PermissionAction } from '../enums/permission.action.enum';
import { PermissionDomain } from '../enums/permission.domain.enum';
import { PermissionSubResource } from '../enums/permission.subresource.enum';

export const PERMISSION_PARAM_KEY = 'PermissionsDecoratorKey';

export class RequiredPermission {
  action: PermissionAction;
  subresource: PermissionSubResource;
}

export type PermissionSpecification = {
  domain: PermissionDomain;
  domainContextHandler: (context: ExecutionContext) => number | string;
  required: RequiredPermission[];
};

export const Permissions = (permissions: PermissionSpecification) =>
  SetMetadata(PERMISSION_PARAM_KEY, permissions);

//REQUEST HANDLERS PARTNER AS CALLED AS WORKSPACE
export function workspaceFromParamsHandler(context: ExecutionContext): number {
  const request = context.switchToHttp().getRequest();
  if (request?.params?.workspaceId) {
    return Number.parseInt(request.params.workspaceId);
  }
  throw new BadRequestException(
    'Workspace Id not provided. Check your request or permission decorator.',
  );
}

export function partnerFromParamsHandler(context: ExecutionContext): number {
  const request = context.switchToHttp().getRequest();
  const partnerId =
    request?.params?.partnerId ||
    request?.params?.workspaceId ||
    request?.query?.workspaceId ||
    request?.query?.partnerId;
  if (partnerId) {
    return Number.parseInt(partnerId);
  }
  throw new BadRequestException(
    'Partner Id not provided. Check your request or permission decorator.',
  );
}

export function partnerFromBodyHandler(context: ExecutionContext): number {
  const request = context.switchToHttp().getRequest();
  const { partnerId } = request.body;

  let extractedId;
  if (request?.body?.workspaceId) {
    extractedId = Number.parseInt(request.body.workspaceId);
  } else if (partnerId) {
    extractedId = Number.parseInt(partnerId);
  }

  if (extractedId === undefined || isNaN(extractedId)) {
    throw new BadRequestException(
      'Partner Id not provided. Check your request or permission decorator.',
    );
  }

  return extractedId;
}

export function partnerFromQueryHandler(context: ExecutionContext): number {
  const request = context.switchToHttp().getRequest();
  if (request?.query?.partnerId) {
    return Number.parseInt(request.query.partnerId);
  }
  throw new BadRequestException(
    'Partner Id not provided. Check your request or permission decorator.',
  );
}

//REQUEST HANDLERS FOR PROJECT
export function projectFromParamsHandler(context: ExecutionContext): number {
  const request = context.switchToHttp().getRequest();
  if (request?.params?.projectId) {
    return Number.parseInt(request.params.projectId);
  }
  throw new BadRequestException(
    'Project Id not provided. Check your request or permission decorator.',
  );
}

export function projectFromQueryHandler(context: ExecutionContext): number {
  const request = context.switchToHttp().getRequest();
  if (request?.query?.projectId) {
    return Number.parseInt(request.query.projectId);
  }
  throw new BadRequestException(
    'Project Id not provided. Check your request or permission decorator.',
  );
}

//REQUEST HANDLERS FOR ORGANIZATION
export function organizationFromParamsHandler(
  context: ExecutionContext,
): string {
  const request = context.switchToHttp().getRequest();
  if (request?.params?.organizationId) {
    return request.params.organizationId;
  }
  throw new BadRequestException(
    'Organization Id not provided. Check your request or permission decorator.',
  );
}

export function organizationFromQueryHandler(
  context: ExecutionContext,
): string {
  const request = context.switchToHttp().getRequest();
  if (request?.query?.organizationId) {
    return request.query.organizationId;
  }
  throw new BadRequestException(
    'Organization Id not provided. Check your request or permission decorator.',
  );
}

export function organizationFromBodyHandler(context: ExecutionContext): string {
  const request = context.switchToHttp().getRequest();
  if (request?.body?.organizationId) {
    return request.body.organizationId;
  }
  throw new BadRequestException(
    'Organization Id not provided. Check your request or permission decorator.',
  );
}
