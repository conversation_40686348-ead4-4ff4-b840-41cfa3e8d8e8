## Please refer to https://vidmob.atlassian.net/wiki/spaces/ATLAS/pages/3724509193/Setting+up+CI+CD for default CI/CD variable overrides
include:
  - project: 'vidmob/repos/recently-migrated/vidmob-operations'
    ref: $OPS_REPO_VERSION
    file: '/gitlab/ci/pipelines/gitflow/soa.nest.docker.ecs.pipeline.yml'

variables:
  JIRA_PROJECT_ID: VID
  JIRA_SUB_PROJECT_ID: PT-VAB
  ENABLE_PLAYWRIGHT_API_TESTS: 'true'
  PLAYWRIGHT_TEST_TAGS_API_CUSTOM: '@plugin_api_bff'

playwright:api:custom:dev:
  tags:
    - vidmob-eks-runner-privileged
  variables:
    PLUGIN_EMAIL: '$QA_PLAYWRIGHT_PLUGIN_EMAIL_DEV'
    PLUGIN_PASSWORD: '$QA_PLAYWRIGHT_PLUGIN_PASSWORD_DEV'

playwright:api:custom:stage:
  tags:
    - vidmob-eks-runner-privileged
  variables:
    PLUGIN_EMAIL: '$QA_PLAYWRIGHT_PLUGIN_EMAIL_STAGE'
    PLUGIN_PASSWORD: '$QA_PLAYWRIGHT_PLUGIN_PASSWORD_STAGE'

playwright:api:custom:prod:
  tags:
    - vidmob-eks-runner-privileged
  variables:
    PLUGIN_EMAIL: '$QA_PLAYWRIGHT_PLUGIN_EMAIL_PROD'
    PLUGIN_PASSWORD: '$QA_PLAYWRIGHT_PLUGIN_PASSWORD_PROD'

deploy:dev:
  variables:
    AWS_CLI_IAM_ROLE_ARN: 'arn:aws:iam::812471470063:role/gitlabUser'
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-api-bff,vidmob-api-bff,ecs/vidmob-api-bff-dev.json'

deploy:stage:
  variables:
    AWS_CLI_IAM_ROLE_ARN: 'arn:aws:iam::840000716985:role/gitlabUser'
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-api-bff,vidmob-api-bff,ecs/vidmob-api-bff-stage.json'

deploy:prod:
  variables:
    AWS_CLI_IAM_ROLE_ARN: 'arn:aws:iam::560638139269:role/gitlabUser'
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-api-bff,vidmob-api-bff,ecs/vidmob-api-bff-prod.json'
