{"name": "vidmob-api-bff", "version": "0.0.0-SNAPSHOT", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@automapper/classes": "8.7.7", "@automapper/core": "8.7.7", "@automapper/nestjs": "8.7.7", "@automapper/types": "6.3.1", "@nestjs/common": "9.4.2", "@nestjs/config": "2.3.2", "@nestjs/core": "9.4.2", "@nestjs/platform-express": "9.4.2", "@nestjs/swagger": "^7.1.16", "@nestjs/throttler": "5.1.2", "@nestjs/typeorm": "9.0.1", "@vidmob/vidmob-authorization-service-sdk": "0.0.0-SNAPSHOT-dc3e9f4e-20250210173836", "@vidmob/vidmob-media-conversion-service-sdk": "0.0.0-SNAPSHOT-2ab42e08-20231003192528", "@vidmob/vidmob-nestjs-common": "1.0.71", "@vidmob/vidmob-organization-service-sdk": "0.0.0-SNAPSHOT-8e571278-20250620184045", "@vidmob/vidmob-soa-analytics-service-sdk": "0.0.0-SNAPSHOT-b0022f4a-20250422201147", "@vidmob/vidmob-soa-media-annotation-service-sdk": "0.0.0-SNAPSHOT-18e3bd73-20250210191444", "@vidmob/vidmob-soa-scoring-service-sdk": "0.0.0-SNAPSHOT-cd1d6bfa-20250618181100", "@vidmob/vidmob-studio-service-sdk": "0.0.0-SNAPSHOT-bf2a51cc-20241223131823", "axios": "1.6.4", "class-transformer": "0.5.1", "class-validator": "0.14.0", "date-fns": "^3.3.1", "ioredis": "^5.4.1", "mysql2": "3.12.0", "nestjs-throttler-storage-redis": "0.4.1", "redis": "4.6.13", "reflect-metadata": "0.1.13", "rxjs": "7.8.1", "typeorm": "0.3.17"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "29.5.1", "@types/multer": "1.4.11", "@types/node": "18.16.12", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.1.0", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}, "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}