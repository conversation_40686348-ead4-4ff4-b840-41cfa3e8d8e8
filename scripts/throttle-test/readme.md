# Load Testing with k6

This repository contains a script for throttle testing using k6. The script sends 11 GET requests in the span of one minute to a specified endpoint with an authorization header and checks for successful responses and throttle limit exceeded responses.

## Prerequisites

- [k6](https://k6.io/docs/getting-started/installation/) installed on your machine.
- A valid authorization token.

## Test Script

The script is saved as `throttle-test.js`.

## Instructions

1. **Replace Placeholder Values**: Update the `Authorization` header in the script with your actual token and the `url` with your target endpoint.

2. **Save the Script**: Ensure the script is saved as `throttle-test.js` in your repository.

3. **Start Both The Authorization and Organization Services**: In 2 other terminals, have both the Authorization and Organization Services running 

4. **Run the Test**:
   Open a terminal and navigate to the directory where `throttle-test.js` is located. Run the following command:
   ```sh
   k6 run throttle-test.js
## Understanding the Test Script
- **Headers**: The params object contains the headers required for the requests, including the authorization token.
- **Virtual Users (vus)**: The options object sets the number of virtual users (1 in this case).
- **Iterations**: Each virtual user will perform 11 iterations (requests) in this script.
- **Request Loop**: The default function loops the number of iterations specified in the script, sending a GET request to the specified URL and checking the response status.
- **Sleep**: The script includes a sleep statement to wait for 100 milliseconds between requests.
