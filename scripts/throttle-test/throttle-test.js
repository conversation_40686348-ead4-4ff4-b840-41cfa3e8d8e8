import http from 'k6/http';
import { sleep } from 'k6';
import { Counter } from 'k6/metrics';

const params = {
  headers: {
    Authorization: 'Bearer <api-key>', // Replace with your actual token
  },
};

export const options = {
  vus: 1, // number of virtual users
  duration: '1m', // duration of the test
};
const request200 = new Counter('status_200');
const request429 = new Counter('status_429');

export default function () {
  const url = 'http://localhost:3000/v1/organization'; // Replace with your actual endpoint
  const requestsPerVU = 11;
  const interval = 60 / requestsPerVU; // 60 seconds divided by number of requests
  for (let i = 0; i < requestsPerVU; i++) {
    const res = http.get(url, params);
    if (res.status === 200) {
      request200.add(1);
    } else if (res.status === 429) {
      request429.add(1);
    }
    // Wait for the calculated interval before sending the next request
    sleep(interval);
  }
}

export function handleSummary(data) {
  console.log(`\nRequest Summary:\n`);
  console.log(
    `✓ status is 200: ${data.metrics.status_200.values.count} requests`,
  );
  console.log(
    `✓ status is 429: ${data.metrics.status_429.values.count} requests`,
  );
  console.log(
    `\nTotal requests: ${
      data.metrics.status_200.values.count +
      data.metrics.status_429.values.count
    }\n`,
  );
}
