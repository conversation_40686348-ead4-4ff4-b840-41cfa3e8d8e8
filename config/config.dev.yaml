NODE_ENV: dev
AWS_REGION: us-east-1
THROTTLE_TTL_MILLISECONDS: 60000 # 60 sec(1000 milliseconds/1 sec):
THROTTLE_LIMIT: 100
KINESIS_ENABLED: true
baseVidMobApiUrl: 'https://api-public-dev.vidmob.com/VidMob'
baseUrlSubmitBatch: 'https://api-public-dev.vidmob.com/as-gw'
baseUrlApiInsight: 'https://api-public-dev.vidmob.com'

clientId: '1945207187'
clientSecret: 'sclPcmeieBrcMXwutzdJKkidz7d6zNUIVWnzPld0Z+E='

database:
  default:
    secret: dev/mysql/soa-rw

kinesis:
  streamName: 'api-bff-kinesis-firehose-stream-dev'
  streamArn: 'arn:aws:kinesis:us-east-1:812471470063:stream/api-bff-kinesis-firehose-stream-dev'


cache:
  redisHost: vidmob-elasticache-redis-dev-lhxds3.serverless.use1.cache.amazonaws.com
  tls: true