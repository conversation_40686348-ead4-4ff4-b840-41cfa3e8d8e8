NODE_ENV: prod
AWS_REGION: us-east-1
THROTTLE_TTL_MILLISECONDS: 60000 # 60 sec(1000 milliseconds/1 sec)
THROTTLE_LIMIT: 100
KINESIS_ENABLED: true
baseVidMobApiUrl: 'https://api-public.vidmob.com/VidMob'
baseUrlSubmitBatch: 'https://api-public.vidmob.com/as-gw'
baseUrlApiInsight: 'https://api-public.vidmob.com'

cache:
  redisHost: vidmob-elasticache-redis-prod-9doibo.serverless.use1.cache.amazonaws.com
  tls: true


database:
  default:
    secret: prod/mysql/soa-rw

kinesis:
  streamName: 'api-bff-kinesis-firehose-stream-prod'
  streamArn: 'arn:aws:kinesis:us-east-1:560638139269:stream/api-bff-kinesis-firehose-stream-prod'
