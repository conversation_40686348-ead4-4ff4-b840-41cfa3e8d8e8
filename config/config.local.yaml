NODE_ENV: local
AWS_REGION: us-east-1
THROTTLE_TTL_MILLISECONDS: 60000 # 60 sec(1000 milliseconds/1 sec)
THROTTLE_LIMIT: 10
authorizationService:
  # basePath: 'http://127.0.0.1:3001'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-authorization-service'
analyticsService:
  # basePath: 'http://127.0.0.1:3002'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-analytics-service'
organizationService:
  # basePath: 'http://127.0.0.1:3003'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-organization-service'
scoringService:
  # basePath: 'http://127.0.0.1:3004'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-scoring-service'
mediaConversionService:
  # basePath: 'http://127.0.0.1:3005'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-media-conversion-service'
notificationService:
  # basePath: 'http://127.0.0.1:3006'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-notification-service'
mediaAnnotationService:
  basePath: 'https://soa-internal-dev.vidmob.com'
  # basePath: 'http://127.0.0.1:3007'  # Local
  defaultHeaders:
    vidmob-nestjs-service: 'media-annotation-service'
studioService:
  # basePath: 'http://127.0.0.1:3007'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-studio-service'
dashboardService:
  # basePath: 'http://127.0.0.1:3008'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-dashboard-service'

legacyAnalyticsServiceUrl: 'https://api-analytics-dev.vidmob.com'
legacyApiGwUrl: 'https://api-analytics-gw-dev.vidmob.com'

platformIntegrationUrl: 'https://api-public-dev.vidmob.com/integrations'
baseVidMobApiUrl: 'https://api-public-dev.vidmob.com/VidMob'
baseUrlSubmitBatch: 'https://api-public-dev.vidmob.com/as-gw'
baseUrlApiInsight: 'https://api-public-dev.vidmob.com'

clientId: '1945207187'
clientSecret: 'sclPcmeieBrcMXwutzdJKkidz7d6zNUIVWnzPld0Z+E='

database:
  default:
    value:
      host: 127.0.0.1
      port: 13326
      database: vidmob
      ssl: false
