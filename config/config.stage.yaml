NODE_ENV: stage
AWS_REGION: us-east-1
THROTTLE_TTL_MILLISECONDS: 60000 # 60 sec(1000 milliseconds/1 sec)
THROTTLE_LIMIT: 100
KINESIS_ENABLED: true
baseVidMobApiUrl: 'https://api-public-stage.vidmob.com/VidMob'
baseUrlSubmitBatch: 'https://api-public-stage.vidmob.com/as-gw'
baseUrlApiInsight: 'https://api-public-stage.vidmob.com'

cache:
  redisHost: vidmob-elasticache-redis-stage-vc9l4m.serverless.use1.cache.amazonaws.com
  tls: true

database:
  default:
    secret: stage/mysql/soa-rw

kinesis:
  streamName: 'api-bff-kinesis-firehose-stream-stage'
  streamArn: 'arn:aws:kinesis:us-east-1:840000716985:stream/api-bff-kinesis-firehose-stream-stage'
